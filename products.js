/**
 * إدارة المنتجات - تكنوفلاش
 */

// مستمع أحداث تحديث الإعدادات
window.addEventListener('settingsUpdated', function(event) {
    const settings = event.detail;
    console.log('تم استلام تحديث الإعدادات في المنتجات:', settings);

    // تحديث الوضع الليلي
    if (settings.general && typeof settings.general.darkMode !== 'undefined') {
        if (settings.general.darkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        console.log('تم تحديث الوضع الليلي في المنتجات:', settings.general.darkMode);
    }

    // تحديث العملة في واجهة المنتجات
    if (settings.general && settings.general.currency) {
        updateProductsCurrency(settings.general.currency);
    }

    // تحديث نوع الأرقام
    if (settings.general && settings.general.numberType) {
        updateProductsNumbers(settings.general.numberType);
    }
});

let currentProduct = null;
let productsData = [];

/**
 * تحميل صفحة المنتجات
 */
async function loadProducts() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-box"></i> إدارة المنتجات</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddProductModal()">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
                <button class="btn btn-success" onclick="showBarcodeManager()">
                    <i class="fas fa-barcode"></i> إدارة الباركود
                </button>
                <button class="btn btn-info" onclick="exportProducts()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="btn btn-warning" onclick="showImportModal()">
                    <i class="fas fa-upload"></i> استيراد
                </button>
            </div>
        </div>

        <!-- شريط البحث والفلاتر -->
        <div class="filters-section">
            <div class="card">
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <div class="search-bar">
                                <input type="text" class="search-input" id="productSearchInput" 
                                       placeholder="البحث بالاسم أو الباركود...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="categoryFilter">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="stockFilter">
                                <option value="">جميع المنتجات</option>
                                <option value="in-stock">متوفر</option>
                                <option value="low-stock">مخزون منخفض</option>
                                <option value="out-of-stock">نفد المخزون</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <div class="card">
            <div class="card-body">
                <!-- عرض الجدول المتجاوب -->
                <div class="products-table-container">
                    <!-- عرض الجدول للشاشات الكبيرة -->
                    <div class="table-responsive desktop-view">
                        <table class="table table-hover products-table" id="productsTable">
                            <thead>
                                <tr>
                                    <th class="col-image">الصورة</th>
                                    <th class="col-name">الاسم</th>
                                    <th class="col-barcode">الباركود</th>
                                    <th class="col-category">الفئة</th>
                                    <th class="col-type">النوع</th>
                                    <th class="col-price">السعر</th>
                                    <th class="col-stock">المخزون</th>
                                    <th class="col-status">الحالة</th>
                                    <th class="col-actions">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>

                    <!-- عرض الجدول المبسط للشاشات المتوسطة -->
                    <div class="table-responsive medium-view">
                        <table class="table table-hover products-table-medium" id="productsTableMedium">
                            <thead>
                                <tr>
                                    <th class="col-product">المنتج</th>
                                    <th class="col-price">السعر</th>
                                    <th class="col-stock">المخزون</th>
                                    <th class="col-actions">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>

                    <!-- عرض البطاقات للشاشات الصغيرة -->
                    <div class="products-cards-container mobile-view" id="productsCards">
                        <!-- سيتم تحميل البطاقات هنا -->
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل منتج -->
        <div id="productModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="productModalTitle">إضافة منتج جديد</h3>
                    <button class="modal-close" onclick="app.hideModal('productModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اسم المنتج *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="form-group">
                                <label>الباركود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="barcode" id="barcodeInput">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-secondary" onclick="generateProductBarcode()">
                                            <i class="fas fa-random"></i> توليد
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="previewBarcode()">
                                            <i class="fas fa-eye"></i> معاينة
                                        </button>
                                    </div>
                                </div>
                                <div id="barcodePreview" class="barcode-preview mt-2" style="display: none;"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الفئة</label>
                                <select class="form-control" name="category">
                                    <option value="">اختر الفئة</option>
                                    <option value="electronics">إلكترونيات</option>
                                    <option value="clothing">ملابس</option>
                                    <option value="food">أغذية</option>
                                    <option value="books">كتب</option>
                                    <option value="home">منزلية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>الوحدة</label>
                                <select class="form-control" name="unit">
                                    <option value="piece">قطعة</option>
                                    <option value="kg">كيلوجرام</option>
                                    <option value="liter">لتر</option>
                                    <option value="meter">متر</option>
                                    <option value="box">صندوق</option>
                                </select>
                            </div>
                        </div>

                        <!-- خيار نوع المنتج -->
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="isCartonBased" name="isCartonBased">
                                <label class="form-check-label" for="isCartonBased">
                                    <i class="fas fa-boxes"></i> منتج مخزّن بالكرتون
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                فعّل هذا الخيار إذا كان المنتج يُباع بالقطعة من داخل كراتين
                            </small>
                        </div>

                        <!-- حقول المنتج العادي -->
                        <div id="regularProductFields">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>سعر الشراء *</label>
                                    <input type="number" class="form-control" name="costPrice"
                                           step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>سعر البيع *</label>
                                    <input type="number" class="form-control" name="price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>الكمية الحالية *</label>
                                    <input type="number" class="form-control" name="quantity"
                                           min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>الحد الأدنى للمخزون</label>
                                    <input type="number" class="form-control" name="minStock"
                                           min="0" value="5">
                                </div>
                            </div>
                        </div>

                        <!-- حقول المنتج المبني على الكرتون -->
                        <div id="cartonBasedFields" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>نظام الكرتون:</strong> يمكنك بيع المنتج كرتونة كاملة أو قطع مفردة
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>عدد القطع في الكرتونة *</label>
                                    <input type="number" class="form-control" name="unitsPerCarton"
                                           min="1" value="1">
                                </div>
                                <div class="form-group">
                                    <label>سعر شراء الكرتونة *</label>
                                    <input type="number" class="form-control" name="cartonCostPrice"
                                           step="0.01" min="0">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>سعر بيع القطعة الواحدة *</label>
                                    <input type="number" class="form-control" name="unitSalePrice"
                                           step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label>سعر بيع الكرتونة الكاملة</label>
                                    <input type="number" class="form-control" name="cartonSalePrice"
                                           step="0.01" min="0" readonly>
                                    <small class="form-text text-muted">يُحسب تلقائياً</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>عدد الكراتين المتوفرة</label>
                                    <input type="number" class="form-control" name="cartonQuantity"
                                           min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label>عدد القطع المفردة</label>
                                    <input type="number" class="form-control" name="unitQuantity"
                                           min="0" value="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>إجمالي القطع المتاحة</label>
                                <input type="number" class="form-control" name="totalUnitsAvailable"
                                       readonly>
                                <small class="form-text text-muted">يُحسب تلقائياً من الكراتين والقطع المفردة</small>
                            </div>

                            <div class="form-group">
                                <label>الحد الأدنى للمخزون (بالقطع)</label>
                                <input type="number" class="form-control" name="minStock"
                                       min="0" value="5">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف المنتج..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>صورة المنتج</label>
                            <input type="file" class="form-control" name="image"
                                   accept="image/*">
                            <div id="imagePreview" class="image-preview"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('productModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة الاستيراد -->
        <div id="importModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>استيراد المنتجات</h3>
                    <button class="modal-close" onclick="app.hideModal('importModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="import-section">
                        <p>يمكنك استيراد المنتجات من ملف JSON:</p>
                        <input type="file" id="importFile" accept=".json" class="form-control">
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="importProducts()">
                                <i class="fas fa-upload"></i> استيراد
                            </button>
                            <button class="btn btn-info" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i> تحميل نموذج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إدارة الباركود -->
        <div id="barcodeManagerModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>إدارة الباركود</h3>
                    <button class="modal-close" onclick="app.hideModal('barcodeManagerModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="barcode-manager-tabs">
                        <button class="tab-btn active" onclick="switchBarcodeTab('generate')">
                            <i class="fas fa-plus"></i> توليد باركود
                        </button>
                        <button class="tab-btn" onclick="switchBarcodeTab('bulk')">
                            <i class="fas fa-layer-group"></i> توليد مجمع
                        </button>
                        <button class="tab-btn" onclick="switchBarcodeTab('print')">
                            <i class="fas fa-print"></i> طباعة الباركود
                        </button>
                    </div>

                    <!-- تبويب توليد باركود فردي -->
                    <div id="generateTab" class="tab-content active">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اختر المنتج</label>
                                <select class="form-control" id="productSelectBarcode">
                                    <option value="">اختر منتج...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>نوع الباركود</label>
                                <select class="form-control" id="barcodeType">
                                    <option value="CODE128">Code 128</option>
                                    <option value="EAN13">EAN-13</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="generateSingleBarcode()">
                                <i class="fas fa-barcode"></i> توليد الباركود
                            </button>
                        </div>
                        <div id="singleBarcodeResult" class="barcode-result"></div>
                    </div>

                    <!-- تبويب توليد مجمع -->
                    <div id="bulkTab" class="tab-content">
                        <div class="bulk-options">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="selectAllProducts"> تحديد جميع المنتجات
                                </label>
                            </div>
                            <div class="form-group">
                                <label>نوع الباركود</label>
                                <select class="form-control" id="bulkBarcodeType">
                                    <option value="CODE128">Code 128</option>
                                    <option value="EAN13">EAN-13</option>
                                </select>
                            </div>
                        </div>
                        <div class="products-selection" id="productsSelection">
                            <!-- قائمة المنتجات للاختيار -->
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="generateBulkBarcodes()">
                                <i class="fas fa-layer-group"></i> توليد الباركود للمنتجات المحددة
                            </button>
                        </div>
                        <div id="bulkBarcodeResult" class="barcode-result"></div>
                    </div>

                    <!-- تبويب طباعة -->
                    <div id="printTab" class="tab-content">
                        <div class="print-options">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>حجم الورق</label>
                                    <select class="form-control" id="paperSize">
                                        <option value="A4">A4</option>
                                        <option value="A5">A5</option>
                                        <option value="label">ملصقات</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>عدد الأعمدة</label>
                                    <select class="form-control" id="printColumns">
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="includePrices" checked> تضمين الأسعار
                                </label>
                            </div>
                        </div>
                        <div id="printPreview" class="print-preview"></div>
                        <div class="form-actions">
                            <button class="btn btn-info" onclick="previewPrintBarcodes()">
                                <i class="fas fa-eye"></i> معاينة الطباعة
                            </button>
                            <button class="btn btn-success" onclick="printBarcodes()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إدارة الباركود -->
        <div id="barcodeManagerModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>إدارة الباركود</h3>
                    <button class="modal-close" onclick="app.hideModal('barcodeManagerModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="barcode-manager-tabs">
                        <button class="tab-btn active" onclick="switchBarcodeTab('generate')">
                            <i class="fas fa-plus"></i> توليد باركود
                        </button>
                        <button class="tab-btn" onclick="switchBarcodeTab('bulk')">
                            <i class="fas fa-layer-group"></i> توليد مجمع
                        </button>
                        <button class="tab-btn" onclick="switchBarcodeTab('print')">
                            <i class="fas fa-print"></i> طباعة الباركود
                        </button>
                    </div>

                    <!-- تبويب توليد باركود فردي -->
                    <div id="generateTab" class="tab-content active">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اختر المنتج</label>
                                <select class="form-control" id="productSelectBarcode">
                                    <option value="">اختر منتج...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>نوع الباركود</label>
                                <select class="form-control" id="barcodeType">
                                    <option value="CODE128">Code 128</option>
                                    <option value="EAN13">EAN-13</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="generateSingleBarcode()">
                                <i class="fas fa-barcode"></i> توليد الباركود
                            </button>
                        </div>
                        <div id="singleBarcodeResult" class="barcode-result"></div>
                    </div>

                    <!-- تبويب توليد مجمع -->
                    <div id="bulkTab" class="tab-content">
                        <div class="bulk-options">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="selectAllProducts"> تحديد جميع المنتجات
                                </label>
                            </div>
                            <div class="form-group">
                                <label>نوع الباركود</label>
                                <select class="form-control" id="bulkBarcodeType">
                                    <option value="CODE128">Code 128</option>
                                    <option value="EAN13">EAN-13</option>
                                </select>
                            </div>
                        </div>
                        <div class="products-selection" id="productsSelection">
                            <!-- قائمة المنتجات للاختيار -->
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="generateBulkBarcodes()">
                                <i class="fas fa-layer-group"></i> توليد الباركود للمنتجات المحددة
                            </button>
                        </div>
                        <div id="bulkBarcodeResult" class="barcode-result"></div>
                    </div>

                    <!-- تبويب طباعة -->
                    <div id="printTab" class="tab-content">
                        <div class="print-options">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>حجم الورق</label>
                                    <select class="form-control" id="paperSize">
                                        <option value="A4">A4</option>
                                        <option value="A5">A5</option>
                                        <option value="label">ملصقات</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>عدد الأعمدة</label>
                                    <select class="form-control" id="printColumns">
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="includePrices" checked> تضمين الأسعار
                                </label>
                            </div>
                        </div>
                        <div id="printPreview" class="print-preview"></div>
                        <div class="form-actions">
                            <button class="btn btn-info" onclick="previewPrintBarcodes()">
                                <i class="fas fa-eye"></i> معاينة الطباعة
                            </button>
                            <button class="btn btn-success" onclick="printBarcodes()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeProductsPage();
}

/**
 * تهيئة صفحة المنتجات
 */
async function initializeProductsPage() {
    // تحميل البيانات
    loadProductsData();

    // تحميل الفئات
    loadCategoryFilters();

    // عرض المنتجات
    displayProducts();

    // إعداد مستمعي الأحداث بعد تأخير صغير لضمان تحميل DOM
    setTimeout(() => {
        setupProductsEventListeners();
    }, 100);
}

/**
 * تحميل بيانات المنتجات
 */
function loadProductsData() {
    productsData = db.getProducts();
}

/**
 * تحميل فلاتر الفئات
 */
function loadCategoryFilters() {
    const categories = [...new Set(productsData.map(p => p.category).filter(c => c))];
    const categoryFilter = document.getElementById('categoryFilter');
    
    // مسح الخيارات الحالية (عدا الخيار الأول)
    categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';
    
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = getCategoryName(category);
        categoryFilter.appendChild(option);
    });
}

/**
 * إعداد مستمعي الأحداث
 */
function setupProductsEventListeners() {
    // البحث
    const searchInput = document.getElementById('productSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', filterProducts);
    }

    // الفلاتر
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterProducts);
    }

    const stockFilter = document.getElementById('stockFilter');
    if (stockFilter) {
        stockFilter.addEventListener('change', filterProducts);
    }

    // نموذج المنتج
    const productForm = document.getElementById('productForm');
    if (productForm) {
        productForm.addEventListener('submit', handleProductSubmit);
    }

    // معاينة الصورة
    const imageInput = document.querySelector('input[name="image"]');
    if (imageInput) {
        imageInput.addEventListener('change', (e) => previewImage(e.target));
    }

    // تبديل نوع المنتج (عادي/كرتون)
    const isCartonBasedCheckbox = document.getElementById('isCartonBased');
    if (isCartonBasedCheckbox) {
        isCartonBasedCheckbox.addEventListener('change', toggleProductType);
    }

    // حساب الأسعار والكميات للمنتجات المبنية على الكرتون
    const cartonFields = ['unitsPerCarton', 'cartonCostPrice', 'unitSalePrice', 'cartonQuantity', 'unitQuantity'];
    cartonFields.forEach(fieldName => {
        const field = document.querySelector(`input[name="${fieldName}"]`);
        if (field) {
            field.addEventListener('input', calculateCartonValues);
        }
    });

    // تحديد جميع المنتجات في إدارة الباركود
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAllProducts);
    }
}

/**
 * عرض المنتجات
 */
function displayProducts(products = productsData) {
    // عرض الجدول الكامل للشاشات الكبيرة
    displayProductsLarge(products);

    // عرض الجدول المبسط للشاشات المتوسطة
    displayProductsMedium(products);

    // عرض البطاقات للشاشات الصغيرة
    displayProductsCards(products);
}

/**
 * عرض المنتجات للشاشات الكبيرة
 */
function displayProductsLarge(products) {
    const tbody = document.querySelector('#productsTable tbody');
    if (!tbody) return;

    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">لا توجد منتجات</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}">` : 
                        '<i class="fas fa-image"></i>'
                    }
                </div>
            </td>
            <td>
                <div class="product-name-info">
                    <strong>${product.name}</strong>
                    ${product.isCartonBased ?
                        `<br><small class="text-info"><i class="fas fa-boxes"></i> ${db.toArabicNumerals(product.unitsPerCarton || 1)} قطعة/كرتونة</small>` :
                        ''
                    }
                    ${product.description ? `<br><small class="text-muted">${product.description}</small>` : ''}
                </div>
            </td>
            <td>${product.barcode || '-'}</td>
            <td>${getCategoryName(product.category)}</td>
            <td>
                ${product.isCartonBased ?
                    `<span class="badge badge-primary"><i class="fas fa-boxes"></i> كرتون</span>` :
                    `<span class="badge badge-secondary"><i class="fas fa-box"></i> عادي</span>`
                }
            </td>
            <td>
                ${product.isCartonBased ?
                    `<div class="price-info">
                        <div class="price-main"><strong>القطعة:</strong> ${db.formatCurrency(product.unitSalePrice || 0)}</div>
                        <div class="price-secondary"><small>الكرتونة (${db.toArabicNumerals(product.unitsPerCarton || 1)} قطعة):</small></div>
                        <div class="price-carton"><strong>${db.formatCurrency((product.unitSalePrice || 0) * (product.unitsPerCarton || 1))}</strong></div>
                        ${product.cartonCostPrice ?
                            `<div class="price-profit"><small>ربح الكرتونة: ${db.formatCurrency(((product.unitSalePrice || 0) * (product.unitsPerCarton || 1)) - (product.cartonCostPrice || 0))}</small></div>` :
                            ''
                        }
                    </div>` :
                    `<div class="price-info">
                        <div class="price-main">${db.formatCurrency(product.price)}</div>
                        ${product.costPrice ?
                            `<div class="price-profit"><small>ربح: ${db.formatCurrency(product.price - product.costPrice)}</small></div>` :
                            ''
                        }
                    </div>`
                }
            </td>
            <td>
                ${product.isCartonBased ?
                    `<div class="stock-info carton-stock">
                        <div class="stock-summary">
                            <div class="stock-main">
                                <i class="fas fa-boxes text-primary"></i>
                                <strong>${db.toArabicNumerals(product.cartonQuantity || 0)}</strong> كرتونة
                            </div>
                            <div class="stock-units">
                                <i class="fas fa-cube text-secondary"></i>
                                <span>${db.toArabicNumerals(product.unitQuantity || 0)}</span> قطعة مفردة
                            </div>
                        </div>
                        <div class="stock-total">
                            <div class="total-available">
                                <span class="total-label">إجمالي متاح:</span>
                                <span class="quantity-badge total ${getStockClass(product)}">
                                    ${db.toArabicNumerals(product.totalUnitsAvailable || 0)} قطعة
                                </span>
                            </div>
                            ${(product.totalUnitsAvailable || 0) > 0 ?
                                `<div class="stock-breakdown">
                                    <small class="text-muted">
                                        (${db.toArabicNumerals(Math.floor((product.totalUnitsAvailable || 0) / (product.unitsPerCarton || 1)))} كرتونة كاملة +
                                        ${db.toArabicNumerals((product.totalUnitsAvailable || 0) % (product.unitsPerCarton || 1))} قطعة)
                                    </small>
                                </div>` :
                                ''
                            }
                        </div>
                    </div>` :
                    `<div class="stock-info regular-stock">
                        <span class="quantity-badge main ${getStockClass(product)}">
                            ${db.toArabicNumerals(product.quantity)} ${getUnitName(product.unit)}
                        </span>
                    </div>`
                }
            </td>
            <td>
                <span class="badge badge-${getStockBadgeClass(product)}">
                    ${getStockStatus(product)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="editProduct('${product.id}')" 
                            title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adjustStock('${product.id}')"
                            title="تعديل المخزون">
                        <i class="fas fa-boxes"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printProductBarcode('${product.id}')"
                            title="طباعة الباركود">
                        <i class="fas fa-barcode"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')"
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * عرض المنتجات للشاشات المتوسطة
 */
function displayProductsMedium(products) {
    const tbody = document.querySelector('#productsTableMedium tbody');
    if (!tbody) return;

    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">لا توجد منتجات</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <div class="product-summary">
                    <div class="product-main-info">
                        <div class="product-image-small">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.name}">` :
                                '<i class="fas fa-image"></i>'
                            }
                        </div>
                        <div class="product-details">
                            <strong>${product.name}</strong>
                            ${product.barcode ? `<br><small class="text-muted">الباركود: ${product.barcode}</small>` : ''}
                            ${product.isCartonBased ?
                                `<br><small class="text-info"><i class="fas fa-boxes"></i> ${db.toArabicNumerals(product.unitsPerCarton || 1)} قطعة/كرتونة</small>` :
                                ''
                            }
                        </div>
                    </div>
                </div>
            </td>
            <td>
                ${product.isCartonBased ?
                    `<div class="price-compact">
                        <div><strong>القطعة:</strong> ${db.formatCurrency(product.unitSalePrice || 0)}</div>
                        <div><small>الكرتونة: ${db.formatCurrency((product.unitSalePrice || 0) * (product.unitsPerCarton || 1))}</small></div>
                    </div>` :
                    `<div class="price-compact">
                        <strong>${db.formatCurrency(product.price)}</strong>
                    </div>`
                }
            </td>
            <td>
                ${product.isCartonBased ?
                    `<div class="stock-compact">
                        <div class="stock-total">
                            <span class="quantity-badge ${getStockClass(product)}">
                                ${db.toArabicNumerals(product.totalUnitsAvailable || 0)} قطعة
                            </span>
                        </div>
                        <div class="stock-breakdown">
                            <small>${db.toArabicNumerals(product.cartonQuantity || 0)} كرتونة + ${db.toArabicNumerals(product.unitQuantity || 0)} قطعة</small>
                        </div>
                    </div>` :
                    `<div class="stock-compact">
                        <span class="quantity-badge ${getStockClass(product)}">
                            ${db.toArabicNumerals(product.quantity)} ${getUnitName(product.unit)}
                        </span>
                    </div>`
                }
            </td>
            <td>
                <div class="action-buttons-compact">
                    <button class="btn btn-sm btn-info" onclick="editProduct('${product.id}')"
                            title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adjustStock('${product.id}')"
                            title="تعديل المخزون">
                        <i class="fas fa-boxes"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')"
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * عرض المنتجات كبطاقات للشاشات الصغيرة
 */
function displayProductsCards(products) {
    const container = document.getElementById('productsCards');
    if (!container) return;

    if (products.length === 0) {
        container.innerHTML = `
            <div class="text-center" style="padding: 2rem;">
                <i class="fas fa-box" style="font-size: 3rem; color: var(--text-muted); margin-bottom: 1rem;"></i>
                <p>لا توجد منتجات</p>
            </div>
        `;
        return;
    }

    container.innerHTML = products.map(product => `
        <div class="product-card">
            <div class="product-card-header">
                <div class="product-card-image">
                    ${product.image ?
                        `<img src="${product.image}" alt="${product.name}">` :
                        '<i class="fas fa-image"></i>'
                    }
                </div>
                <div class="product-card-info">
                    <div class="product-card-name">${product.name}</div>
                    ${product.barcode ? `<small class="text-muted">الباركود: ${product.barcode}</small>` : ''}
                    ${product.isCartonBased ?
                        `<br><small class="text-info"><i class="fas fa-boxes"></i> ${db.toArabicNumerals(product.unitsPerCarton || 1)} قطعة/كرتونة</small>` :
                        ''
                    }
                </div>
            </div>

            <div class="product-card-details">
                <div class="product-card-detail">
                    <span>الفئة:</span>
                    <span>${getCategoryName(product.category)}</span>
                </div>
                <div class="product-card-detail">
                    <span>النوع:</span>
                    <span>
                        ${product.isCartonBased ?
                            `<span class="badge badge-primary"><i class="fas fa-boxes"></i> كرتون</span>` :
                            `<span class="badge badge-secondary"><i class="fas fa-box"></i> عادي</span>`
                        }
                    </span>
                </div>
                <div class="product-card-detail">
                    <span>السعر:</span>
                    <span>
                        ${product.isCartonBased ?
                            `<div>
                                <div><strong>القطعة:</strong> ${db.formatCurrency(product.unitSalePrice || 0)}</div>
                                <div><small>الكرتونة: ${db.formatCurrency((product.unitSalePrice || 0) * (product.unitsPerCarton || 1))}</small></div>
                            </div>` :
                            `<strong>${db.formatCurrency(product.price)}</strong>`
                        }
                    </span>
                </div>
                <div class="product-card-detail">
                    <span>المخزون:</span>
                    <span>
                        ${product.isCartonBased ?
                            `<div>
                                <div class="quantity-badge ${getStockClass(product)}">
                                    ${db.toArabicNumerals(product.totalUnitsAvailable || 0)} قطعة
                                </div>
                                <small>${db.toArabicNumerals(product.cartonQuantity || 0)} كرتونة + ${db.toArabicNumerals(product.unitQuantity || 0)} قطعة</small>
                            </div>` :
                            `<span class="quantity-badge ${getStockClass(product)}">
                                ${db.toArabicNumerals(product.quantity)} ${getUnitName(product.unit)}
                            </span>`
                        }
                    </span>
                </div>
                <div class="product-card-detail">
                    <span>الحالة:</span>
                    <span>
                        <span class="badge badge-${getStockBadgeClass(product)}">
                            ${getStockStatus(product)}
                        </span>
                    </span>
                </div>
            </div>

            <div class="product-card-actions">
                <button class="btn btn-sm btn-info" onclick="editProduct('${product.id}')"
                        title="تعديل">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-warning" onclick="adjustStock('${product.id}')"
                        title="تعديل المخزون">
                    <i class="fas fa-boxes"></i> المخزون
                </button>
                <button class="btn btn-sm btn-success" onclick="printProductBarcode('${product.id}')"
                        title="طباعة الباركود">
                    <i class="fas fa-barcode"></i> باركود
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')"
                        title="حذف">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * الحصول على فئة CSS للمخزون
 */
function getStockClass(product) {
    const quantity = product.isCartonBased ?
        (product.totalUnitsAvailable || 0) :
        (product.quantity || 0);
    const minStock = product.minStock || 5;

    if (quantity === 0) {
        return 'out-of-stock';
    } else if (quantity <= minStock) {
        return 'low-stock';
    } else {
        return 'in-stock';
    }
}

/**
 * الحصول على فئة Badge للمخزون
 */
function getStockBadgeClass(product) {
    const stockClass = getStockClass(product);
    switch (stockClass) {
        case 'in-stock':
            return 'success';
        case 'low-stock':
            return 'warning';
        case 'out-of-stock':
            return 'danger';
        default:
            return 'secondary';
    }
}

/**
 * الحصول على نص حالة المخزون
 */
function getStockStatus(product) {
    const stockClass = getStockClass(product);
    switch (stockClass) {
        case 'in-stock':
            return 'متوفر';
        case 'low-stock':
            return 'مخزون منخفض';
        case 'out-of-stock':
            return 'نفد المخزون';
        default:
            return 'غير محدد';
    }
}

/**
 * الحصول على اسم الوحدة
 */
function getUnitName(unit) {
    const units = {
        'piece': 'قطعة',
        'kg': 'كيلوجرام',
        'liter': 'لتر',
        'meter': 'متر',
        'box': 'صندوق'
    };
    return units[unit] || 'قطعة';
}

/**
 * الحصول على اسم الفئة
 */
function getCategoryName(category) {
    const categories = {
        'electronics': 'إلكترونيات',
        'clothing': 'ملابس',
        'food': 'أغذية',
        'books': 'كتب',
        'home': 'منزلية',
        'other': 'أخرى'
    };
    return categories[category] || category || 'غير محدد';
}

/**
 * فلترة المنتجات
 */
function filterProducts() {
    const searchTerm = document.getElementById('productSearchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;
    
    let filteredProducts = productsData;
    
    // فلترة بالبحث
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            (product.barcode && product.barcode.toLowerCase().includes(searchTerm)) ||
            (product.description && product.description.toLowerCase().includes(searchTerm))
        );
    }
    
    // فلترة بالفئة
    if (categoryFilter) {
        filteredProducts = filteredProducts.filter(product => 
            product.category === categoryFilter
        );
    }
    
    // فلترة بحالة المخزون
    if (stockFilter) {
        filteredProducts = filteredProducts.filter(product => {
            const quantity = product.isCartonBased ?
                (product.totalUnitsAvailable || 0) :
                (product.quantity || 0);
            const minStock = product.minStock || 5;

            switch (stockFilter) {
                case 'in-stock':
                    return quantity > minStock;
                case 'low-stock':
                    return quantity <= minStock && quantity > 0;
                case 'out-of-stock':
                    return quantity === 0;
                default:
                    return true;
            }
        });
    }
    
    displayProducts(filteredProducts);
}

/**
 * إظهار نافذة إضافة منتج
 */
function showAddProductModal() {
    currentProduct = null;
    document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productForm').reset();
    document.getElementById('imagePreview').innerHTML = '';
    app.showModal('productModal');
}

/**
 * تعديل منتج
 */
function editProduct(productId) {
    currentProduct = db.getProduct(productId);
    if (!currentProduct) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }
    
    document.getElementById('productModalTitle').textContent = 'تعديل المنتج';
    
    // ملء النموذج
    const form = document.getElementById('productForm');
    form.name.value = currentProduct.name;
    form.barcode.value = currentProduct.barcode || '';
    form.category.value = currentProduct.category || '';
    form.unit.value = currentProduct.unit || 'piece';
    form.description.value = currentProduct.description || '';

    // تحديد نوع المنتج وملء الحقول المناسبة
    const isCartonBasedCheckbox = document.getElementById('isCartonBased');
    if (currentProduct.isCartonBased) {
        isCartonBasedCheckbox.checked = true;

        // ملء حقول المنتج المبني على الكرتون
        form.unitsPerCarton.value = currentProduct.unitsPerCarton || 1;
        form.cartonCostPrice.value = currentProduct.cartonCostPrice || '';
        form.unitSalePrice.value = currentProduct.unitSalePrice || '';
        form.cartonQuantity.value = currentProduct.cartonQuantity || 0;
        form.unitQuantity.value = currentProduct.unitQuantity || 0;
        form.minStock.value = currentProduct.minStock || 5;

        // حساب القيم المشتقة
        calculateCartonValues();
    } else {
        isCartonBasedCheckbox.checked = false;

        // ملء حقول المنتج العادي
        form.costPrice.value = currentProduct.costPrice || '';
        form.price.value = currentProduct.price;
        form.quantity.value = currentProduct.quantity;
        form.minStock.value = currentProduct.minStock || 5;
    }

    // تحديث عرض الحقول
    toggleProductType();

    // عرض الصورة إذا كانت موجودة
    const imagePreview = document.getElementById('imagePreview');
    if (currentProduct.image) {
        imagePreview.innerHTML = `<img src="${currentProduct.image}" alt="معاينة">`;
    } else {
        imagePreview.innerHTML = '';
    }
    
    app.showModal('productModal');
}

/**
 * معالجة إرسال نموذج المنتج
 */
async function handleProductSubmit(e) {
    e.preventDefault();
    console.log('بدء معالجة إرسال نموذج المنتج');

    const formData = new FormData(e.target);
    const isCartonBased = formData.get('isCartonBased') === 'on';

    const productData = {
        name: formData.get('name') ? formData.get('name').trim() : '',
        barcode: formData.get('barcode') ? formData.get('barcode').trim() : '',
        category: formData.get('category') || '',
        unit: formData.get('unit') || 'piece',
        description: formData.get('description') ? formData.get('description').trim() : '',
        isCartonBased: isCartonBased
    };

    if (isCartonBased) {
        // بيانات المنتج المبني على الكرتون
        productData.unitsPerCarton = parseInt(formData.get('unitsPerCarton')) || 1;
        productData.cartonCostPrice = parseFloat(formData.get('cartonCostPrice')) || 0;
        productData.unitSalePrice = parseFloat(formData.get('unitSalePrice')) || 0;
        productData.cartonQuantity = parseInt(formData.get('cartonQuantity')) || 0;
        productData.unitQuantity = parseInt(formData.get('unitQuantity')) || 0;
        productData.minStock = parseInt(formData.get('minStock')) || 5;

        // حساب القيم المشتقة
        productData.unitCostPrice = productData.cartonCostPrice / productData.unitsPerCarton;
        productData.totalUnitsAvailable = (productData.cartonQuantity * productData.unitsPerCarton) + productData.unitQuantity;
        productData.price = productData.unitSalePrice; // للتوافق مع النظام الحالي
        productData.quantity = productData.totalUnitsAvailable; // للتوافق مع النظام الحالي
        productData.costPrice = productData.unitCostPrice; // للتوافق مع النظام الحالي
    } else {
        // بيانات المنتج العادي
        productData.costPrice = parseFloat(formData.get('costPrice')) || 0;
        productData.price = parseFloat(formData.get('price')) || 0;
        productData.quantity = parseInt(formData.get('quantity')) || 0;
        productData.minStock = parseInt(formData.get('minStock')) || 5;
    }

    console.log('بيانات المنتج:', productData);

    // التحقق من صحة البيانات
    if (!productData.name || productData.name.length < 2) {
        app.showAlert('خطأ', 'يرجى إدخال اسم المنتج (أكثر من حرفين)');
        return;
    }

    if (isCartonBased) {
        // التحقق من بيانات المنتج المبني على الكرتون
        if (productData.unitsPerCarton <= 0) {
            app.showAlert('خطأ', 'يرجى إدخال عدد صحيح للقطع في الكرتونة');
            return;
        }
        if (isNaN(productData.cartonCostPrice) || productData.cartonCostPrice < 0) {
            app.showAlert('خطأ', 'يرجى إدخال سعر شراء صحيح للكرتونة');
            return;
        }
        if (isNaN(productData.unitSalePrice) || productData.unitSalePrice <= 0) {
            app.showAlert('خطأ', 'يرجى إدخال سعر بيع صحيح للقطعة الواحدة');
            return;
        }
        if (productData.cartonQuantity < 0 || productData.unitQuantity < 0) {
            app.showAlert('خطأ', 'يرجى إدخال كميات صحيحة');
            return;
        }
    } else {
        // التحقق من بيانات المنتج العادي
        if (isNaN(productData.price) || productData.price <= 0) {
            app.showAlert('خطأ', 'يرجى إدخال سعر صحيح');
            return;
        }
        if (isNaN(productData.quantity) || productData.quantity < 0) {
            app.showAlert('خطأ', 'يرجى إدخال كمية صحيحة');
            return;
        }
    }
    
    // التحقق من تفرد الباركود
    if (productData.barcode) {
        const existingProduct = db.getProductByBarcode(productData.barcode);
        if (existingProduct && (!currentProduct || existingProduct.id !== currentProduct.id)) {
            app.showAlert('خطأ', 'الباركود موجود مسبقاً');
            return;
        }
    }
    
    app.showLoading();
    
    try {
        // معالجة الصورة
        const imageFile = formData.get('image');
        if (imageFile && imageFile.size > 0) {
            productData.image = await processProductImage(imageFile);
        } else if (currentProduct && currentProduct.image) {
            productData.image = currentProduct.image;
        }
        
        let result;
        if (currentProduct) {
            // تحديث منتج موجود
            result = db.updateProduct(currentProduct.id, productData);
            if (result) {
                app.showNotification('تم تحديث المنتج بنجاح', 'success');
            } else {
                throw new Error('فشل في تحديث المنتج');
            }
        } else {
            // إضافة منتج جديد
            result = db.addProduct(productData);
            if (result) {
                app.showNotification('تم إضافة المنتج بنجاح', 'success');
            } else {
                throw new Error('فشل في إضافة المنتج');
            }
        }

        // تحديث العرض
        loadProductsData();
        displayProducts();

        // إغلاق النافذة
        app.hideModal('productModal');
        
    } catch (error) {
        console.error('خطأ في حفظ المنتج:', error);
        app.showAlert('خطأ', 'حدث خطأ في حفظ المنتج');
    }
    
    app.hideLoading();
}

/**
 * معالجة صورة المنتج
 */
function processProductImage(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(file);
    });
}

/**
 * معاينة الصورة
 */
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = (e) => {
            preview.innerHTML = `<img src="${e.target.result}" alt="معاينة">`;
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}

/**
 * توليد باركود عشوائي
 */
function generateBarcode() {
    const barcode = Date.now().toString() + Math.random().toString(36).substring(2, 7);
    document.querySelector('[name="barcode"]').value = barcode;
}

/**
 * تعديل المخزون
 */
function adjustStock(productId) {
    const product = db.getProduct(productId);
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }
    
    const newQuantity = prompt(
        `الكمية الحالية: ${product.quantity}\nأدخل الكمية الجديدة:`,
        product.quantity
    );
    
    if (newQuantity === null) return;
    
    const quantity = parseInt(newQuantity);
    if (isNaN(quantity) || quantity < 0) {
        app.showAlert('خطأ', 'يرجى إدخال كمية صحيحة');
        return;
    }
    
    db.updateProduct(product.id, { quantity });
    app.showNotification('تم تحديث المخزون بنجاح', 'success');
    
    loadProductsData();
    displayProducts();
}

/**
 * حذف منتج
 */
function deleteProduct(productId) {
    const product = db.getProduct(productId);
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }
    
    app.showConfirm(
        'حذف المنتج',
        `هل أنت متأكد من حذف المنتج "${product.name}"؟`,
        () => {
            db.deleteProduct(productId);
            app.showNotification('تم حذف المنتج بنجاح', 'success');
            
            loadProductsData();
            displayProducts();
        }
    );
}

/**
 * تصدير المنتجات
 */
function exportProducts() {
    const products = db.getProducts();
    const exportData = products.map(product => ({
        name: product.name,
        barcode: product.barcode,
        category: product.category,
        unit: product.unit,
        costPrice: product.costPrice,
        price: product.price,
        quantity: product.quantity,
        minStock: product.minStock,
        description: product.description
    }));
    
    Utils.downloadJSON(exportData, `products_${new Date().toISOString().split('T')[0]}.json`);
    app.showNotification('تم تصدير المنتجات بنجاح', 'success');
}

/**
 * إظهار نافذة الاستيراد
 */
function showImportModal() {
    app.showModal('importModal');
}

/**
 * استيراد المنتجات
 */
async function importProducts() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        app.showAlert('خطأ', 'يرجى اختيار ملف');
        return;
    }
    
    app.showLoading();
    
    try {
        const data = await Utils.readJSONFile(file);
        
        if (!Array.isArray(data)) {
            throw new Error('تنسيق الملف غير صحيح');
        }
        
        let importedCount = 0;
        let errorCount = 0;
        
        for (const productData of data) {
            try {
                // التحقق من البيانات الأساسية
                if (!productData.name || !productData.price) {
                    errorCount++;
                    continue;
                }
                
                // التحقق من تفرد الباركود
                if (productData.barcode && db.getProductByBarcode(productData.barcode)) {
                    errorCount++;
                    continue;
                }
                
                db.addProduct(productData);
                importedCount++;
                
            } catch (error) {
                errorCount++;
            }
        }
        
        app.showNotification(
            `تم استيراد ${importedCount} منتج بنجاح${errorCount > 0 ? ` (${errorCount} خطأ)` : ''}`,
            'success'
        );
        
        // تحديث العرض
        loadProductsData();
        displayProducts();
        
        // إغلاق النافذة
        app.hideModal('importModal');
        
    } catch (error) {
        console.error('خطأ في الاستيراد:', error);
        app.showAlert('خطأ', 'حدث خطأ في استيراد الملف');
    }
    
    app.hideLoading();
}

/**
 * تحميل نموذج الاستيراد
 */
function downloadTemplate() {
    const template = [
        {
            name: "منتج تجريبي",
            barcode: "1234567890",
            category: "electronics",
            unit: "piece",
            costPrice: 50,
            price: 100,
            quantity: 10,
            minStock: 5,
            description: "وصف المنتج"
        }
    ];
    
    Utils.downloadJSON(template, 'products_template.json');
    app.showNotification('تم تحميل النموذج بنجاح', 'info');
}

/**
 * الحصول على اسم الفئة
 */
function getCategoryName(category) {
    const categories = {
        electronics: 'إلكترونيات',
        clothing: 'ملابس',
        food: 'أغذية',
        books: 'كتب',
        home: 'منزلية',
        other: 'أخرى'
    };
    return categories[category] || category || 'غير محدد';
}

/**
 * الحصول على اسم الوحدة
 */
function getUnitName(unit) {
    const units = {
        piece: 'قطعة',
        kg: 'كجم',
        liter: 'لتر',
        meter: 'متر',
        box: 'صندوق'
    };
    return units[unit] || unit || 'قطعة';
}

/**
 * الحصول على فئة CSS للمخزون
 */
function getStockClass(product) {
    if (product.quantity === 0) return 'out-of-stock';
    if (product.quantity <= product.minStock) return 'low-stock';
    return 'in-stock';
}

/**
 * الحصول على فئة شارة المخزون
 */
function getStockBadgeClass(product) {
    if (product.quantity === 0) return 'danger';
    if (product.quantity <= product.minStock) return 'warning';
    return 'success';
}

/**
 * الحصول على حالة المخزون
 */
function getStockStatus(product) {
    if (product.quantity === 0) return 'نفد المخزون';
    if (product.quantity <= product.minStock) return 'مخزون منخفض';
    return 'متوفر';
}




// تهيئة الصفحة عند تحميلها
document.addEventListener('DOMContentLoaded', function() {
    // تأخير التهيئة للتأكد من تحميل جميع العناصر
    setTimeout(initializeProductsPage, 100);
});

/**
 * تحديث العملة في واجهة المنتجات
 */
function updateProductsCurrency(currency) {
    try {
        // تحديث العملة في العنصر الجذر
        document.documentElement.setAttribute('data-currency', currency);

        // تحديث جميع عناصر العملة في واجهة المنتجات
        const currencyElements = document.querySelectorAll('.currency, [data-currency], .price, .cost, .amount');
        currencyElements.forEach(element => {
            element.setAttribute('data-currency', currency);
            element.classList.add('currency');
        });

        // إعادة تحميل قائمة المنتجات لتطبيق العملة الجديدة
        if (typeof loadProductsList === 'function') {
            loadProductsList();
        }

        console.log('تم تحديث العملة في المنتجات:', currency);
    } catch (error) {
        console.error('خطأ في تحديث العملة في المنتجات:', error);
    }
}

/**
 * تحديث نوع الأرقام في واجهة المنتجات
 */
function updateProductsNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // إعادة تحميل قائمة المنتجات لتطبيق نوع الأرقام الجديد
        if (typeof loadProductsList === 'function') {
            loadProductsList();
        }

        console.log('تم تحديث نوع الأرقام في المنتجات:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام في المنتجات:', error);
    }
}



/**
 * تحديث نوع الأرقام في واجهة المنتجات
 */
function updateProductsNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // تحديث جميع عناصر الأرقام
        const numberElements = document.querySelectorAll('.number, .price, .cost, .amount, .quantity');
        numberElements.forEach(element => {
            element.classList.add('number');
        });

        // إعادة تحميل قائمة المنتجات لتطبيق نوع الأرقام الجديد
        if (typeof loadProductsList === 'function') {
            loadProductsList();
        }

        console.log('تم تحديث نوع الأرقام في المنتجات:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام في المنتجات:', error);
    }
}

/**
 * إنشاء مولد الباركود
 */
let barcodeGenerator;
function initializeBarcodeGenerator() {
    if (!barcodeGenerator) {
        barcodeGenerator = new BarcodeGenerator();
    }
    return barcodeGenerator;
}

/**
 * توليد باركود للمنتج
 */
function generateProductBarcode() {
    const barcodeInput = document.getElementById('barcodeInput');
    if (!barcodeInput) return;

    const generator = initializeBarcodeGenerator();
    const productId = currentProduct ? currentProduct.id : Date.now().toString();
    const newBarcode = generator.generateProductBarcode(productId, 'CODE128');

    barcodeInput.value = newBarcode;
    previewBarcode();
}

/**
 * معاينة الباركود
 */
function previewBarcode() {
    const barcodeInput = document.getElementById('barcodeInput');
    const previewDiv = document.getElementById('barcodePreview');

    if (!barcodeInput || !previewDiv) return;

    const barcodeValue = barcodeInput.value.trim();
    if (!barcodeValue) {
        previewDiv.style.display = 'none';
        return;
    }

    try {
        const generator = initializeBarcodeGenerator();
        const pattern = generator.generateCode128(barcodeValue);
        const canvas = generator.renderToCanvas(pattern, {
            width: 200,
            height: 60,
            showText: true,
            text: barcodeValue,
            fontSize: 10
        });

        previewDiv.innerHTML = '';
        previewDiv.appendChild(canvas);
        previewDiv.style.display = 'block';
    } catch (error) {
        previewDiv.innerHTML = `<div class="alert alert-danger">خطأ في توليد الباركود: ${error.message}</div>`;
        previewDiv.style.display = 'block';
    }
}

/**
 * إظهار نافذة إدارة الباركود
 */
function showBarcodeManager() {
    loadProductsForBarcode();
    loadProductsSelection();
    app.showModal('barcodeManagerModal');
}

/**
 * تحميل المنتجات في قائمة الباركود
 */
function loadProductsForBarcode() {
    const select = document.getElementById('productSelectBarcode');
    if (!select) return;

    const products = db.getProducts();
    select.innerHTML = '<option value="">اختر منتج...</option>';

    products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} - ${db.formatCurrency(product.price)}`;
        select.appendChild(option);
    });
}

/**
 * تحميل قائمة المنتجات للاختيار المجمع
 */
function loadProductsSelection() {
    const container = document.getElementById('productsSelection');
    if (!container) return;

    const products = db.getProducts();
    container.innerHTML = '';

    products.forEach(product => {
        const div = document.createElement('div');
        div.className = 'product-selection-item';
        div.innerHTML = `
            <label class="checkbox-label">
                <input type="checkbox" value="${product.id}" class="product-checkbox">
                <span class="product-info">
                    <strong>${product.name}</strong>
                    <small>السعر: ${db.formatCurrency(product.price)} | الباركود: ${product.barcode || 'غير محدد'}</small>
                </span>
            </label>
        `;
        container.appendChild(div);
    });
}

/**
 * تبديل تبويبات إدارة الباركود
 */
function switchBarcodeTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // تفعيل الزر المحدد
    event.target.classList.add('active');
}

/**
 * توليد باركود فردي
 */
function generateSingleBarcode() {
    const productSelect = document.getElementById('productSelectBarcode');
    const barcodeType = document.getElementById('barcodeType');
    const resultDiv = document.getElementById('singleBarcodeResult');

    if (!productSelect.value) {
        app.showAlert('خطأ', 'يرجى اختيار منتج');
        return;
    }

    const product = db.getProduct(productSelect.value);
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }

    try {
        const generator = initializeBarcodeGenerator();
        let barcodeValue;

        if (product.barcode) {
            barcodeValue = product.barcode;
        } else {
            barcodeValue = generator.generateProductBarcode(product.id, barcodeType.value);
            // حفظ الباركود الجديد في المنتج
            db.updateProduct(product.id, { barcode: barcodeValue });
        }

        const pattern = barcodeType.value === 'EAN13' ?
            generator.generateEAN13(barcodeValue) :
            generator.generateCode128(barcodeValue);

        const canvas = generator.renderToCanvas(pattern, {
            width: 300,
            height: 100,
            showText: true,
            text: barcodeValue,
            fontSize: 12
        });

        resultDiv.innerHTML = `
            <div class="barcode-item">
                <h4>${product.name}</h4>
                <div class="barcode-canvas"></div>
                <p>الباركود: ${barcodeValue}</p>
                <div class="barcode-actions">
                    <button class="btn btn-sm btn-success" onclick="downloadBarcodeImage('${product.id}')">
                        <i class="fas fa-download"></i> تحميل PNG
                    </button>
                    <button class="btn btn-sm btn-info" onclick="printSingleBarcode('${product.id}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        `;

        resultDiv.querySelector('.barcode-canvas').appendChild(canvas);

        // تحديث عرض المنتجات
        displayProducts();

        app.showNotification('تم توليد الباركود بنجاح', 'success');

    } catch (error) {
        app.showAlert('خطأ', `فشل في توليد الباركود: ${error.message}`);
    }
}

/**
 * طباعة باركود منتج واحد
 */
function printProductBarcode(productId) {
    const product = db.getProduct(productId);
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }

    if (!product.barcode) {
        // محاولة توليد باركود تلقائياً
        const generator = initializeBarcodeGenerator();
        const newBarcode = generator.generateProductBarcode(product.id, 'CODE128');
        db.updateProduct(product.id, { barcode: newBarcode });
        product.barcode = newBarcode;

        app.showNotification('تم توليد باركود تلقائياً للمنتج', 'info');

        // تحديث عرض المنتجات
        displayProducts();
    }

    try {
        const generator = initializeBarcodeGenerator();
        const pattern = generator.generateCode128(product.barcode);
        const canvas = generator.renderToCanvas(pattern, {
            width: 400,
            height: 150,
            showText: true,
            text: product.barcode,
            fontSize: 14
        });

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة باركود - ${product.name}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            text-align: center;
                            padding: 20px;
                            direction: rtl;
                        }
                        .barcode-label {
                            border: 1px solid #ccc;
                            padding: 15px;
                            margin: 10px;
                            display: inline-block;
                            background: white;
                        }
                        .product-name {
                            font-weight: bold;
                            margin-bottom: 10px;
                            font-size: 16px;
                        }
                        .product-price {
                            color: #666;
                            margin-top: 10px;
                            font-size: 14px;
                        }
                        .barcode-text {
                            font-family: monospace;
                            font-size: 12px;
                            margin-top: 5px;
                        }
                        @media print {
                            body { margin: 0; }
                            .barcode-label {
                                border: 1px solid #000;
                                page-break-inside: avoid;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="barcode-label">
                        <div class="product-name">${product.name}</div>
                        <div id="barcodeContainer"></div>
                        <div class="barcode-text">${product.barcode}</div>
                        <div class="product-price">${db.formatCurrency(product.price)}</div>
                    </div>
                </body>
            </html>
        `);

        printWindow.document.getElementById('barcodeContainer').appendChild(canvas);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                app.showNotification('تم إرسال الباركود للطباعة بنجاح', 'success');
                // إغلاق النافذة بعد الطباعة (اختياري)
                setTimeout(() => {
                    printWindow.close();
                }, 2000);
            }, 500);
        };

        // في حالة عدم تشغيل onload
        setTimeout(() => {
            if (printWindow.document.readyState === 'complete') {
                printWindow.print();
                app.showNotification('تم إرسال الباركود للطباعة', 'success');
            }
        }, 1000);

    } catch (error) {
        app.showAlert('خطأ', `فشل في طباعة الباركود: ${error.message}`);
    }
}

/**
 * توليد باركود مجمع
 */
function generateBulkBarcodes() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    const barcodeType = document.getElementById('bulkBarcodeType').value;
    const resultDiv = document.getElementById('bulkBarcodeResult');

    if (checkboxes.length === 0) {
        app.showAlert('خطأ', 'يرجى اختيار منتج واحد على الأقل');
        return;
    }

    const generator = initializeBarcodeGenerator();
    let successCount = 0;
    let errorCount = 0;
    const results = [];

    checkboxes.forEach(checkbox => {
        const productId = checkbox.value;
        const product = db.getProduct(productId);

        if (!product) {
            errorCount++;
            return;
        }

        try {
            let barcodeValue;

            if (product.barcode) {
                barcodeValue = product.barcode;
            } else {
                barcodeValue = generator.generateProductBarcode(product.id, barcodeType);
                // حفظ الباركود الجديد في المنتج
                db.updateProduct(product.id, { barcode: barcodeValue });
            }

            const pattern = barcodeType === 'EAN13' ?
                generator.generateEAN13(barcodeValue) :
                generator.generateCode128(barcodeValue);

            const canvas = generator.renderToCanvas(pattern, {
                width: 250,
                height: 80,
                showText: true,
                text: barcodeValue,
                fontSize: 10
            });

            results.push({
                product: product,
                canvas: canvas,
                barcode: barcodeValue
            });

            successCount++;

        } catch (error) {
            console.error(`خطأ في توليد باركود للمنتج ${product.name}:`, error);
            errorCount++;
        }
    });

    // عرض النتائج
    resultDiv.innerHTML = `
        <div class="bulk-results-header">
            <h4>نتائج التوليد المجمع</h4>
            <p>تم توليد ${successCount} باركود بنجاح${errorCount > 0 ? ` (${errorCount} خطأ)` : ''}</p>
        </div>
        <div class="bulk-results-grid" id="bulkResultsGrid"></div>
        <div class="bulk-actions">
            <button class="btn btn-success" onclick="downloadAllBarcodes()">
                <i class="fas fa-download"></i> تحميل جميع الباركود
            </button>
            <button class="btn btn-info" onclick="printAllBarcodes()">
                <i class="fas fa-print"></i> طباعة جميع الباركود
            </button>
        </div>
    `;

    const grid = document.getElementById('bulkResultsGrid');
    results.forEach(result => {
        const div = document.createElement('div');
        div.className = 'barcode-item';
        div.innerHTML = `
            <h5>${result.product.name}</h5>
            <div class="barcode-canvas"></div>
            <p class="barcode-text">${result.barcode}</p>
        `;
        div.querySelector('.barcode-canvas').appendChild(result.canvas);
        grid.appendChild(div);
    });

    // تحديث عرض المنتجات
    displayProducts();

    app.showNotification(`تم توليد ${successCount} باركود بنجاح`, 'success');
}

/**
 * تحديد/إلغاء تحديد جميع المنتجات
 */
function toggleSelectAllProducts() {
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');

    productCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

/**
 * طباعة باركود واحد من نافذة التوليد الفردي
 */
function printSingleBarcode(productId) {
    printProductBarcode(productId);
}

/**
 * تحميل صورة الباركود
 */
function downloadBarcodeImage(productId) {
    const product = db.getProduct(productId);
    if (!product || !product.barcode) {
        app.showAlert('خطأ', 'المنتج أو الباركود غير موجود');
        return;
    }

    try {
        const generator = initializeBarcodeGenerator();
        const pattern = generator.generateCode128(product.barcode);
        const canvas = generator.renderToCanvas(pattern, {
            width: 400,
            height: 150,
            showText: true,
            text: product.barcode,
            fontSize: 14
        });

        // تحويل Canvas إلى صورة وتحميلها
        const imageData = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `barcode-${product.name}-${product.barcode}.png`;
        link.href = imageData;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        app.showNotification('تم تحميل صورة الباركود بنجاح', 'success');

    } catch (error) {
        app.showAlert('خطأ', `فشل في تحميل الصورة: ${error.message}`);
    }
}

/**
 * تحميل جميع صور الباركود
 */
function downloadAllBarcodes() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    if (checkboxes.length === 0) {
        app.showAlert('خطأ', 'لا توجد منتجات محددة');
        return;
    }

    let downloadCount = 0;
    const generator = initializeBarcodeGenerator();

    checkboxes.forEach((checkbox, index) => {
        const productId = checkbox.value;
        const product = db.getProduct(productId);

        if (product && product.barcode) {
            setTimeout(() => {
                try {
                    const pattern = generator.generateCode128(product.barcode);
                    const canvas = generator.renderToCanvas(pattern, {
                        width: 400,
                        height: 150,
                        showText: true,
                        text: product.barcode,
                        fontSize: 14
                    });

                    const imageData = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = `barcode-${product.name}-${product.barcode}.png`;
                    link.href = imageData;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    downloadCount++;

                    if (downloadCount === checkboxes.length) {
                        app.showNotification(`تم تحميل ${downloadCount} صورة باركود بنجاح`, 'success');
                    }
                } catch (error) {
                    console.error(`خطأ في تحميل باركود ${product.name}:`, error);
                }
            }, index * 500); // تأخير بين التحميلات
        }
    });
}

/**
 * طباعة جميع الباركود المحددة
 */
function printAllBarcodes() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    if (checkboxes.length === 0) {
        app.showAlert('خطأ', 'لا توجد منتجات محددة للطباعة');
        return;
    }

    const generator = initializeBarcodeGenerator();
    const products = [];

    // جمع المنتجات المحددة
    checkboxes.forEach(checkbox => {
        const productId = checkbox.value;
        const product = db.getProduct(productId);
        if (product && product.barcode) {
            products.push(product);
        }
    });

    if (products.length === 0) {
        app.showAlert('خطأ', 'لا توجد منتجات تحتوي على باركود للطباعة');
        return;
    }

    try {
        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة باركود متعددة - تكنوفلاش</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            direction: rtl;
                        }
                        .barcode-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 20px;
                            margin: 20px 0;
                        }
                        .barcode-label {
                            border: 1px solid #ccc;
                            padding: 15px;
                            text-align: center;
                            background: white;
                            page-break-inside: avoid;
                        }
                        .product-name {
                            font-weight: bold;
                            margin-bottom: 10px;
                            font-size: 16px;
                        }
                        .product-price {
                            color: #666;
                            margin-top: 10px;
                            font-size: 14px;
                        }
                        .barcode-text {
                            font-family: monospace;
                            font-size: 12px;
                            margin-top: 5px;
                        }
                        @media print {
                            body { margin: 0; }
                            .barcode-label {
                                border: 1px solid #000;
                            }
                        }
                        @page {
                            margin: 1cm;
                        }
                    </style>
                </head>
                <body>
                    <h1 style="text-align: center;">طباعة باركود المنتجات - تكنوفلاش</h1>
                    <div class="barcode-grid" id="barcodeGrid">
                    </div>
                </body>
            </html>
        `);

        const grid = printWindow.document.getElementById('barcodeGrid');

        // إضافة كل منتج
        products.forEach(product => {
            const pattern = generator.generateCode128(product.barcode);
            const canvas = generator.renderToCanvas(pattern, {
                width: 200,
                height: 80,
                showText: true,
                text: product.barcode,
                fontSize: 10
            });

            const labelDiv = printWindow.document.createElement('div');
            labelDiv.className = 'barcode-label';
            labelDiv.innerHTML = `
                <div class="product-name">${product.name}</div>
                <div class="barcode-container"></div>
                <div class="barcode-text">${product.barcode}</div>
                <div class="product-price">${db.formatCurrency(product.price)}</div>
            `;

            labelDiv.querySelector('.barcode-container').appendChild(canvas);
            grid.appendChild(labelDiv);
        });

        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                // إغلاق النافذة بعد الطباعة (اختياري)
                setTimeout(() => {
                    printWindow.close();
                }, 2000);
            }, 1000);
        };

        // في حالة عدم تشغيل onload
        setTimeout(() => {
            if (printWindow.document.readyState === 'complete') {
                printWindow.print();
            }
        }, 2000);

        app.showNotification(`تم إعداد طباعة ${products.length} باركود`, 'success');

    } catch (error) {
        app.showAlert('خطأ', `فشل في طباعة الباركود: ${error.message}`);
    }
}

/**
 * معاينة طباعة الباركود
 */
function previewPrintBarcodes() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    const previewDiv = document.getElementById('printPreview');

    if (!previewDiv) return;

    if (checkboxes.length === 0) {
        previewDiv.innerHTML = '<div class="alert alert-warning">يرجى تحديد منتجات للمعاينة</div>';
        return;
    }

    const generator = initializeBarcodeGenerator();
    const products = [];

    // جمع المنتجات المحددة
    checkboxes.forEach(checkbox => {
        const productId = checkbox.value;
        const product = db.getProduct(productId);
        if (product) {
            if (!product.barcode) {
                // توليد باركود تلقائياً
                const newBarcode = generator.generateProductBarcode(product.id, 'CODE128');
                db.updateProduct(product.id, { barcode: newBarcode });
                product.barcode = newBarcode;
            }
            products.push(product);
        }
    });

    if (products.length === 0) {
        previewDiv.innerHTML = '<div class="alert alert-warning">لا توجد منتجات صالحة للمعاينة</div>';
        return;
    }

    try {
        previewDiv.innerHTML = '<h4>معاينة الطباعة</h4><div class="barcode-grid" id="previewGrid"></div>';
        const grid = document.getElementById('previewGrid');

        products.forEach(product => {
            const pattern = generator.generateCode128(product.barcode);
            const canvas = generator.renderToCanvas(pattern, {
                width: 200,
                height: 80,
                showText: true,
                text: product.barcode,
                fontSize: 10
            });

            const labelDiv = document.createElement('div');
            labelDiv.className = 'barcode-label';
            labelDiv.style.cssText = `
                border: 1px solid #ccc;
                padding: 10px;
                margin: 5px;
                text-align: center;
                background: white;
                display: inline-block;
                vertical-align: top;
            `;

            labelDiv.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">${product.name}</div>
                <div class="barcode-container"></div>
                <div style="font-family: monospace; font-size: 12px; margin: 5px 0;">${product.barcode}</div>
                <div style="color: #666; font-size: 14px;">${db.formatCurrency(product.price)}</div>
            `;

            labelDiv.querySelector('.barcode-container').appendChild(canvas);
            grid.appendChild(labelDiv);
        });

        // تحديث عرض المنتجات
        displayProducts();

    } catch (error) {
        previewDiv.innerHTML = `<div class="alert alert-danger">خطأ في المعاينة: ${error.message}</div>`;
    }
}

/**
 * طباعة الباركود من تبويب الطباعة
 */
function printBarcodes() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');

    if (checkboxes.length === 0) {
        app.showAlert('خطأ', 'يرجى تحديد منتجات للطباعة');
        return;
    }

    // استخدام دالة طباعة جميع الباركود الموجودة
    printAllBarcodes();
}

/**
 * تبديل نوع المنتج بين عادي ومبني على الكرتون
 */
function toggleProductType() {
    const isCartonBased = document.getElementById('isCartonBased').checked;
    const regularFields = document.getElementById('regularProductFields');
    const cartonFields = document.getElementById('cartonBasedFields');

    if (isCartonBased) {
        regularFields.style.display = 'none';
        cartonFields.style.display = 'block';

        // إزالة required من الحقول العادية
        regularFields.querySelectorAll('input[required]').forEach(input => {
            input.removeAttribute('required');
        });

        // إضافة required للحقول المطلوبة في الكرتون
        const requiredCartonFields = ['unitsPerCarton', 'cartonCostPrice', 'unitSalePrice'];
        requiredCartonFields.forEach(fieldName => {
            const field = document.querySelector(`input[name="${fieldName}"]`);
            if (field) {
                field.setAttribute('required', 'required');
            }
        });
    } else {
        regularFields.style.display = 'block';
        cartonFields.style.display = 'none';

        // إضافة required للحقول العادية
        regularFields.querySelectorAll('input[data-required="true"]').forEach(input => {
            input.setAttribute('required', 'required');
        });

        // إزالة required من حقول الكرتون
        cartonFields.querySelectorAll('input[required]').forEach(input => {
            input.removeAttribute('required');
        });
    }
}

/**
 * حساب القيم للمنتجات المبنية على الكرتون
 */
function calculateCartonValues() {
    const unitsPerCarton = parseFloat(document.querySelector('input[name="unitsPerCarton"]').value) || 0;
    const cartonCostPrice = parseFloat(document.querySelector('input[name="cartonCostPrice"]').value) || 0;
    const unitSalePrice = parseFloat(document.querySelector('input[name="unitSalePrice"]').value) || 0;
    const cartonQuantity = parseInt(document.querySelector('input[name="cartonQuantity"]').value) || 0;
    const unitQuantity = parseInt(document.querySelector('input[name="unitQuantity"]').value) || 0;

    // حساب سعر بيع الكرتونة الكاملة
    const cartonSalePrice = unitSalePrice * unitsPerCarton;
    document.querySelector('input[name="cartonSalePrice"]').value = cartonSalePrice.toFixed(2);

    // حساب إجمالي القطع المتاحة
    const totalUnitsAvailable = (cartonQuantity * unitsPerCarton) + unitQuantity;
    document.querySelector('input[name="totalUnitsAvailable"]').value = totalUnitsAvailable;
}

// تصدير الدوال للاستخدام العام
window.generateProductBarcode = generateProductBarcode;
window.previewBarcode = previewBarcode;
window.showBarcodeManager = showBarcodeManager;
window.switchBarcodeTab = switchBarcodeTab;
window.generateSingleBarcode = generateSingleBarcode;
window.printProductBarcode = printProductBarcode;
window.printSingleBarcode = printSingleBarcode;
window.generateBulkBarcodes = generateBulkBarcodes;
window.toggleSelectAllProducts = toggleSelectAllProducts;
window.downloadBarcodeImage = downloadBarcodeImage;
window.downloadAllBarcodes = downloadAllBarcodes;
window.printAllBarcodes = printAllBarcodes;
window.previewPrintBarcodes = previewPrintBarcodes;
window.printBarcodes = printBarcodes;
