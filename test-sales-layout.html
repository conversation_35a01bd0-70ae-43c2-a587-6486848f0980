<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تخطيط المبيعات - تكنوفلاش</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #f0f2f5;
            padding: 20px;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-header">
        <h1><i class="fas fa-layout"></i> اختبار تخطيط المبيعات</h1>
        <p>فحص التنسيقات والتخطيط لصفحة المبيعات</p>
    </div>

    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>حالة التنسيقات</h3>
                    </div>
                    <div class="card-body">
                        <div id="layoutStatus">
                            <p><span class="status-indicator status-warning"></span> جاري فحص التنسيقات...</p>
                        </div>
                        <button class="btn btn-primary" onclick="checkLayout()">
                            <i class="fas fa-sync"></i> إعادة فحص
                        </button>
                        <button class="btn btn-success" onclick="fixLayout()">
                            <i class="fas fa-wrench"></i> إصلاح التنسيق
                        </button>
                        <button class="btn btn-warning" onclick="fixTablesOnly()">
                            <i class="fas fa-table"></i> إصلاح الجداول فقط
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- محاكاة صفحة المبيعات -->
        <div class="sales-container">
            <div class="row">
                <!-- قسم إضافة المنتجات -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-barcode"></i>
                                إضافة منتج
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- شريط البحث -->
                            <div class="search-section">
                                <div class="form-group">
                                    <label>البحث بالاسم أو الباركود</label>
                                    <div class="search-bar">
                                        <input type="text" class="search-input form-control" 
                                               placeholder="ابحث عن منتج أو امسح الباركود..." 
                                               autocomplete="off">
                                        <i class="fas fa-search search-icon"></i>
                                    </div>
                                    <div class="search-results">
                                        <div class="search-result-item">
                                            <div class="product-info">
                                                <strong>منتج تجريبي 1</strong><br>
                                                <small>الباركود: 123456789</small><br>
                                                <small>المخزون: 50</small>
                                            </div>
                                            <div class="product-price">25.00 ج.م</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- جدول المنتجات -->
                            <div class="sale-items">
                                <h4>المنتجات المضافة</h4>
                                <table class="table" id="saleItemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>منتج تجريبي 1</td>
                                            <td>25.00 ج.م</td>
                                            <td>
                                                <div class="quantity-controls">
                                                    <button class="btn btn-sm btn-secondary">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <span class="quantity-value">2</span>
                                                    <button class="btn btn-sm btn-secondary">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>50.00 ج.م</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="no-items" style="display: none;">
                                            <td colspan="5">لم يتم إضافة أي منتجات بعد</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم ملخص البيع -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-calculator"></i>
                                ملخص البيع
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="sale-summary">
                                <div class="summary-row">
                                    <span>المجموع الفرعي:</span>
                                    <span>50.00 ج.م</span>
                                </div>
                                <div class="summary-row">
                                    <span>الخصم:</span>
                                    <span>0.00 ج.م</span>
                                </div>
                                <div class="summary-row">
                                    <span>الضريبة:</span>
                                    <span>0.00 ج.م</span>
                                </div>
                                <div class="summary-row total">
                                    <span>الإجمالي:</span>
                                    <span>50.00 ج.م</span>
                                </div>
                            </div>

                            <div class="form-group mt-3">
                                <label>طريقة الدفع</label>
                                <select class="form-control">
                                    <option value="cash">نقداً</option>
                                    <option value="credit">آجل</option>
                                    <option value="card">بطاقة</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>المبلغ المدفوع</label>
                                <input type="number" class="form-control" value="50.00">
                            </div>

                            <button class="btn btn-success btn-block w-100 mt-3">
                                <i class="fas fa-check"></i> إتمام البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين الملفات -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="fix-sales-css.js"></script>
    
    <script>
        function checkLayout() {
            const statusDiv = document.getElementById('layoutStatus');
            statusDiv.innerHTML = '<p><span class="status-indicator status-warning"></span> جاري فحص التنسيقات...</p>';
            
            setTimeout(() => {
                const issues = [];
                
                // فحص البطاقات
                const cards = document.querySelectorAll('.sales-container .card');
                cards.forEach((card, index) => {
                    const computedStyle = window.getComputedStyle(card);
                    if (computedStyle.backgroundColor === 'rgba(0, 0, 0, 0)' || 
                        computedStyle.backgroundColor === 'transparent') {
                        issues.push(`البطاقة ${index + 1}: خلفية شفافة`);
                    }
                    if (!computedStyle.boxShadow || computedStyle.boxShadow === 'none') {
                        issues.push(`البطاقة ${index + 1}: لا توجد ظلال`);
                    }
                });
                
                // فحص الجداول
                const tables = document.querySelectorAll('.sales-container table');
                tables.forEach((table, index) => {
                    const computedStyle = window.getComputedStyle(table);
                    if (computedStyle.width !== '100%') {
                        issues.push(`الجدول ${index + 1}: العرض غير صحيح`);
                    }
                });
                
                // فحص الأزرار
                const buttons = document.querySelectorAll('.sales-container .btn');
                let buttonIssues = 0;
                buttons.forEach(button => {
                    const computedStyle = window.getComputedStyle(button);
                    if (!computedStyle.backgroundColor || computedStyle.backgroundColor === 'rgba(0, 0, 0, 0)') {
                        buttonIssues++;
                    }
                });
                
                if (buttonIssues > 0) {
                    issues.push(`${buttonIssues} أزرار بدون ألوان خلفية`);
                }
                
                // فحص الأعمدة
                const columns = document.querySelectorAll('.sales-container .col-md-8, .sales-container .col-md-4');
                columns.forEach((col, index) => {
                    const computedStyle = window.getComputedStyle(col);
                    if (!computedStyle.paddingLeft || computedStyle.paddingLeft === '0px') {
                        issues.push(`العمود ${index + 1}: لا توجد مسافات داخلية`);
                    }
                });
                
                // عرض النتائج
                let statusHTML = '';
                if (issues.length === 0) {
                    statusHTML = '<p><span class="status-indicator status-ok"></span> جميع التنسيقات تعمل بشكل صحيح!</p>';
                } else {
                    statusHTML = '<p><span class="status-indicator status-error"></span> تم اكتشاف مشاكل في التنسيق:</p>';
                    statusHTML += '<ul>';
                    issues.forEach(issue => {
                        statusHTML += `<li>${issue}</li>`;
                    });
                    statusHTML += '</ul>';
                }
                
                statusDiv.innerHTML = statusHTML;
            }, 1000);
        }
        
        function fixLayout() {
            const statusDiv = document.getElementById('layoutStatus');
            statusDiv.innerHTML = '<p><span class="status-indicator status-warning"></span> جاري إصلاح التنسيقات...</p>';

            if (typeof fixSalesCSS === 'function') {
                fixSalesCSS();
                setTimeout(() => {
                    statusDiv.innerHTML = '<p><span class="status-indicator status-ok"></span> تم تطبيق إصلاحات CSS!</p>';
                    setTimeout(checkLayout, 500);
                }, 1000);
            } else {
                statusDiv.innerHTML = '<p><span class="status-indicator status-error"></span> وظيفة الإصلاح غير متوفرة!</p>';
            }
        }

        function fixTablesOnly() {
            const statusDiv = document.getElementById('layoutStatus');
            statusDiv.innerHTML = '<p><span class="status-indicator status-warning"></span> جاري إصلاح الجداول...</p>';

            if (typeof fixTablesSpecifically === 'function') {
                fixTablesSpecifically();
                setTimeout(() => {
                    statusDiv.innerHTML = '<p><span class="status-indicator status-ok"></span> تم إصلاح الجداول!</p>';
                    setTimeout(checkLayout, 500);
                }, 1000);
            } else {
                statusDiv.innerHTML = '<p><span class="status-indicator status-error"></span> وظيفة إصلاح الجداول غير متوفرة!</p>';
            }
        }
        
        // تشغيل الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkLayout, 1000);
        });
    </script>
</body>
</html>
