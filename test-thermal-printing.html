<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة الحرارية - تكنوفلاش POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .test-section:last-child {
            border-bottom: none;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .status-success {
            color: #28a745;
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .status-error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .status-info {
            color: #17a2b8;
            background: #d1ecf1;
            border-color: #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-print"></i> اختبار الطباعة الحرارية</h1>
            <p>نظام تكنوفلاش POS - اختبار شامل للطابعات الحرارية وملصقات الباركود</p>
        </div>

        <!-- اختبار الطابعة الأساسي -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> اختبار الطابعة الأساسي</h3>
            <p>اختبار الاتصال والوظائف الأساسية للطابعة الحرارية</p>
            <button class="btn btn-primary test-btn" onclick="runBasicPrinterTest()">
                <i class="fas fa-play"></i> تشغيل الاختبار الأساسي
            </button>
            <div id="basicTestResult" class="result-area"></div>
        </div>

        <!-- اختبار فاتورة المبيعات -->
        <div class="test-section">
            <h3><i class="fas fa-receipt"></i> اختبار فاتورة المبيعات</h3>
            <p>اختبار طباعة فاتورة مبيعات تجريبية بحجم 58mm</p>
            <button class="btn btn-success test-btn" onclick="testSalesInvoice()">
                <i class="fas fa-file-invoice"></i> طباعة فاتورة تجريبية
            </button>
            <div id="invoiceTestResult" class="result-area"></div>
        </div>

        <!-- اختبار ملصقات الباركود -->
        <div class="test-section">
            <h3><i class="fas fa-barcode"></i> اختبار ملصقات الباركود</h3>
            <p>اختبار طباعة ملصقات باركود بأحجام مختلفة</p>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-info test-btn w-100" onclick="testSingleBarcodeLabel()">
                        <i class="fas fa-tag"></i> ملصق واحد
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-warning test-btn w-100" onclick="testMultipleBarcodeLabels()">
                        <i class="fas fa-tags"></i> ملصقات متعددة
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-secondary test-btn w-100" onclick="testCustomSizeLabel()">
                        <i class="fas fa-expand-arrows-alt"></i> حجم مخصص
                    </button>
                </div>
            </div>
            <div id="barcodeTestResult" class="result-area"></div>
        </div>

        <!-- اختبار أحجام الورق -->
        <div class="test-section">
            <h3><i class="fas fa-ruler"></i> اختبار أحجام الورق</h3>
            <p>اختبار التوافق مع أحجام ورق مختلفة</p>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-outline-primary test-btn w-100" onclick="testPaperSize('58mm')">
                        <i class="fas fa-scroll"></i> ورق 58mm
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-secondary test-btn w-100" onclick="testPaperSize('80mm')">
                        <i class="fas fa-scroll"></i> ورق 80mm
                    </button>
                </div>
            </div>
            <div id="paperTestResult" class="result-area"></div>
        </div>

        <!-- نتائج الاختبار الشامل -->
        <div class="test-section">
            <h3><i class="fas fa-chart-line"></i> نتائج الاختبار الشامل</h3>
            <button class="btn btn-danger test-btn" onclick="runFullTest()">
                <i class="fas fa-rocket"></i> تشغيل جميع الاختبارات
            </button>
            <div id="fullTestResult" class="result-area"></div>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    
    <script>
        // متغيرات عامة للاختبار
        let testResults = {
            basic: false,
            invoice: false,
            barcode: false,
            paper: false
        };

        // وظائف الاختبار
        async function runBasicPrinterTest() {
            const resultDiv = document.getElementById('basicTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري تشغيل الاختبار الأساسي...';
            
            try {
                await testThermalPrinter();
                testResults.basic = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = '✅ نجح الاختبار الأساسي!\nتم إرسال صفحة اختبار للطابعة.';
            } catch (error) {
                testResults.basic = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل الاختبار الأساسي:\n' + error.message;
            }
        }

        function testSalesInvoice() {
            const resultDiv = document.getElementById('invoiceTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري إنشاء فاتورة تجريبية...';
            
            try {
                // إنشاء بيانات فاتورة تجريبية
                const testSale = {
                    id: 'TEST-' + Date.now(),
                    invoiceNumber: 'INV-TEST-001',
                    date: new Date().toISOString(),
                    customerId: 'guest',
                    items: [
                        {
                            productId: 'test1',
                            quantity: 2,
                            price: 25.50
                        },
                        {
                            productId: 'test2', 
                            quantity: 1,
                            price: 15.00
                        }
                    ],
                    subtotal: 66.00,
                    tax: 6.60,
                    discount: 5.00,
                    total: 67.60,
                    paymentMethod: 'cash',
                    amountPaid: 70.00
                };
                
                // محاكاة قاعدة البيانات
                window.db = {
                    getSale: () => testSale,
                    getCustomer: () => null,
                    getSettings: () => ({
                        company: {
                            companyName: 'متجر تكنوفلاش التجريبي',
                            phone: '0*********0',
                            address: 'القاهرة، مصر',
                            taxNumber: '*********'
                        }
                    }),
                    getProduct: (id) => ({
                        name: id === 'test1' ? 'منتج تجريبي 1' : 'منتج تجريبي 2'
                    })
                };
                
                printThermalInvoice(testSale);
                testResults.invoice = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = '✅ تم إرسال الفاتورة التجريبية للطباعة!';
                
            } catch (error) {
                testResults.invoice = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل في طباعة الفاتورة:\n' + error.message;
            }
        }

        function testSingleBarcodeLabel() {
            const resultDiv = document.getElementById('barcodeTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري إنشاء ملصق باركود واحد...';
            
            try {
                const testProduct = {
                    id: 'TEST001',
                    name: 'منتج تجريبي للاختبار',
                    barcode: 'TEST*********',
                    price: 99.99,
                    code: 'T001'
                };
                
                printSmallBarcodeLabel(testProduct);
                testResults.barcode = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = '✅ تم إرسال ملصق الباركود للطباعة!';
                
            } catch (error) {
                testResults.barcode = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل في طباعة ملصق الباركود:\n' + error.message;
            }
        }

        function testMultipleBarcodeLabels() {
            const resultDiv = document.getElementById('barcodeTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري إنشاء ملصقات متعددة...';
            
            try {
                const testProducts = ['TEST001', 'TEST002', 'TEST003'];
                
                // محاكاة قاعدة البيانات
                window.db = {
                    getProduct: (id) => ({
                        id: id,
                        name: `منتج تجريبي ${id}`,
                        barcode: `${id}123456`,
                        price: Math.random() * 100 + 10,
                        code: id
                    })
                };
                
                printMultipleBarcodeLabels(testProducts);
                testResults.barcode = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = '✅ تم إرسال الملصقات المتعددة للطباعة!';
                
            } catch (error) {
                testResults.barcode = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل في طباعة الملصقات المتعددة:\n' + error.message;
            }
        }

        function testCustomSizeLabel() {
            const resultDiv = document.getElementById('barcodeTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري إنشاء ملصق بحجم مخصص...';
            
            try {
                const testProduct = {
                    id: 'CUSTOM001',
                    name: 'منتج مخصص للاختبار',
                    barcode: 'CUSTOM*********',
                    price: 149.99,
                    code: 'C001'
                };
                
                const customOptions = {
                    width: 25,
                    barcodeHeight: 35,
                    barcodeWidth: 1,
                    showPrice: true,
                    showCode: true
                };
                
                printCustomSizeBarcodeLabel(testProduct, customOptions);
                testResults.barcode = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = '✅ تم إرسال الملصق المخصص للطباعة!';
                
            } catch (error) {
                testResults.barcode = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل في طباعة الملصق المخصص:\n' + error.message;
            }
        }

        function testPaperSize(size) {
            const resultDiv = document.getElementById('paperTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = `جاري اختبار ورق ${size}...`;
            
            try {
                // إنشاء محتوى اختبار للحجم المحدد
                let testContent = '';
                testContent += ESC_POS_COMMANDS.INIT;
                testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
                testContent += ESC_POS_COMMANDS.BOLD_ON;
                testContent += `اختبار ورق ${size}\n`;
                testContent += ESC_POS_COMMANDS.BOLD_OFF;
                testContent += repeatChar('=', size === '58mm' ? 32 : 48) + '\n';
                testContent += ESC_POS_COMMANDS.ALIGN_LEFT;
                testContent += `حجم الورق: ${size}\n`;
                testContent += `العرض: ${size === '58mm' ? '32 حرف' : '48 حرف'}\n`;
                testContent += `التاريخ: ${new Date().toLocaleDateString('ar-SA')}\n`;
                testContent += repeatChar('-', size === '58mm' ? 32 : 48) + '\n';
                testContent += 'هذا نص تجريبي لاختبار العرض\n';
                testContent += '*********0*********0*********0*********0\n';
                testContent += repeatChar('=', size === '58mm' ? 32 : 48) + '\n';
                testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
                testContent += 'انتهى الاختبار\n';
                testContent += ESC_POS_COMMANDS.FEED_LINES(3);
                testContent += ESC_POS_COMMANDS.CUT_FEED;
                
                sendToThermalPrinter(testContent, `اختبار ورق ${size}`, {
                    paperSize: size,
                    fontSize: '12px'
                });
                
                testResults.paper = true;
                resultDiv.className = 'result-area status-success';
                resultDiv.textContent = `✅ تم إرسال اختبار ورق ${size} للطباعة!`;
                
            } catch (error) {
                testResults.paper = false;
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = `❌ فشل في اختبار ورق ${size}:\n` + error.message;
            }
        }

        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.className = 'result-area status-info';
            resultDiv.textContent = 'جاري تشغيل جميع الاختبارات...\n';
            
            try {
                // تشغيل جميع الاختبارات بالتتابع
                await runBasicPrinterTest();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                testSalesInvoice();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                testSingleBarcodeLabel();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                testPaperSize('58mm');
                
                // عرض النتائج النهائية
                const passedTests = Object.values(testResults).filter(result => result).length;
                const totalTests = Object.keys(testResults).length;
                
                resultDiv.className = passedTests === totalTests ? 'result-area status-success' : 'result-area status-error';
                resultDiv.textContent = `📊 نتائج الاختبار الشامل:\n` +
                    `✅ نجح: ${passedTests} من ${totalTests} اختبارات\n` +
                    `🔧 الاختبار الأساسي: ${testResults.basic ? 'نجح' : 'فشل'}\n` +
                    `🧾 فاتورة المبيعات: ${testResults.invoice ? 'نجح' : 'فشل'}\n` +
                    `🏷️ ملصقات الباركود: ${testResults.barcode ? 'نجح' : 'فشل'}\n` +
                    `📄 أحجام الورق: ${testResults.paper ? 'نجح' : 'فشل'}\n\n` +
                    (passedTests === totalTests ? 
                        '🎉 تهانينا! جميع الاختبارات نجحت.' : 
                        '⚠️ بعض الاختبارات فشلت. يرجى التحقق من إعدادات الطابعة.');
                
            } catch (error) {
                resultDiv.className = 'result-area status-error';
                resultDiv.textContent = '❌ فشل في تشغيل الاختبار الشامل:\n' + error.message;
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة اختبار الطباعة الحرارية');
            
            // التحقق من توفر الوظائف المطلوبة
            if (typeof testThermalPrinter === 'undefined') {
                console.error('❌ وظيفة testThermalPrinter غير متوفرة');
            }
            if (typeof printThermalInvoice === 'undefined') {
                console.error('❌ وظيفة printThermalInvoice غير متوفرة');
            }
            if (typeof printSmallBarcodeLabel === 'undefined') {
                console.error('❌ وظيفة printSmallBarcodeLabel غير متوفرة');
            }
        });
    </script>
</body>
</html>
