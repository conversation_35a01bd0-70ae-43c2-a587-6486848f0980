<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة الباركود - تكنوفلاش</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            color: white;
        }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-info { background: #17a2b8; }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .barcode-display {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            margin: 15px 0;
        }
        .result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖨️ اختبار طباعة الباركود</h1>
        
        <div class="test-section">
            <h2>إنشاء باركود للاختبار</h2>
            <input type="text" id="testText" class="form-control" value="TEST123" placeholder="أدخل النص">
            <button class="btn btn-primary" onclick="generateTestBarcode()">توليد باركود</button>
            <div id="barcodeDisplay" class="barcode-display"></div>
        </div>

        <div class="test-section">
            <h2>اختبار الطباعة</h2>
            <button class="btn btn-success" onclick="testPrint()" id="printBtn" disabled>
                🖨️ اختبار الطباعة
            </button>
            <button class="btn btn-info" onclick="testPrintMultiple()">
                🖨️ اختبار طباعة متعددة
            </button>
            <div id="printResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>معلومات المتصفح</h2>
            <div id="browserInfo">
                <p><strong>المتصفح:</strong> <span id="browser"></span></p>
                <p><strong>دعم الطباعة:</strong> <span id="printSupport"></span></p>
                <p><strong>دعم Canvas:</strong> <span id="canvasSupport"></span></p>
            </div>
        </div>
    </div>

    <script src="barcode-generator.js"></script>
    <script>
        let generator;
        let testCanvas;

        window.addEventListener('DOMContentLoaded', function() {
            initializeTest();
        });

        function initializeTest() {
            try {
                generator = new BarcodeGenerator();
                showResult('تم تحميل مولد الباركود بنجاح', 'success');
            } catch (error) {
                showResult('خطأ في تحميل مولد الباركود: ' + error.message, 'error');
                return;
            }

            // معلومات المتصفح
            document.getElementById('browser').textContent = navigator.userAgent.split(' ')[0];
            
            // دعم الطباعة
            const printSupported = window.print !== undefined;
            document.getElementById('printSupport').textContent = printSupported ? 'مدعوم ✅' : 'غير مدعوم ❌';
            document.getElementById('printSupport').style.color = printSupported ? 'green' : 'red';
            
            // دعم Canvas
            const canvas = document.createElement('canvas');
            const canvasSupported = !!(canvas.getContext && canvas.getContext('2d'));
            document.getElementById('canvasSupport').textContent = canvasSupported ? 'مدعوم ✅' : 'غير مدعوم ❌';
            document.getElementById('canvasSupport').style.color = canvasSupported ? 'green' : 'red';
        }

        function generateTestBarcode() {
            const text = document.getElementById('testText').value;
            const displayDiv = document.getElementById('barcodeDisplay');
            
            if (!text) {
                showResult('يرجى إدخال نص', 'error');
                return;
            }

            try {
                const pattern = generator.generateCode128(text);
                const canvas = generator.renderToCanvas(pattern, {
                    width: 300,
                    height: 100,
                    showText: true,
                    text: text,
                    fontSize: 12
                });

                testCanvas = canvas;
                displayDiv.innerHTML = '';
                displayDiv.appendChild(canvas);
                
                document.getElementById('printBtn').disabled = false;
                showResult('تم توليد الباركود بنجاح', 'success');
            } catch (error) {
                showResult('خطأ في توليد الباركود: ' + error.message, 'error');
            }
        }

        function testPrint() {
            if (!testCanvas) {
                showResult('يرجى توليد باركود أولاً', 'error');
                return;
            }

            try {
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>اختبار طباعة الباركود</title>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    text-align: center; 
                                    padding: 20px;
                                    direction: rtl;
                                }
                                .barcode-label {
                                    border: 2px solid #000;
                                    padding: 20px;
                                    margin: 20px auto;
                                    display: inline-block;
                                    background: white;
                                }
                                h1 { color: #333; }
                                .test-info { 
                                    color: #666; 
                                    font-size: 14px;
                                    margin: 10px 0;
                                }
                                @media print {
                                    body { margin: 0; }
                                    .barcode-label { 
                                        border: 2px solid #000;
                                        page-break-inside: avoid;
                                    }
                                }
                            </style>
                        </head>
                        <body>
                            <h1>اختبار طباعة الباركود - تكنوفلاش</h1>
                            <div class="barcode-label">
                                <div class="test-info">باركود اختبار</div>
                                <div id="barcodeContainer"></div>
                                <div class="test-info">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>
                            </div>
                        </body>
                    </html>
                `);
                
                printWindow.document.getElementById('barcodeContainer').appendChild(testCanvas.cloneNode(true));
                printWindow.document.close();
                
                printWindow.onload = function() {
                    setTimeout(() => {
                        printWindow.print();
                        showResult('تم إرسال الباركود للطباعة بنجاح! تحقق من طابعتك.', 'success');
                        setTimeout(() => {
                            printWindow.close();
                        }, 2000);
                    }, 500);
                };
                
                setTimeout(() => {
                    if (printWindow.document.readyState === 'complete') {
                        printWindow.print();
                        showResult('تم إرسال الباركود للطباعة', 'success');
                    }
                }, 1000);
                
            } catch (error) {
                showResult('خطأ في الطباعة: ' + error.message, 'error');
            }
        }

        function testPrintMultiple() {
            try {
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>اختبار طباعة متعددة</title>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    padding: 20px;
                                    direction: rtl;
                                }
                                .barcode-grid {
                                    display: grid;
                                    grid-template-columns: repeat(2, 1fr);
                                    gap: 20px;
                                    margin: 20px 0;
                                }
                                .barcode-label {
                                    border: 1px solid #000;
                                    padding: 15px;
                                    text-align: center;
                                    background: white;
                                    page-break-inside: avoid;
                                }
                                @media print {
                                    body { margin: 0; }
                                    .barcode-label { border: 1px solid #000; }
                                }
                            </style>
                        </head>
                        <body>
                            <h1 style="text-align: center;">اختبار طباعة متعددة - تكنوفلاش</h1>
                            <div class="barcode-grid" id="barcodeGrid"></div>
                        </body>
                    </html>
                `);
                
                const grid = printWindow.document.getElementById('barcodeGrid');
                const testItems = ['ITEM001', 'ITEM002', 'ITEM003', 'ITEM004'];
                
                testItems.forEach(item => {
                    const pattern = generator.generateCode128(item);
                    const canvas = generator.renderToCanvas(pattern, {
                        width: 200,
                        height: 80,
                        showText: true,
                        text: item,
                        fontSize: 10
                    });
                    
                    const labelDiv = printWindow.document.createElement('div');
                    labelDiv.className = 'barcode-label';
                    labelDiv.innerHTML = `
                        <div style="font-weight: bold; margin-bottom: 10px;">منتج ${item}</div>
                        <div class="barcode-container"></div>
                        <div style="font-family: monospace; font-size: 12px; margin-top: 5px;">${item}</div>
                        <div style="color: #666; margin-top: 5px;">100.00 ج.م</div>
                    `;
                    
                    labelDiv.querySelector('.barcode-container').appendChild(canvas);
                    grid.appendChild(labelDiv);
                });
                
                printWindow.document.close();
                
                printWindow.onload = function() {
                    setTimeout(() => {
                        printWindow.print();
                        showResult('تم إرسال الباركود المتعددة للطباعة بنجاح!', 'success');
                        setTimeout(() => {
                            printWindow.close();
                        }, 2000);
                    }, 1000);
                };
                
            } catch (error) {
                showResult('خطأ في الطباعة المتعددة: ' + error.message, 'error');
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('printResult');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
        }
    </script>
</body>
</html>
