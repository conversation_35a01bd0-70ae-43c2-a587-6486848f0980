/**
 * الملف الرئيسي لتطبيق تكنوفلاش POS
 * يحتوي على الوظائف الأساسية والتحكم في التطبيق
 */

class TechnoFlashApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'dashboard';
        this.isLoggedIn = false;
        
        // عناصر DOM الرئيسية
        this.loginScreen = document.getElementById('loginScreen');
        this.mainApp = document.getElementById('mainApp');
        this.loginForm = document.getElementById('loginForm');
        this.mainContent = document.getElementById('mainContent');
        this.sidebar = document.getElementById('sidebar');
        
        // تهيئة التطبيق
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        this.setupEventListeners();
        this.updateDateTime();
        this.checkLoginStatus();
        
        // تحديث الوقت كل ثانية
        setInterval(() => this.updateDateTime(), 1000);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تسجيل الدخول
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        
        // تسجيل الخروج
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());
        
        // تبديل الثيم
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        
        // التنقل في القائمة الجانبية
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });
        
        // النوافذ المنبثقة
        document.getElementById('confirmCancel').addEventListener('click', () => this.hideModal('confirmModal'));
        document.getElementById('confirmOk').addEventListener('click', () => this.handleConfirm());
        document.getElementById('alertOk').addEventListener('click', () => this.hideModal('alertModal'));
        
        // إغلاق النوافذ المنبثقة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target.id);
            }
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * التحقق من حالة تسجيل الدخول
     */
    checkLoginStatus() {
        const loginStatus = localStorage.getItem('technoflash_login');
        if (loginStatus === 'true') {
            this.showMainApp();
        } else {
            this.showLoginScreen();
        }
    }

    /**
     * معالجة تسجيل الدخول
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const password = document.getElementById('password').value;
        
        if (!password) {
            this.showAlert('خطأ', 'يرجى إدخال كلمة المرور');
            return;
        }
        
        // إظهار شريط التحميل
        this.showLoading();
        
        // محاكاة تأخير التحقق
        setTimeout(() => {
            if (db.verifyPassword(password)) {
                this.isLoggedIn = true;
                localStorage.setItem('technoflash_login', 'true');
                this.showMainApp();
                this.showNotification('تم تسجيل الدخول بنجاح', 'success');
            } else {
                this.showAlert('خطأ في تسجيل الدخول', 'كلمة المرور غير صحيحة');
            }
            
            this.hideLoading();
            document.getElementById('password').value = '';
        }, 1000);
    }

    /**
     * تسجيل الخروج
     */
    logout() {
        this.showConfirm('تسجيل الخروج', 'هل أنت متأكد من تسجيل الخروج؟', () => {
            this.isLoggedIn = false;
            localStorage.removeItem('technoflash_login');
            this.showLoginScreen();
            this.showNotification('تم تسجيل الخروج بنجاح', 'info');
        });
    }

    /**
     * إظهار شاشة تسجيل الدخول
     */
    showLoginScreen() {
        this.loginScreen.classList.remove('hidden');
        this.mainApp.classList.add('hidden');
        document.getElementById('password').focus();
    }

    /**
     * تحميل وتطبيق الإعدادات
     */
    loadAndApplySettings() {
        try {
            // تحميل الإعدادات من قاعدة البيانات
            const settings = db.getStoredSettings() || db.getSettings();

            if (settings) {
                console.log('تم تحميل الإعدادات:', settings);

                // تطبيق الإعدادات العامة
                if (settings.general) {
                    // تطبيق الوضع الليلي
                    if (settings.general.darkMode) {
                        document.body.classList.add('dark-mode');
                    } else {
                        document.body.classList.remove('dark-mode');
                    }

                    // تطبيق نوع الأرقام
                    document.documentElement.setAttribute('data-number-type', settings.general.numberType || 'arabic');

                    // تطبيق العملة
                    document.documentElement.setAttribute('data-currency', settings.general.currency || 'EGP');

                    // تطبيق اللغة
                    document.documentElement.setAttribute('lang', settings.general.language || 'ar');
                    document.documentElement.setAttribute('dir', 'rtl');
                }

                // تطبيق إعدادات الشركة على العناصر العامة
                if (settings.company && settings.company.companyName) {
                    // تحديث اسم الشركة في الرأسية إذا وجد
                    const companyNameElements = document.querySelectorAll('.company-name');
                    companyNameElements.forEach(element => {
                        element.textContent = settings.company.companyName;
                    });

                    // تحديث عنوان الصفحة
                    document.title = `${settings.company.companyName} - تكنوفلاش POS`;
                }

                // إشعار باقي أجزاء التطبيق بتحديث الإعدادات
                window.dispatchEvent(new CustomEvent('settingsUpdated', {
                    detail: settings
                }));

                console.log('تم تطبيق الإعدادات بنجاح');
            } else {
                console.log('لم يتم العثور على إعدادات محفوظة، سيتم استخدام الإعدادات الافتراضية');
            }
        } catch (error) {
            console.error('خطأ في تحميل وتطبيق الإعدادات:', error);
        }
    }

    /**
     * إظهار التطبيق الرئيسي
     */
    showMainApp() {
        this.loginScreen.classList.add('hidden');
        this.mainApp.classList.remove('hidden');

        // تحميل وتطبيق الإعدادات عند بدء التطبيق
        this.loadAndApplySettings();

        this.loadPage('dashboard');
    }

    /**
     * معالجة التنقل
     */
    handleNavigation(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const page = link.getAttribute('data-page');
        
        if (page) {
            // إزالة الفئة النشطة من جميع الروابط
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // إضافة الفئة النشطة للرابط الحالي
            link.parentElement.classList.add('active');
            
            // تحميل الصفحة
            this.loadPage(page);
        }
    }

    /**
     * تحميل صفحة
     */
    async loadPage(pageName) {
        this.showLoading();
        
        try {
            this.currentPage = pageName;
            
            // تحميل محتوى الصفحة
            switch (pageName) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'sales':
                    await this.loadSales();
                    break;
                case 'products':
                    await this.loadProducts();
                    break;
                case 'customers':
                    await this.loadCustomers();
                    break;
                case 'suppliers':
                    await this.loadSuppliers();
                    break;
                case 'purchases':
                    await this.loadPurchases();
                    break;
                case 'debts':
                    await this.loadDebts();
                    break;
                case 'reports':
                    await this.loadReports();
                    break;
                case 'settings':
                    await this.loadSettings();
                    break;
                case 'backup':
                    await this.loadBackup();
                    break;
                default:
                    this.mainContent.innerHTML = '<div class="error-page"><h2>الصفحة غير موجودة</h2></div>';
            }
        } catch (error) {
            console.error('خطأ في تحميل الصفحة:', error);
            this.showAlert('خطأ', 'حدث خطأ في تحميل الصفحة');
        }
        
        this.hideLoading();
    }

    /**
     * تحميل لوحة المعلومات
     */
    async loadDashboard() {
        if (typeof loadDashboard === 'function') {
            await loadDashboard();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h1>
                </div>
                <div class="dashboard-loading">
                    <p>جاري تحميل لوحة المعلومات...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة المبيعات
     */
    async loadSales() {
        if (typeof loadSales === 'function') {
            await loadSales();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-shopping-cart"></i> المبيعات</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة المبيعات...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة المنتجات
     */
    async loadProducts() {
        if (typeof loadProducts === 'function') {
            await loadProducts();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-box"></i> المنتجات</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة المنتجات...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة العملاء
     */
    async loadCustomers() {
        if (typeof loadCustomers === 'function') {
            await loadCustomers();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-users"></i> العملاء</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة العملاء...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة الموردين
     */
    async loadSuppliers() {
        if (typeof window.loadSuppliers === 'function') {
            await window.loadSuppliers();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-truck"></i> الموردين</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة الموردين...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة المشتريات
     */
    async loadPurchases() {
        if (typeof window.loadPurchases === 'function') {
            await window.loadPurchases();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-shopping-bag"></i> المشتريات</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة المشتريات...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة الديون
     */
    async loadDebts() {
        if (typeof window.loadDebts === 'function') {
            await window.loadDebts();
        } else {
            this.mainContent.innerHTML = `
                <div class="page-header">
                    <h1><i class="fas fa-money-bill-wave"></i> الديون والمدفوعات</h1>
                </div>
                <div class="page-loading">
                    <p>جاري تحميل صفحة الديون والمدفوعات...</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة التقارير
     */
    async loadReports() {
        try {
            await loadReports();
        } catch (error) {
            console.error('خطأ في تحميل صفحة التقارير:', error);
            this.mainContent.innerHTML = `
                <div class="error-page">
                    <h2>خطأ في تحميل التقارير</h2>
                    <p>حدث خطأ أثناء تحميل صفحة التقارير. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة الإعدادات
     */
    async loadSettings() {
        try {
            await loadSettings();
        } catch (error) {
            console.error('خطأ في تحميل صفحة الإعدادات:', error);
            this.mainContent.innerHTML = `
                <div class="error-page">
                    <h2>خطأ في تحميل الإعدادات</h2>
                    <p>حدث خطأ أثناء تحميل صفحة الإعدادات. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        }
    }

    /**
     * تحميل صفحة النسخ الاحتياطي
     */
    async loadBackup() {
        try {
            await loadBackup();
        } catch (error) {
            console.error('خطأ في تحميل صفحة النسخ الاحتياطي:', error);
            this.mainContent.innerHTML = `
                <div class="error-page">
                    <h2>خطأ في تحميل النسخ الاحتياطي</h2>
                    <p>حدث خطأ أثناء تحميل صفحة النسخ الاحتياطي. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        }
    }

    /**
     * تبديل الثيم
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        
        // تحديث أيقونة الثيم
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        // حفظ الثيم في الإعدادات
        db.updateSettings({ theme: newTheme });
        
        this.showNotification(`تم التبديل إلى الثيم ${newTheme === 'dark' ? 'الداكن' : 'المضيء'}`, 'info');
    }

    /**
     * تحديث التاريخ والوقت
     */
    updateDateTime() {
        const now = new Date();
        const timeElement = document.getElementById('currentTime');
        const dateElement = document.getElementById('currentDate');
        
        if (timeElement && dateElement) {
            // تنسيق الوقت
            const hours = db.toArabicNumerals(now.getHours().toString().padStart(2, '0'));
            const minutes = db.toArabicNumerals(now.getMinutes().toString().padStart(2, '0'));
            const seconds = db.toArabicNumerals(now.getSeconds().toString().padStart(2, '0'));
            timeElement.textContent = `${hours}:${minutes}:${seconds}`;
            
            // تنسيق التاريخ
            dateElement.textContent = db.formatDate(now);
        }
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // Ctrl + L للخروج
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            this.logout();
        }
        
        // Ctrl + T لتبديل الثيم
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            this.toggleTheme();
        }
        
        // ESC لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal:not(.hidden)');
            modals.forEach(modal => this.hideModal(modal.id));
        }
    }

    /**
     * إظهار شريط التحميل
     */
    showLoading() {
        document.getElementById('loadingBar').classList.remove('hidden');
    }

    /**
     * إخفاء شريط التحميل
     */
    hideLoading() {
        document.getElementById('loadingBar').classList.add('hidden');
    }

    /**
     * إظهار إشعار
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        document.getElementById('notifications').appendChild(notification);
        
        // إزالة الإشعار بعد المدة المحددة
        setTimeout(() => {
            notification.remove();
        }, duration);
    }

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * إظهار نافذة تأكيد
     */
    showConfirm(title, message, callback) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        this.confirmCallback = callback;
        this.showModal('confirmModal');
    }

    /**
     * معالجة التأكيد
     */
    handleConfirm() {
        if (this.confirmCallback) {
            this.confirmCallback();
            this.confirmCallback = null;
        }
        this.hideModal('confirmModal');
    }

    /**
     * إظهار نافذة تنبيه
     */
    showAlert(title, message) {
        document.getElementById('alertTitle').textContent = title;
        document.getElementById('alertMessage').textContent = message;
        this.showModal('alertModal');
    }

    /**
     * إظهار نافذة منبثقة
     */
    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }

    /**
     * إخفاء نافذة منبثقة
     */
    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }
}

// متغير عالمي للتطبيق
let app;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app = new TechnoFlashApp();
    window.app = app;

    // تحميل وتطبيق الإعدادات عند بدء التطبيق
    setTimeout(() => {
        if (window.app) {
            window.app.loadAndApplySettings();
        }
    }, 100);
});
