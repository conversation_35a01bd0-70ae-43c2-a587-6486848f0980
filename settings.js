/**
 * إدارة الإعدادات - تكنوفلاش POS
 * نظام إعدادات محسن ومبسط
 */

// متغير الإعدادات الحالية
let currentSettings = {};

// الإعدادات الافتراضية
const DEFAULT_SETTINGS = {
    general: {
        language: 'ar',
        currency: 'EGP',
        dateType: 'gregorian',
        numberType: 'arabic',
        darkMode: false,
        soundEffects: true
    },
    company: {
        companyName: '',
        commercialRegister: '',
        taxNumber: '',
        phone: '',
        address: '',
        email: '',
        website: '',
        logo: ''
    },
    pos: {
        defaultPaymentMethod: 'cash',
        invoiceNumberLength: 6,
        autoSave: true,
        autoPrint: false,
        barcodeScanner: false,
        lowStockAlert: true,
        lowStockThreshold: 10
    },
    invoice: {
        paperSize: 'A4',
        customWidth: 210,
        customHeight: 297,
        orientation: 'portrait',
        showLogo: true,
        showCompanyInfo: true,
        showCustomerInfo: true,
        showItemCodes: true,
        showItemImages: false,
        footerText: 'شكراً لتعاملكم معنا',
        headerColor: '#2c3e50',
        accentColor: '#3498db'
    },
    taxes: {
        enableTax: false,
        taxRates: [
            { name: 'ضريبة القيمة المضافة', rate: 14, isDefault: true }
        ]
    },
    notifications: {
        enableNotifications: true,
        lowStockAlerts: true,
        salesAlerts: false,
        systemAlerts: true,
        soundEnabled: true
    },
    backup: {
        autoBackup: false,
        backupFrequency: 'daily',
        backupTime: '02:00',
        maxBackups: 7
    }
};

/**
 * تحميل صفحة الإعدادات
 */
async function loadSettings() {
    try {
        console.log('🚀 بدء تحميل صفحة الإعدادات...');

        // تحميل الإعدادات الحالية
        loadCurrentSettings();

        // التحقق من وجود العنصر الأساسي
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) {
            throw new Error('عنصر mainContent غير موجود');
        }

        // إنشاء محتوى صفحة الإعدادات
        mainContent.innerHTML = createSettingsHTML();

        // تهيئة الصفحة
        setTimeout(() => {
            initializeSettingsPage();
        }, 100);

        console.log('✅ تم تحميل صفحة الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الإعدادات:', error);
        throw error;
    }
}

/**
 * إنشاء HTML لصفحة الإعدادات
 */
function createSettingsHTML() {
    return `
        <div class="settings-container">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i> حفظ جميع الإعدادات
                    </button>
                    <button class="btn btn-warning" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i> استعادة الافتراضية
                    </button>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="settings-tabs">
                <button class="tab-btn active" data-tab="general">
                    <i class="fas fa-globe"></i> عام
                </button>
                <button class="tab-btn" data-tab="company">
                    <i class="fas fa-building"></i> بيانات الشركة
                </button>
                <button class="tab-btn" data-tab="pos">
                    <i class="fas fa-cash-register"></i> نقطة البيع
                </button>
                <button class="tab-btn" data-tab="invoice">
                    <i class="fas fa-file-invoice"></i> الفاتورة
                </button>
                <button class="tab-btn" data-tab="taxes">
                    <i class="fas fa-percentage"></i> الضرائب
                </button>
                <button class="tab-btn" data-tab="notifications">
                    <i class="fas fa-bell"></i> التنبيهات
                </button>
                <button class="tab-btn" data-tab="backup">
                    <i class="fas fa-database"></i> النسخ الاحتياطي
                </button>
            </div>

            <!-- محتوى التبويبات -->
            <div class="tab-content">
                ${createGeneralSettingsTab()}
                ${createCompanySettingsTab()}
                ${createPosSettingsTab()}
                ${createInvoiceSettingsTab()}
                ${createTaxesSettingsTab()}
                ${createNotificationsSettingsTab()}
                ${createBackupSettingsTab()}
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب الإعدادات العامة
 */
function createGeneralSettingsTab() {
    return `
        <div id="general" class="tab-pane active">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-globe"></i> الإعدادات العامة</h3>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-language"></i> اللغة</label>
                                <select class="form-control" name="language">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-coins"></i> العملة</label>
                                <select class="form-control" name="currency">
                                    <option value="EGP">جنيه مصري (ج.م)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-calendar"></i> نوع التاريخ</label>
                                <select class="form-control" name="dateType">
                                    <option value="gregorian">ميلادي</option>
                                    <option value="hijri">هجري</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-sort-numeric-up"></i> نوع الأرقام</label>
                                <select class="form-control" name="numberType">
                                    <option value="arabic">عربية (١٢٣)</option>
                                    <option value="english">إنجليزية (123)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="darkMode" id="darkMode">
                                    <label class="form-check-label" for="darkMode">
                                        <i class="fas fa-moon"></i> الوضع الليلي
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="soundEffects" id="soundEffects">
                                    <label class="form-check-label" for="soundEffects">
                                        <i class="fas fa-volume-up"></i> المؤثرات الصوتية
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب بيانات الشركة
 */
function createCompanySettingsTab() {
    return `
        <div id="company" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-building"></i> بيانات الشركة</h3>
                </div>
                <div class="card-body">
                    <form id="companySettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-building"></i> اسم الشركة</label>
                                <input type="text" class="form-control" name="companyName" placeholder="اسم الشركة">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-id-card"></i> السجل التجاري</label>
                                <input type="text" class="form-control" name="commercialRegister" placeholder="رقم السجل التجاري">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-percentage"></i> الرقم الضريبي</label>
                                <input type="text" class="form-control" name="taxNumber" placeholder="الرقم الضريبي">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-phone"></i> الهاتف</label>
                                <input type="tel" class="form-control" name="phone" placeholder="رقم الهاتف">
                            </div>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-map-marker-alt"></i> العنوان</label>
                            <textarea class="form-control" name="address" rows="3" placeholder="عنوان الشركة"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-envelope"></i> البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" placeholder="البريد الإلكتروني">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-globe"></i> الموقع الإلكتروني</label>
                                <input type="url" class="form-control" name="website" placeholder="الموقع الإلكتروني">
                            </div>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-image"></i> شعار الشركة</label>
                            <input type="file" class="form-control" name="logo" accept="image/*">
                            <small class="form-text text-muted">يُفضل أن يكون الشعار بصيغة PNG أو JPG</small>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب إعدادات نقطة البيع
 */
function createPosSettingsTab() {
    return `
        <div id="pos" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-cash-register"></i> إعدادات نقطة البيع</h3>
                </div>
                <div class="card-body">
                    <form id="posSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-credit-card"></i> طريقة الدفع الافتراضية</label>
                                <select class="form-control" name="defaultPaymentMethod">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="bank">تحويل بنكي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-hashtag"></i> طول رقم الفاتورة</label>
                                <input type="number" class="form-control" name="invoiceNumberLength" min="4" max="10" value="6">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-exclamation-triangle"></i> حد التنبيه للمخزون المنخفض</label>
                                <input type="number" class="form-control" name="lowStockThreshold" min="1" max="100" value="10">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="autoSave" id="autoSave">
                                    <label class="form-check-label" for="autoSave">
                                        <i class="fas fa-save"></i> الحفظ التلقائي
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="autoPrint" id="autoPrint">
                                    <label class="form-check-label" for="autoPrint">
                                        <i class="fas fa-print"></i> الطباعة التلقائية
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="barcodeScanner" id="barcodeScanner">
                                    <label class="form-check-label" for="barcodeScanner">
                                        <i class="fas fa-barcode"></i> ماسح الباركود
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="lowStockAlert" id="lowStockAlert">
                                    <label class="form-check-label" for="lowStockAlert">
                                        <i class="fas fa-bell"></i> تنبيه المخزون المنخفض
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب إعدادات الفاتورة
 */
function createInvoiceSettingsTab() {
    return `
        <div id="invoice" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-file-invoice"></i> إعدادات الفاتورة</h3>
                </div>
                <div class="card-body">
                    <form id="invoiceSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-file"></i> حجم الورق</label>
                                <select class="form-control" name="paperSize">
                                    <option value="A4">A4</option>
                                    <option value="A5">A5</option>
                                    <option value="thermal">حراري 80mm</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-arrows-alt"></i> اتجاه الصفحة</label>
                                <select class="form-control" name="orientation">
                                    <option value="portrait">عمودي</option>
                                    <option value="landscape">أفقي</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row" id="customSizeRow" style="display: none;">
                            <div class="form-group">
                                <label><i class="fas fa-ruler-horizontal"></i> العرض (مم)</label>
                                <input type="number" class="form-control" name="customWidth" value="210">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-ruler-vertical"></i> الارتفاع (مم)</label>
                                <input type="number" class="form-control" name="customHeight" value="297">
                            </div>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-comment"></i> نص التذييل</label>
                            <textarea class="form-control" name="footerText" rows="2" placeholder="شكراً لتعاملكم معنا"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-palette"></i> لون الرأسية</label>
                                <input type="color" class="form-control" name="headerColor" value="#2c3e50">
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-palette"></i> اللون المميز</label>
                                <input type="color" class="form-control" name="accentColor" value="#3498db">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="showLogo" id="showLogo">
                                    <label class="form-check-label" for="showLogo">
                                        <i class="fas fa-image"></i> إظهار الشعار
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="showCompanyInfo" id="showCompanyInfo">
                                    <label class="form-check-label" for="showCompanyInfo">
                                        <i class="fas fa-building"></i> إظهار بيانات الشركة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="showCustomerInfo" id="showCustomerInfo">
                                    <label class="form-check-label" for="showCustomerInfo">
                                        <i class="fas fa-user"></i> إظهار بيانات العميل
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="showItemCodes" id="showItemCodes">
                                    <label class="form-check-label" for="showItemCodes">
                                        <i class="fas fa-barcode"></i> إظهار أكواد المنتجات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب إعدادات الضرائب
 */
function createTaxesSettingsTab() {
    return `
        <div id="taxes" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-percentage"></i> إعدادات الضرائب</h3>
                </div>
                <div class="card-body">
                    <form id="taxesSettingsForm">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="enableTax" id="enableTax">
                                <label class="form-check-label" for="enableTax">
                                    <i class="fas fa-percentage"></i> تفعيل الضرائب
                                </label>
                            </div>
                        </div>

                        <div id="taxRatesSection" style="display: none;">
                            <h5>معدلات الضرائب</h5>
                            <div class="tax-rates-list">
                                <div class="tax-rate-item">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>اسم الضريبة</label>
                                            <input type="text" class="form-control" value="ضريبة القيمة المضافة" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label>المعدل (%)</label>
                                            <input type="number" class="form-control" value="14" min="0" max="100" step="0.01">
                                        </div>
                                        <div class="form-group">
                                            <label>افتراضي</label>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" checked disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب إعدادات التنبيهات
 */
function createNotificationsSettingsTab() {
    return `
        <div id="notifications" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-bell"></i> إعدادات التنبيهات</h3>
                </div>
                <div class="card-body">
                    <form id="notificationsSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="enableNotifications" id="enableNotifications">
                                    <label class="form-check-label" for="enableNotifications">
                                        <i class="fas fa-bell"></i> تفعيل التنبيهات
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="soundEnabled" id="soundEnabled">
                                    <label class="form-check-label" for="soundEnabled">
                                        <i class="fas fa-volume-up"></i> الأصوات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="lowStockAlerts" id="lowStockAlerts">
                                    <label class="form-check-label" for="lowStockAlerts">
                                        <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون المنخفض
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="salesAlerts" id="salesAlerts">
                                    <label class="form-check-label" for="salesAlerts">
                                        <i class="fas fa-shopping-cart"></i> تنبيهات المبيعات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="systemAlerts" id="systemAlerts">
                                <label class="form-check-label" for="systemAlerts">
                                    <i class="fas fa-cog"></i> تنبيهات النظام
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء تبويب إعدادات النسخ الاحتياطي
 */
function createBackupSettingsTab() {
    return `
        <div id="backup" class="tab-pane">
            <div class="settings-card">
                <div class="card-header">
                    <h3><i class="fas fa-database"></i> إعدادات النسخ الاحتياطي</h3>
                </div>
                <div class="card-body">
                    <form id="backupSettingsForm">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="autoBackup" id="autoBackup">
                                <label class="form-check-label" for="autoBackup">
                                    <i class="fas fa-clock"></i> النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                        </div>

                        <div id="autoBackupSettings" style="display: none;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label><i class="fas fa-calendar"></i> تكرار النسخ</label>
                                    <select class="form-control" name="backupFrequency">
                                        <option value="daily">يومياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="monthly">شهرياً</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label><i class="fas fa-clock"></i> وقت النسخ</label>
                                    <input type="time" class="form-control" name="backupTime" value="02:00">
                                </div>
                            </div>

                            <div class="form-group">
                                <label><i class="fas fa-archive"></i> عدد النسخ المحفوظة</label>
                                <input type="number" class="form-control" name="maxBackups" min="1" max="30" value="7">
                                <small class="form-text text-muted">الحد الأقصى لعدد النسخ الاحتياطية المحفوظة</small>
                            </div>
                        </div>

                        <div class="backup-actions mt-4">
                            <button type="button" class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-download"></i> إنشاء نسخة احتياطية الآن
                            </button>
                            <button type="button" class="btn btn-success" onclick="restoreBackup()">
                                <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحميل الإعدادات الحالية
 */
function loadCurrentSettings() {
    try {
        console.log('🔄 تحميل الإعدادات الحالية...');

        // محاولة تحميل الإعدادات من localStorage
        const storedSettings = localStorage.getItem('technoflash_settings');

        if (storedSettings) {
            try {
                const parsedSettings = JSON.parse(storedSettings);
                console.log('✅ تم تحميل الإعدادات من localStorage:', parsedSettings);

                // دمج الإعدادات المحفوظة مع الافتراضية
                currentSettings = mergeSettings(DEFAULT_SETTINGS, parsedSettings);
            } catch (parseError) {
                console.error('❌ خطأ في تحليل الإعدادات المحفوظة:', parseError);
                console.log('🔧 استخدام الإعدادات الافتراضية...');
                currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));
            }
        } else {
            console.log('📝 لا توجد إعدادات محفوظة، استخدام الإعدادات الافتراضية');
            currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));
        }

        console.log('✅ تم تحميل الإعدادات بنجاح:', currentSettings);
        return currentSettings;

    } catch (error) {
        console.error('❌ خطأ في تحميل الإعدادات:', error);
        currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));
        return currentSettings;
    }
}

/**
 * دمج الإعدادات
 */
function mergeSettings(defaultSettings, userSettings) {
    const merged = JSON.parse(JSON.stringify(defaultSettings)); // نسخ عميق

    for (const section in userSettings) {
        if (merged[section] && typeof merged[section] === 'object' && !Array.isArray(merged[section])) {
            // دمج الكائنات
            merged[section] = { ...merged[section], ...userSettings[section] };
        } else {
            // استبدال القيم المباشرة والمصفوفات
            merged[section] = userSettings[section];
        }
    }

    return merged;
}

/**
 * تهيئة صفحة الإعدادات
 */
function initializeSettingsPage() {
    try {
        console.log('🔄 بدء تهيئة صفحة الإعدادات...');

        // إعداد مستمعي الأحداث للتبويبات
        setupTabListeners();

        // إعداد مستمعي الأحداث للنماذج
        setupFormListeners();

        // ملء النماذج بالقيم الحالية
        populateAllForms();

        console.log('✅ تم تهيئة صفحة الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة صفحة الإعدادات:', error);
    }
}

/**
 * إعداد مستمعي الأحداث للتبويبات
 */
function setupTabListeners() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

/**
 * إعداد مستمعي الأحداث للنماذج
 */
function setupFormListeners() {
    // مستمع لتغيير حجم الورق في إعدادات الفاتورة
    const paperSizeSelect = document.querySelector('[name="paperSize"]');
    if (paperSizeSelect) {
        paperSizeSelect.addEventListener('change', (e) => {
            const customSizeRow = document.getElementById('customSizeRow');
            if (e.target.value === 'custom') {
                customSizeRow.style.display = 'flex';
            } else {
                customSizeRow.style.display = 'none';
            }
        });
    }

    // مستمع لتفعيل الضرائب
    const enableTaxCheckbox = document.querySelector('[name="enableTax"]');
    if (enableTaxCheckbox) {
        enableTaxCheckbox.addEventListener('change', (e) => {
            const taxRatesSection = document.getElementById('taxRatesSection');
            if (e.target.checked) {
                taxRatesSection.style.display = 'block';
            } else {
                taxRatesSection.style.display = 'none';
            }
        });
    }

    // مستمع لتفعيل النسخ الاحتياطي التلقائي
    const autoBackupCheckbox = document.querySelector('[name="autoBackup"]');
    if (autoBackupCheckbox) {
        autoBackupCheckbox.addEventListener('change', (e) => {
            const autoBackupSettings = document.getElementById('autoBackupSettings');
            if (e.target.checked) {
                autoBackupSettings.style.display = 'block';
            } else {
                autoBackupSettings.style.display = 'none';
            }
        });
    }
}

/**
 * ملء جميع النماذج بالقيم الحالية
 */
function populateAllForms() {
    try {
        console.log('📝 ملء جميع النماذج...');

        populateGeneralForm();
        populateCompanyForm();
        populatePosForm();
        populateInvoiceForm();
        populateTaxesForm();
        populateNotificationsForm();
        populateBackupForm();

        console.log('✅ تم ملء جميع النماذج بنجاح');

    } catch (error) {
        console.error('❌ خطأ في ملء النماذج:', error);
    }
}

/**
 * ملء نموذج الإعدادات العامة
 */
function populateGeneralForm() {
    const form = document.getElementById('generalSettingsForm');
    if (!form || !currentSettings.general) return;

    const settings = currentSettings.general;

    setSelectValue(form, 'language', settings.language);
    setSelectValue(form, 'currency', settings.currency);
    setSelectValue(form, 'dateType', settings.dateType);
    setSelectValue(form, 'numberType', settings.numberType);
    setCheckboxValue(form, 'darkMode', settings.darkMode);
    setCheckboxValue(form, 'soundEffects', settings.soundEffects);

    console.log('✅ تم ملء نموذج الإعدادات العامة');
}

/**
 * ملء نموذج بيانات الشركة
 */
function populateCompanyForm() {
    const form = document.getElementById('companySettingsForm');
    if (!form || !currentSettings.company) return;

    const settings = currentSettings.company;

    setInputValue(form, 'companyName', settings.companyName);
    setInputValue(form, 'commercialRegister', settings.commercialRegister);
    setInputValue(form, 'taxNumber', settings.taxNumber);
    setInputValue(form, 'phone', settings.phone);
    setInputValue(form, 'address', settings.address);
    setInputValue(form, 'email', settings.email);
    setInputValue(form, 'website', settings.website);

    console.log('✅ تم ملء نموذج بيانات الشركة');
}

/**
 * ملء نموذج إعدادات نقطة البيع
 */
function populatePosForm() {
    const form = document.getElementById('posSettingsForm');
    if (!form || !currentSettings.pos) return;

    const settings = currentSettings.pos;

    setSelectValue(form, 'defaultPaymentMethod', settings.defaultPaymentMethod);
    setInputValue(form, 'invoiceNumberLength', settings.invoiceNumberLength);
    setInputValue(form, 'lowStockThreshold', settings.lowStockThreshold);
    setCheckboxValue(form, 'autoSave', settings.autoSave);
    setCheckboxValue(form, 'autoPrint', settings.autoPrint);
    setCheckboxValue(form, 'barcodeScanner', settings.barcodeScanner);
    setCheckboxValue(form, 'lowStockAlert', settings.lowStockAlert);

    console.log('✅ تم ملء نموذج إعدادات نقطة البيع');
}

/**
 * ملء نموذج إعدادات الفاتورة
 */
function populateInvoiceForm() {
    const form = document.getElementById('invoiceSettingsForm');
    if (!form || !currentSettings.invoice) return;

    const settings = currentSettings.invoice;

    setSelectValue(form, 'paperSize', settings.paperSize);
    setSelectValue(form, 'orientation', settings.orientation);
    setInputValue(form, 'customWidth', settings.customWidth);
    setInputValue(form, 'customHeight', settings.customHeight);
    setInputValue(form, 'footerText', settings.footerText);
    setInputValue(form, 'headerColor', settings.headerColor);
    setInputValue(form, 'accentColor', settings.accentColor);
    setCheckboxValue(form, 'showLogo', settings.showLogo);
    setCheckboxValue(form, 'showCompanyInfo', settings.showCompanyInfo);
    setCheckboxValue(form, 'showCustomerInfo', settings.showCustomerInfo);
    setCheckboxValue(form, 'showItemCodes', settings.showItemCodes);

    // إظهار/إخفاء حقول الحجم المخصص
    const customSizeRow = document.getElementById('customSizeRow');
    if (customSizeRow) {
        customSizeRow.style.display = settings.paperSize === 'custom' ? 'flex' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات الفاتورة');
}

/**
 * ملء نموذج إعدادات الضرائب
 */
function populateTaxesForm() {
    const form = document.getElementById('taxesSettingsForm');
    if (!form || !currentSettings.taxes) return;

    const settings = currentSettings.taxes;

    setCheckboxValue(form, 'enableTax', settings.enableTax);

    // إظهار/إخفاء قسم معدلات الضرائب
    const taxRatesSection = document.getElementById('taxRatesSection');
    if (taxRatesSection) {
        taxRatesSection.style.display = settings.enableTax ? 'block' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات الضرائب');
}

/**
 * ملء نموذج إعدادات التنبيهات
 */
function populateNotificationsForm() {
    const form = document.getElementById('notificationsSettingsForm');
    if (!form || !currentSettings.notifications) return;

    const settings = currentSettings.notifications;

    setCheckboxValue(form, 'enableNotifications', settings.enableNotifications);
    setCheckboxValue(form, 'lowStockAlerts', settings.lowStockAlerts);
    setCheckboxValue(form, 'salesAlerts', settings.salesAlerts);
    setCheckboxValue(form, 'systemAlerts', settings.systemAlerts);
    setCheckboxValue(form, 'soundEnabled', settings.soundEnabled);

    console.log('✅ تم ملء نموذج إعدادات التنبيهات');
}

/**
 * ملء نموذج إعدادات النسخ الاحتياطي
 */
function populateBackupForm() {
    const form = document.getElementById('backupSettingsForm');
    if (!form || !currentSettings.backup) return;

    const settings = currentSettings.backup;

    setCheckboxValue(form, 'autoBackup', settings.autoBackup);
    setSelectValue(form, 'backupFrequency', settings.backupFrequency);
    setInputValue(form, 'backupTime', settings.backupTime);
    setInputValue(form, 'maxBackups', settings.maxBackups);

    // إظهار/إخفاء إعدادات النسخ التلقائي
    const autoBackupSettings = document.getElementById('autoBackupSettings');
    if (autoBackupSettings) {
        autoBackupSettings.style.display = settings.autoBackup ? 'block' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات النسخ الاحتياطي');
}

/**
 * دوال مساعدة لملء النماذج
 */
function setInputValue(form, name, value) {
    const input = form.querySelector(`[name="${name}"]`);
    if (input && value !== undefined && value !== null) {
        input.value = value;
    }
}

function setSelectValue(form, name, value) {
    const select = form.querySelector(`[name="${name}"]`);
    if (select && value !== undefined && value !== null) {
        select.value = value;
    }
}

function setCheckboxValue(form, name, value) {
    const checkbox = form.querySelector(`[name="${name}"]`);
    if (checkbox && value !== undefined && value !== null) {
        checkbox.checked = Boolean(value);
    }
}

/**
 * حفظ جميع الإعدادات
 */
function saveAllSettings() {
    try {
        console.log('💾 بدء حفظ جميع الإعدادات...');

        // جمع البيانات من جميع النماذج
        collectAllFormData();

        // حفظ الإعدادات في localStorage
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

        // حفظ في قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db && db.updateSettings) {
            db.updateSettings(currentSettings);
        }

        // تطبيق الإعدادات
        applySettings();

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } else {
            alert('✅ تم حفظ الإعدادات بنجاح');
        }

        console.log('✅ تم حفظ جميع الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في حفظ الإعدادات:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في حفظ الإعدادات: ' + error.message);
        } else {
            alert('❌ فشل في حفظ الإعدادات: ' + error.message);
        }
    }
}

/**
 * جمع البيانات من جميع النماذج
 */
function collectAllFormData() {
    // جمع الإعدادات العامة
    const generalForm = document.getElementById('generalSettingsForm');
    if (generalForm) {
        const formData = new FormData(generalForm);
        currentSettings.general = {
            language: formData.get('language') || 'ar',
            currency: formData.get('currency') || 'EGP',
            dateType: formData.get('dateType') || 'gregorian',
            numberType: formData.get('numberType') || 'arabic',
            darkMode: formData.has('darkMode'),
            soundEffects: formData.has('soundEffects')
        };
    }

    // جمع بيانات الشركة
    const companyForm = document.getElementById('companySettingsForm');
    if (companyForm) {
        const formData = new FormData(companyForm);
        currentSettings.company = {
            companyName: formData.get('companyName') || '',
            commercialRegister: formData.get('commercialRegister') || '',
            taxNumber: formData.get('taxNumber') || '',
            phone: formData.get('phone') || '',
            address: formData.get('address') || '',
            email: formData.get('email') || '',
            website: formData.get('website') || '',
            logo: currentSettings.company.logo || '' // الاحتفاظ بالشعار الحالي
        };
    }

    // جمع إعدادات نقطة البيع
    const posForm = document.getElementById('posSettingsForm');
    if (posForm) {
        const formData = new FormData(posForm);
        currentSettings.pos = {
            defaultPaymentMethod: formData.get('defaultPaymentMethod') || 'cash',
            invoiceNumberLength: parseInt(formData.get('invoiceNumberLength')) || 6,
            lowStockThreshold: parseInt(formData.get('lowStockThreshold')) || 10,
            autoSave: formData.has('autoSave'),
            autoPrint: formData.has('autoPrint'),
            barcodeScanner: formData.has('barcodeScanner'),
            lowStockAlert: formData.has('lowStockAlert')
        };
    }

    // جمع إعدادات الفاتورة
    const invoiceForm = document.getElementById('invoiceSettingsForm');
    if (invoiceForm) {
        const formData = new FormData(invoiceForm);
        currentSettings.invoice = {
            paperSize: formData.get('paperSize') || 'A4',
            customWidth: parseInt(formData.get('customWidth')) || 210,
            customHeight: parseInt(formData.get('customHeight')) || 297,
            orientation: formData.get('orientation') || 'portrait',
            footerText: formData.get('footerText') || 'شكراً لتعاملكم معنا',
            headerColor: formData.get('headerColor') || '#2c3e50',
            accentColor: formData.get('accentColor') || '#3498db',
            showLogo: formData.has('showLogo'),
            showCompanyInfo: formData.has('showCompanyInfo'),
            showCustomerInfo: formData.has('showCustomerInfo'),
            showItemCodes: formData.has('showItemCodes'),
            showItemImages: currentSettings.invoice.showItemImages || false
        };
    }

    // جمع إعدادات الضرائب
    const taxesForm = document.getElementById('taxesSettingsForm');
    if (taxesForm) {
        const formData = new FormData(taxesForm);
        currentSettings.taxes = {
            enableTax: formData.has('enableTax'),
            taxRates: currentSettings.taxes.taxRates || DEFAULT_SETTINGS.taxes.taxRates
        };
    }

    // جمع إعدادات التنبيهات
    const notificationsForm = document.getElementById('notificationsSettingsForm');
    if (notificationsForm) {
        const formData = new FormData(notificationsForm);
        currentSettings.notifications = {
            enableNotifications: formData.has('enableNotifications'),
            lowStockAlerts: formData.has('lowStockAlerts'),
            salesAlerts: formData.has('salesAlerts'),
            systemAlerts: formData.has('systemAlerts'),
            soundEnabled: formData.has('soundEnabled')
        };
    }

    // جمع إعدادات النسخ الاحتياطي
    const backupForm = document.getElementById('backupSettingsForm');
    if (backupForm) {
        const formData = new FormData(backupForm);
        currentSettings.backup = {
            autoBackup: formData.has('autoBackup'),
            backupFrequency: formData.get('backupFrequency') || 'daily',
            backupTime: formData.get('backupTime') || '02:00',
            maxBackups: parseInt(formData.get('maxBackups')) || 7
        };
    }

    console.log('📊 تم جمع البيانات من جميع النماذج:', currentSettings);
}

/**
 * تطبيق الإعدادات
 */
function applySettings() {
    try {
        console.log('⚙️ تطبيق الإعدادات...');

        // تطبيق الإعدادات العامة
        if (currentSettings.general) {
            // تطبيق الوضع الليلي
            if (currentSettings.general.darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق نوع الأرقام
            document.documentElement.setAttribute('data-number-type', currentSettings.general.numberType || 'arabic');

            // تطبيق العملة
            document.documentElement.setAttribute('data-currency', currentSettings.general.currency || 'EGP');

            // تطبيق اللغة
            document.documentElement.setAttribute('lang', currentSettings.general.language || 'ar');
            document.documentElement.setAttribute('dir', 'rtl');
        }

        // إشعار باقي أجزاء التطبيق بتحديث الإعدادات
        window.dispatchEvent(new CustomEvent('settingsUpdated', {
            detail: currentSettings
        }));

        console.log('✅ تم تطبيق الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تطبيق الإعدادات:', error);
    }
}

/**
 * إعادة تعيين الإعدادات إلى الافتراضية
 */
function resetToDefaults() {
    try {
        // تأكيد من المستخدم
        const confirmReset = confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع الإعدادات الحالية.');

        if (!confirmReset) {
            return;
        }

        console.log('🔄 إعادة تعيين الإعدادات إلى الافتراضية...');

        // إعادة تعيين الإعدادات
        currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));

        // حفظ الإعدادات الجديدة
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

        // حفظ في قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db && db.updateSettings) {
            db.updateSettings(currentSettings);
        }

        // إعادة ملء النماذج
        populateAllForms();

        // تطبيق الإعدادات
        applySettings();

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'success');
        } else {
            alert('✅ تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
        }

        console.log('✅ تم إعادة تعيين الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إعادة تعيين الإعدادات: ' + error.message);
        } else {
            alert('❌ فشل في إعادة تعيين الإعدادات: ' + error.message);
        }
    }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup() {
    try {
        console.log('📦 إنشاء نسخة احتياطية...');

        // جمع جميع البيانات
        const backupData = {
            settings: currentSettings,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        // إضافة بيانات قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db) {
            try {
                backupData.database = {
                    products: db.getProducts() || [],
                    customers: db.getCustomers() || [],
                    suppliers: db.getSuppliers() || [],
                    sales: db.getSales() || [],
                    purchases: db.getPurchases() || [],
                    debts: db.getDebts() || []
                };
            } catch (dbError) {
                console.warn('تحذير: لم يتم تضمين بيانات قاعدة البيانات في النسخة الاحتياطية:', dbError);
            }
        }

        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(backupData, null, 2);

        // إنشاء ملف للتحميل
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // إنشاء رابط التحميل
        const link = document.createElement('a');
        link.href = url;
        link.download = `technoflash_backup_${new Date().toISOString().split('T')[0]}.json`;

        // تحميل الملف
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // تنظيف الذاكرة
        URL.revokeObjectURL(url);

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            alert('✅ تم إنشاء النسخة الاحتياطية بنجاح');
        }

        console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        } else {
            alert('❌ فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        }
    }
}

/**
 * استعادة نسخة احتياطية
 */
function restoreBackup() {
    try {
        console.log('📥 استعادة نسخة احتياطية...');

        // إنشاء عنصر input للملف
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';

        fileInput.onchange = function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    // التحقق من صحة البيانات
                    if (!backupData.settings) {
                        throw new Error('ملف النسخة الاحتياطية غير صحيح');
                    }

                    // تأكيد من المستخدم
                    const confirmRestore = confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\nسيتم استبدال جميع البيانات الحالية.');

                    if (!confirmRestore) {
                        return;
                    }

                    // استعادة الإعدادات
                    currentSettings = backupData.settings;
                    localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

                    // استعادة بيانات قاعدة البيانات إذا كانت متاحة
                    if (backupData.database && typeof db !== 'undefined' && db) {
                        try {
                            if (backupData.database.products) db.saveProducts(backupData.database.products);
                            if (backupData.database.customers) db.saveCustomers(backupData.database.customers);
                            if (backupData.database.suppliers) db.saveSuppliers(backupData.database.suppliers);
                            if (backupData.database.sales) db.saveSales(backupData.database.sales);
                            if (backupData.database.purchases) db.savePurchases(backupData.database.purchases);
                            if (backupData.database.debts) db.saveDebts(backupData.database.debts);
                        } catch (dbError) {
                            console.warn('تحذير: لم يتم استعادة بعض بيانات قاعدة البيانات:', dbError);
                        }
                    }

                    // إعادة ملء النماذج
                    populateAllForms();

                    // تطبيق الإعدادات
                    applySettings();

                    // إشعار المستخدم
                    if (typeof app !== 'undefined' && app && app.showNotification) {
                        app.showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                    } else {
                        alert('✅ تم استعادة النسخة الاحتياطية بنجاح');
                    }

                    console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');

                } catch (parseError) {
                    console.error('❌ خطأ في تحليل ملف النسخة الاحتياطية:', parseError);

                    if (typeof app !== 'undefined' && app && app.showAlert) {
                        app.showAlert('خطأ', 'ملف النسخة الاحتياطية غير صحيح أو تالف');
                    } else {
                        alert('❌ ملف النسخة الاحتياطية غير صحيح أو تالف');
                    }
                }
            };

            reader.readAsText(file);
        };

        // فتح مربع حوار اختيار الملف
        fileInput.click();

    } catch (error) {
        console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في استعادة النسخة الاحتياطية: ' + error.message);
        } else {
            alert('❌ فشل في استعادة النسخة الاحتياطية: ' + error.message);
        }
    }
}

/**
 * تطبيق الإعدادات
 */
function applySettings() {
    try {
        console.log('⚙️ تطبيق الإعدادات...');

        // تطبيق الإعدادات العامة
        if (currentSettings.general) {
            // تطبيق الوضع الليلي
            if (currentSettings.general.darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق نوع الأرقام
            document.documentElement.setAttribute('data-number-type', currentSettings.general.numberType || 'arabic');

            // تطبيق العملة
            document.documentElement.setAttribute('data-currency', currentSettings.general.currency || 'EGP');

            // تطبيق اللغة
            document.documentElement.setAttribute('lang', currentSettings.general.language || 'ar');
            document.documentElement.setAttribute('dir', 'rtl');
        }

        // إشعار باقي أجزاء التطبيق بتحديث الإعدادات
        window.dispatchEvent(new CustomEvent('settingsUpdated', {
            detail: currentSettings
        }));

        console.log('✅ تم تطبيق الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تطبيق الإعدادات:', error);
    }
}

/**
 * إعادة تعيين الإعدادات إلى الافتراضية
 */
function resetToDefaults() {
    try {
        // تأكيد من المستخدم
        const confirmReset = confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع الإعدادات الحالية.');

        if (!confirmReset) {
            return;
        }

        console.log('🔄 إعادة تعيين الإعدادات إلى الافتراضية...');

        // إعادة تعيين الإعدادات
        currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));

        // حفظ الإعدادات الجديدة
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

        // حفظ في قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db && db.updateSettings) {
            db.updateSettings(currentSettings);
        }

        // إعادة ملء النماذج
        populateAllForms();

        // تطبيق الإعدادات
        applySettings();

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'success');
        } else {
            alert('✅ تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
        }

        console.log('✅ تم إعادة تعيين الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إعادة تعيين الإعدادات: ' + error.message);
        } else {
            alert('❌ فشل في إعادة تعيين الإعدادات: ' + error.message);
        }
    }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup() {
    try {
        console.log('📦 إنشاء نسخة احتياطية...');

        // جمع جميع البيانات
        const backupData = {
            settings: currentSettings,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        // إضافة بيانات قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db) {
            try {
                backupData.database = {
                    products: db.getProducts() || [],
                    customers: db.getCustomers() || [],
                    suppliers: db.getSuppliers() || [],
                    sales: db.getSales() || [],
                    purchases: db.getPurchases() || [],
                    debts: db.getDebts() || []
                };
            } catch (dbError) {
                console.warn('تحذير: لم يتم تضمين بيانات قاعدة البيانات في النسخة الاحتياطية:', dbError);
            }
        }

        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(backupData, null, 2);

        // إنشاء ملف للتحميل
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // إنشاء رابط التحميل
        const link = document.createElement('a');
        link.href = url;
        link.download = `technoflash_backup_${new Date().toISOString().split('T')[0]}.json`;

        // تحميل الملف
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // تنظيف الذاكرة
        URL.revokeObjectURL(url);

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            alert('✅ تم إنشاء النسخة الاحتياطية بنجاح');
        }

        console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        } else {
            alert('❌ فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        }
    }
}

/**
 * تطبيق الإعدادات
 */
function applySettings() {
    try {
        console.log('⚙️ تطبيق الإعدادات...');

        // تطبيق الإعدادات العامة
        if (currentSettings.general) {
            // تطبيق الوضع الليلي
            if (currentSettings.general.darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق نوع الأرقام
            document.documentElement.setAttribute('data-number-type', currentSettings.general.numberType || 'arabic');

            // تطبيق العملة
            document.documentElement.setAttribute('data-currency', currentSettings.general.currency || 'EGP');

            // تطبيق اللغة
            document.documentElement.setAttribute('lang', currentSettings.general.language || 'ar');
            document.documentElement.setAttribute('dir', 'rtl');
        }

        // إشعار باقي أجزاء التطبيق بتحديث الإعدادات
        window.dispatchEvent(new CustomEvent('settingsUpdated', {
            detail: currentSettings
        }));

        console.log('✅ تم تطبيق الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تطبيق الإعدادات:', error);
    }
}

/**
 * إعادة تعيين الإعدادات إلى الافتراضية
 */
function resetToDefaults() {
    try {
        // تأكيد من المستخدم
        const confirmReset = confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع الإعدادات الحالية.');

        if (!confirmReset) {
            return;
        }

        console.log('🔄 إعادة تعيين الإعدادات إلى الافتراضية...');

        // إعادة تعيين الإعدادات
        currentSettings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));

        // حفظ الإعدادات الجديدة
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

        // حفظ في قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db && db.updateSettings) {
            db.updateSettings(currentSettings);
        }

        // إعادة ملء النماذج
        populateAllForms();

        // تطبيق الإعدادات
        applySettings();

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'success');
        } else {
            alert('✅ تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
        }

        console.log('✅ تم إعادة تعيين الإعدادات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إعادة تعيين الإعدادات: ' + error.message);
        } else {
            alert('❌ فشل في إعادة تعيين الإعدادات: ' + error.message);
        }
    }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup() {
    try {
        console.log('📦 إنشاء نسخة احتياطية...');

        // جمع جميع البيانات
        const backupData = {
            settings: currentSettings,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        // إضافة بيانات قاعدة البيانات إذا كانت متاحة
        if (typeof db !== 'undefined' && db) {
            try {
                backupData.database = {
                    products: db.getProducts() || [],
                    customers: db.getCustomers() || [],
                    suppliers: db.getSuppliers() || [],
                    sales: db.getSales() || [],
                    purchases: db.getPurchases() || [],
                    debts: db.getDebts() || []
                };
            } catch (dbError) {
                console.warn('تحذير: لم يتم تضمين بيانات قاعدة البيانات في النسخة الاحتياطية:', dbError);
            }
        }

        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(backupData, null, 2);

        // إنشاء ملف للتحميل
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // إنشاء رابط التحميل
        const link = document.createElement('a');
        link.href = url;
        link.download = `technoflash_backup_${new Date().toISOString().split('T')[0]}.json`;

        // تحميل الملف
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // تنظيف الذاكرة
        URL.revokeObjectURL(url);

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            alert('✅ تم إنشاء النسخة الاحتياطية بنجاح');
        }

        console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        } else {
            alert('❌ فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        }
    }
}

/**
 * استعادة نسخة احتياطية
 */
function restoreBackup() {
    try {
        console.log('📥 استعادة نسخة احتياطية...');

        // إنشاء عنصر input للملف
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';

        fileInput.onchange = function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    // التحقق من صحة البيانات
                    if (!backupData.settings) {
                        throw new Error('ملف النسخة الاحتياطية غير صحيح');
                    }

                    // تأكيد من المستخدم
                    const confirmRestore = confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\nسيتم استبدال جميع البيانات الحالية.');

                    if (!confirmRestore) {
                        return;
                    }

                    // استعادة الإعدادات
                    currentSettings = backupData.settings;
                    localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));

                    // استعادة بيانات قاعدة البيانات إذا كانت متاحة
                    if (backupData.database && typeof db !== 'undefined' && db) {
                        try {
                            if (backupData.database.products) db.saveProducts(backupData.database.products);
                            if (backupData.database.customers) db.saveCustomers(backupData.database.customers);
                            if (backupData.database.suppliers) db.saveSuppliers(backupData.database.suppliers);
                            if (backupData.database.sales) db.saveSales(backupData.database.sales);
                            if (backupData.database.purchases) db.savePurchases(backupData.database.purchases);
                            if (backupData.database.debts) db.saveDebts(backupData.database.debts);
                        } catch (dbError) {
                            console.warn('تحذير: لم يتم استعادة بعض بيانات قاعدة البيانات:', dbError);
                        }
                    }

                    // إعادة ملء النماذج
                    populateAllForms();

                    // تطبيق الإعدادات
                    applySettings();

                    // إشعار المستخدم
                    if (typeof app !== 'undefined' && app && app.showNotification) {
                        app.showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                    } else {
                        alert('✅ تم استعادة النسخة الاحتياطية بنجاح');
                    }

                    console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');

                } catch (parseError) {
                    console.error('❌ خطأ في تحليل ملف النسخة الاحتياطية:', parseError);

                    if (typeof app !== 'undefined' && app && app.showAlert) {
                        app.showAlert('خطأ', 'ملف النسخة الاحتياطية غير صحيح أو تالف');
                    } else {
                        alert('❌ ملف النسخة الاحتياطية غير صحيح أو تالف');
                    }
                }
            };

            reader.readAsText(file);
        };

        // فتح مربع حوار اختيار الملف
        fileInput.click();

    } catch (error) {
        console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في استعادة النسخة الاحتياطية: ' + error.message);
        } else {
            alert('❌ فشل في استعادة النسخة الاحتياطية: ' + error.message);
        }
    }
}

/**
 * ملء نموذج الإعدادات العامة
 */
function populateGeneralForm() {
    const form = document.getElementById('generalSettingsForm');
    if (!form || !currentSettings.general) return;

    const settings = currentSettings.general;

    setSelectValue(form, 'language', settings.language);
    setSelectValue(form, 'currency', settings.currency);
    setSelectValue(form, 'dateType', settings.dateType);
    setSelectValue(form, 'numberType', settings.numberType);
    setCheckboxValue(form, 'darkMode', settings.darkMode);
    setCheckboxValue(form, 'soundEffects', settings.soundEffects);

    console.log('✅ تم ملء نموذج الإعدادات العامة');
}

/**
 * ملء نموذج بيانات الشركة
 */
function populateCompanyForm() {
    const form = document.getElementById('companySettingsForm');
    if (!form || !currentSettings.company) return;

    const settings = currentSettings.company;

    setInputValue(form, 'companyName', settings.companyName);
    setInputValue(form, 'commercialRegister', settings.commercialRegister);
    setInputValue(form, 'taxNumber', settings.taxNumber);
    setInputValue(form, 'phone', settings.phone);
    setInputValue(form, 'address', settings.address);
    setInputValue(form, 'email', settings.email);
    setInputValue(form, 'website', settings.website);

    console.log('✅ تم ملء نموذج بيانات الشركة');
}

/**
 * ملء نموذج إعدادات نقطة البيع
 */
function populatePosForm() {
    const form = document.getElementById('posSettingsForm');
    if (!form || !currentSettings.pos) return;

    const settings = currentSettings.pos;

    setSelectValue(form, 'defaultPaymentMethod', settings.defaultPaymentMethod);
    setInputValue(form, 'invoiceNumberLength', settings.invoiceNumberLength);
    setInputValue(form, 'lowStockThreshold', settings.lowStockThreshold);
    setCheckboxValue(form, 'autoSave', settings.autoSave);
    setCheckboxValue(form, 'autoPrint', settings.autoPrint);
    setCheckboxValue(form, 'barcodeScanner', settings.barcodeScanner);
    setCheckboxValue(form, 'lowStockAlert', settings.lowStockAlert);

    console.log('✅ تم ملء نموذج إعدادات نقطة البيع');
}

/**
 * ملء نموذج إعدادات الفاتورة
 */
function populateInvoiceForm() {
    const form = document.getElementById('invoiceSettingsForm');
    if (!form || !currentSettings.invoice) return;

    const settings = currentSettings.invoice;

    setSelectValue(form, 'paperSize', settings.paperSize);
    setSelectValue(form, 'orientation', settings.orientation);
    setInputValue(form, 'customWidth', settings.customWidth);
    setInputValue(form, 'customHeight', settings.customHeight);
    setInputValue(form, 'footerText', settings.footerText);
    setInputValue(form, 'headerColor', settings.headerColor);
    setInputValue(form, 'accentColor', settings.accentColor);
    setCheckboxValue(form, 'showLogo', settings.showLogo);
    setCheckboxValue(form, 'showCompanyInfo', settings.showCompanyInfo);
    setCheckboxValue(form, 'showCustomerInfo', settings.showCustomerInfo);
    setCheckboxValue(form, 'showItemCodes', settings.showItemCodes);

    // إظهار/إخفاء حقول الحجم المخصص
    const customSizeRow = document.getElementById('customSizeRow');
    if (customSizeRow) {
        customSizeRow.style.display = settings.paperSize === 'custom' ? 'flex' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات الفاتورة');
}

/**
 * ملء نموذج إعدادات الضرائب
 */
function populateTaxesForm() {
    const form = document.getElementById('taxesSettingsForm');
    if (!form || !currentSettings.taxes) return;

    const settings = currentSettings.taxes;

    setCheckboxValue(form, 'enableTax', settings.enableTax);

    // إظهار/إخفاء قسم معدلات الضرائب
    const taxRatesSection = document.getElementById('taxRatesSection');
    if (taxRatesSection) {
        taxRatesSection.style.display = settings.enableTax ? 'block' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات الضرائب');
}

/**
 * ملء نموذج إعدادات التنبيهات
 */
function populateNotificationsForm() {
    const form = document.getElementById('notificationsSettingsForm');
    if (!form || !currentSettings.notifications) return;

    const settings = currentSettings.notifications;

    setCheckboxValue(form, 'enableNotifications', settings.enableNotifications);
    setCheckboxValue(form, 'lowStockAlerts', settings.lowStockAlerts);
    setCheckboxValue(form, 'salesAlerts', settings.salesAlerts);
    setCheckboxValue(form, 'systemAlerts', settings.systemAlerts);
    setCheckboxValue(form, 'soundEnabled', settings.soundEnabled);

    console.log('✅ تم ملء نموذج إعدادات التنبيهات');
}

/**
 * ملء نموذج إعدادات النسخ الاحتياطي
 */
function populateBackupForm() {
    const form = document.getElementById('backupSettingsForm');
    if (!form || !currentSettings.backup) return;

    const settings = currentSettings.backup;

    setCheckboxValue(form, 'autoBackup', settings.autoBackup);
    setSelectValue(form, 'backupFrequency', settings.backupFrequency);
    setInputValue(form, 'backupTime', settings.backupTime);
    setInputValue(form, 'maxBackups', settings.maxBackups);

    // إظهار/إخفاء إعدادات النسخ التلقائي
    const autoBackupSettings = document.getElementById('autoBackupSettings');
    if (autoBackupSettings) {
        autoBackupSettings.style.display = settings.autoBackup ? 'block' : 'none';
    }

    console.log('✅ تم ملء نموذج إعدادات النسخ الاحتياطي');
}
