/**
 * إدارة النسخ الاحتياطي - تكنوفلاش
 */

let backupHistory = [];

/**
 * إصلاح الإعدادات التالفة بعد ضبط المصنع
 */
function fixCorruptedSettings() {
    try {
        console.log('🔧 فحص وإصلاح الإعدادات التالفة...');

        // فحص وجود الإعدادات
        let settings = null;
        try {
            const storedSettings = localStorage.getItem('technoflash_settings');
            if (storedSettings) {
                settings = JSON.parse(storedSettings);
            }
        } catch (e) {
            console.error('إعدادات تالفة، سيتم إنشاء إعدادات جديدة');
            localStorage.removeItem('technoflash_settings');
        }

        // إنشاء إعدادات افتراضية إذا لم توجد
        if (!settings || typeof settings !== 'object') {
            console.log('إنشاء إعدادات افتراضية جديدة...');
            settings = {
                general: {
                    language: 'ar',
                    currency: 'EGP',
                    dateType: 'gregorian',
                    numberType: 'arabic',
                    darkMode: false,
                    soundEffects: true
                },
                company: {
                    companyName: 'تكنوفلاش',
                    commercialRegister: '',
                    taxNumber: '',
                    phone: '',
                    address: '',
                    email: '',
                    website: '',
                    logo: ''
                },
                pos: {
                    defaultPaymentMethod: 'cash',
                    invoiceNumberLength: 6,
                    autoSave: true,
                    autoPrint: false,
                    barcodeScanner: true,
                    lowStockAlert: true,
                    lowStockThreshold: 10
                },
                taxes: {
                    enableTax: false,
                    defaultTaxType: 'inclusive',
                    taxRates: [
                        { name: 'ضريبة القيمة المضافة', rate: 15, isDefault: true }
                    ]
                },
                invoice: {
                    template: 'default',
                    showLogo: true,
                    showCompanyInfo: true,
                    showTax: false,
                    paperSize: 'A4',
                    language: 'ar'
                },
                notifications: {
                    lowStock: true,
                    newSale: true,
                    newPurchase: true,
                    payment: true
                },
                backup: {
                    autoBackup: false,
                    backupFrequency: 'daily',
                    backupTime: '02:00',
                    maxBackups: 7
                }
            };

            // حفظ الإعدادات الجديدة
            localStorage.setItem('technoflash_settings', JSON.stringify(settings));
            console.log('✅ تم إنشاء وحفظ الإعدادات الافتراضية');
        }

        return settings;

    } catch (error) {
        console.error('❌ خطأ في إصلاح الإعدادات:', error);
        return null;
    }
}

/**
 * إصلاح الإعدادات من وحدة التحكم (للمطورين)
 */
window.fixSettings = function() {
    console.log('🔧 بدء إصلاح الإعدادات من وحدة التحكم...');
    const result = fixCorruptedSettings();
    if (result) {
        console.log('✅ تم إصلاح الإعدادات بنجاح');
        console.log('الإعدادات الحالية:', result);

        // إعادة تحميل الصفحة إذا كانت صفحة الإعدادات مفتوحة
        if (window.location.hash === '#settings' || document.querySelector('.settings-page')) {
            console.log('إعادة تحميل صفحة الإعدادات...');
            setTimeout(() => {
                if (typeof loadSettings === 'function') {
                    loadSettings();
                } else {
                    location.reload();
                }
            }, 1000);
        }

        return result;
    } else {
        console.error('❌ فشل في إصلاح الإعدادات');
        return null;
    }
};

/**
 * تحميل صفحة النسخ الاحتياطي
 */
async function loadBackup() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-database"></i> النسخ الاحتياطي والاستعادة</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="createBackup()">
                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                </button>
                <button class="btn btn-success" onclick="showRestoreModal()">
                    <i class="fas fa-upload"></i> استعادة البيانات
                </button>
                <button class="btn btn-danger" onclick="showFactoryResetModal()" title="تحذير: سيحذف جميع البيانات نهائياً!">
                    <i class="fas fa-exclamation-triangle"></i> ضبط المصنع
                </button>
            </div>
        </div>

        <!-- إحصائيات النسخ الاحتياطي -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-database"></i>
                </div>
                <div class="stat-value" id="totalBackups">٠</div>
                <div class="stat-label">إجمالي النسخ</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-value" id="lastBackupDate">-</div>
                <div class="stat-label">آخر نسخة احتياطية</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="stat-value" id="totalDataSize">٠ KB</div>
                <div class="stat-label">حجم البيانات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-value" id="autoBackupStatus">معطل</div>
                <div class="stat-label">النسخ التلقائي</div>
            </div>
        </div>

        <!-- النسخ الاحتياطي السريع -->
        <div class="quick-backup-section">
            <div class="card">
                <div class="card-header">
                    <h3>النسخ الاحتياطي السريع</h3>
                </div>
                <div class="card-body">
                    <div class="backup-options">
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupProducts" checked>
                                المنتجات والمخزون
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupCustomers" checked>
                                العملاء
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupSuppliers" checked>
                                الموردين
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupSales" checked>
                                المبيعات
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupPurchases" checked>
                                المشتريات
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupPayments" checked>
                                المدفوعات
                            </label>
                        </div>
                        <div class="backup-option">
                            <label class="checkbox-label">
                                <input type="checkbox" id="backupSettings" checked>
                                الإعدادات
                            </label>
                        </div>
                    </div>
                    
                    <div class="backup-actions">
                        <button class="btn btn-primary" onclick="createCustomBackup()">
                            <i class="fas fa-download"></i> إنشاء نسخة مخصصة
                        </button>
                        <button class="btn btn-secondary" onclick="selectAllBackupOptions()">
                            <i class="fas fa-check-double"></i> تحديد الكل
                        </button>
                        <button class="btn btn-secondary" onclick="clearAllBackupOptions()">
                            <i class="fas fa-times"></i> إلغاء التحديد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل النسخ الاحتياطي -->
        <div class="backup-history-section">
            <div class="card">
                <div class="card-header">
                    <h3>سجل النسخ الاحتياطي</h3>
                    <div class="search-bar">
                        <input type="text" class="search-input" id="backupSearch" 
                               placeholder="البحث في النسخ الاحتياطي...">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="backupHistoryTable">
                            <thead>
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>النوع</th>
                                    <th>الحجم</th>
                                    <th>البيانات المشمولة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة الاستعادة -->
        <div id="restoreModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>استعادة البيانات</h3>
                    <button class="modal-close" onclick="app.hideModal('restoreModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="restore-options">
                        <div class="form-group">
                            <label>طريقة الاستعادة</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="restoreMethod" value="file" checked 
                                           onchange="toggleRestoreMethod()">
                                    من ملف
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="restoreMethod" value="history" 
                                           onchange="toggleRestoreMethod()">
                                    من السجل
                                </label>
                            </div>
                        </div>
                        
                        <div id="fileRestoreSection">
                            <div class="form-group">
                                <label>اختر ملف النسخة الاحتياطية</label>
                                <input type="file" class="form-control" id="restoreFile" 
                                       accept=".json" onchange="validateRestoreFile()">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <!-- سيتم عرض معلومات الملف هنا -->
                            </div>
                        </div>
                        
                        <div id="historyRestoreSection" style="display: none;">
                            <div class="form-group">
                                <label>اختر من السجل</label>
                                <select class="form-control" id="historyBackupSelect">
                                    <option value="">اختر نسخة احتياطية</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="confirmRestore">
                                أؤكد أنني أريد استعادة البيانات (سيتم استبدال البيانات الحالية)
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-success" onclick="performRestore()" 
                                id="restoreButton" disabled>
                            <i class="fas fa-upload"></i> استعادة البيانات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.hideModal('restoreModal')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة تقدم العملية -->
        <div id="progressModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="progressTitle">جاري المعالجة...</h3>
                </div>
                <div class="modal-body">
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">٠%</div>
                    </div>
                    <div class="progress-details" id="progressDetails">
                        جاري التحضير...
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة ضبط المصنع -->
        <div id="factoryResetModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle text-danger"></i> ضبط المصنع</h3>
                    <button class="modal-close" onclick="app.hideModal('factoryResetModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير!</strong> هذه العملية ستحذف جميع البيانات نهائياً ولا يمكن التراجع عنها.
                    </div>

                    <div class="factory-reset-info">
                        <h4>سيتم حذف البيانات التالية:</h4>
                        <ul class="reset-items-list">
                            <li><i class="fas fa-box text-danger"></i> جميع المنتجات والمخزون</li>
                            <li><i class="fas fa-users text-danger"></i> جميع العملاء</li>
                            <li><i class="fas fa-truck text-danger"></i> جميع الموردين</li>
                            <li><i class="fas fa-shopping-cart text-danger"></i> جميع المبيعات</li>
                            <li><i class="fas fa-shopping-bag text-danger"></i> جميع المشتريات</li>
                            <li><i class="fas fa-credit-card text-danger"></i> جميع المدفوعات والديون</li>
                            <li><i class="fas fa-chart-bar text-danger"></i> جميع التقارير والإحصائيات</li>
                            <li><i class="fas fa-database text-danger"></i> جميع النسخ الاحتياطي</li>
                        </ul>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة:</strong> سيتم الاحتفاظ بالإعدادات الأساسية للنظام فقط.
                        </div>
                    </div>

                    <div class="factory-reset-options">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="createBackupBeforeReset">
                                إنشاء نسخة احتياطية قبل الحذف
                            </label>
                            <small class="form-text text-muted">
                                يُنصح بشدة بإنشاء نسخة احتياطية قبل المتابعة
                            </small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="resetSettings">
                                إعادة ضبط الإعدادات أيضاً
                            </label>
                            <small class="form-text text-muted">
                                سيتم إعادة ضبط جميع الإعدادات للقيم الافتراضية
                            </small>
                        </div>

                        <div class="form-group">
                            <label>للمتابعة، اكتب "ضبط المصنع" في الحقل أدناه:</label>
                            <input type="text" class="form-control" id="factoryResetConfirmation"
                                   placeholder="اكتب: ضبط المصنع"
                                   onkeyup="validateFactoryResetConfirmation()">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-danger" onclick="performFactoryReset()"
                                id="factoryResetButton" disabled>
                            <i class="fas fa-exclamation-triangle"></i> تأكيد ضبط المصنع
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.hideModal('factoryResetModal')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeBackupPage();
}

/**
 * تهيئة صفحة النسخ الاحتياطي
 */
async function initializeBackupPage() {
    try {
        console.log('🔄 بدء تهيئة صفحة النسخ الاحتياطي...');

        // إصلاح الإعدادات التالفة أولاً
        fixCorruptedSettings();

        // تحميل سجل النسخ الاحتياطي
        loadBackupHistory();

        // تحديث الإحصائيات
        updateBackupStats();

        // إعداد مستمعي الأحداث
        setupBackupEventListeners();

        // تحديث حالة النسخ التلقائي
        updateAutoBackupStatus();

        console.log('✅ تم تهيئة صفحة النسخ الاحتياطي بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة صفحة النسخ الاحتياطي:', error);

        // عرض رسالة خطأ للمستخدم
        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'حدث خطأ في تحميل صفحة النسخ الاحتياطي: ' + error.message);
        }
    }
}

/**
 * تحميل سجل النسخ الاحتياطي
 */
function loadBackupHistory() {
    backupHistory = db.getBackupHistory() || [];
    displayBackupHistory();
}

/**
 * عرض سجل النسخ الاحتياطي
 */
function displayBackupHistory(history = backupHistory) {
    const tbody = document.querySelector('#backupHistoryTable tbody');
    
    if (history.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد نسخ احتياطية</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = history.map(backup => `
        <tr>
            <td>${db.formatDateTime(backup.createdAt)}</td>
            <td>
                <span class="badge badge-${backup.type === 'auto' ? 'info' : 'primary'}">
                    ${backup.type === 'auto' ? 'تلقائي' : 'يدوي'}
                </span>
            </td>
            <td>${formatFileSize(backup.size)}</td>
            <td>
                <div class="backup-includes">
                    ${backup.includes.map(item => `
                        <span class="backup-include-item">${getDataTypeName(item)}</span>
                    `).join('')}
                </div>
            </td>
            <td>
                <span class="badge badge-${backup.status === 'success' ? 'success' : 'danger'}">
                    ${backup.status === 'success' ? 'نجح' : 'فشل'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    ${backup.status === 'success' ? `
                        <button class="btn btn-sm btn-success" onclick="restoreFromHistory('${backup.id}')" 
                                title="استعادة">
                            <i class="fas fa-upload"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="downloadBackup('${backup.id}')" 
                                title="تحميل">
                            <i class="fas fa-download"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-danger" onclick="deleteBackup('${backup.id}')" 
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * تحديث إحصائيات النسخ الاحتياطي
 */
function updateBackupStats() {
    const successfulBackups = backupHistory.filter(b => b.status === 'success');
    const totalSize = successfulBackups.reduce((sum, backup) => sum + backup.size, 0);
    const lastBackup = successfulBackups.length > 0 ? 
        successfulBackups.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0] : null;
    
    document.getElementById('totalBackups').textContent = db.toArabicNumerals(successfulBackups.length);
    document.getElementById('lastBackupDate').textContent = lastBackup ? 
        db.formatDate(lastBackup.createdAt.split('T')[0]) : '-';
    document.getElementById('totalDataSize').textContent = formatFileSize(totalSize);
}

/**
 * تحديث حالة النسخ التلقائي
 */
function updateAutoBackupStatus() {
    const settings = db.getSettings();
    const autoBackupEnabled = settings?.backup?.autoBackup || false;
    
    document.getElementById('autoBackupStatus').textContent = 
        autoBackupEnabled ? 'مفعل' : 'معطل';
}

/**
 * إعداد مستمعي الأحداث
 */
function setupBackupEventListeners() {
    // البحث في النسخ الاحتياطي
    document.getElementById('backupSearch').addEventListener('input', filterBackupHistory);

    // تأكيد الاستعادة
    document.getElementById('confirmRestore').addEventListener('change', toggleRestoreButton);
}

/**
 * إنشاء نسخة احتياطية كاملة
 */
async function createBackup() {
    const includes = ['products', 'customers', 'suppliers', 'sales', 'purchases', 'payments', 'settings'];
    await performBackup(includes, 'manual');
}

/**
 * إنشاء نسخة احتياطية مخصصة
 */
async function createCustomBackup() {
    const includes = [];

    // جمع البيانات المحددة
    if (document.getElementById('backupProducts').checked) includes.push('products');
    if (document.getElementById('backupCustomers').checked) includes.push('customers');
    if (document.getElementById('backupSuppliers').checked) includes.push('suppliers');
    if (document.getElementById('backupSales').checked) includes.push('sales');
    if (document.getElementById('backupPurchases').checked) includes.push('purchases');
    if (document.getElementById('backupPayments').checked) includes.push('payments');
    if (document.getElementById('backupSettings').checked) includes.push('settings');

    if (includes.length === 0) {
        app.showAlert('خطأ', 'يرجى اختيار البيانات المراد نسخها احتياطياً');
        return;
    }

    await performBackup(includes, 'manual');
}

/**
 * تنفيذ عملية النسخ الاحتياطي
 */
async function performBackup(includes, type = 'manual') {
    try {
        // إظهار نافذة التقدم
        showProgressModal('إنشاء نسخة احتياطية', 'جاري تحضير البيانات...');

        const backupData = {
            version: '1.0',
            createdAt: new Date().toISOString(),
            type: type,
            includes: includes,
            data: {}
        };

        let progress = 0;
        const totalSteps = includes.length;

        // جمع البيانات
        for (let i = 0; i < includes.length; i++) {
            const dataType = includes[i];
            updateProgress(Math.round((i / totalSteps) * 50), `جاري نسخ ${getDataTypeName(dataType)}...`);

            switch (dataType) {
                case 'products':
                    backupData.data.products = db.getProducts();
                    break;
                case 'customers':
                    backupData.data.customers = db.getCustomers();
                    break;
                case 'suppliers':
                    backupData.data.suppliers = db.getSuppliers();
                    break;
                case 'sales':
                    backupData.data.sales = db.getSales();
                    break;
                case 'purchases':
                    backupData.data.purchases = db.getPurchases();
                    break;
                case 'payments':
                    backupData.data.payments = db.getPayments();
                    break;
                case 'settings':
                    backupData.data.settings = db.getSettings();
                    break;
            }

            // محاكاة وقت المعالجة
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        updateProgress(75, 'جاري ضغط البيانات...');

        // تحويل البيانات إلى JSON
        const jsonData = JSON.stringify(backupData, null, 2);
        const dataSize = new Blob([jsonData]).size;

        updateProgress(90, 'جاري حفظ النسخة الاحتياطية...');

        // إنشاء معرف فريد للنسخة الاحتياطية
        const backupId = 'backup_' + Date.now();

        // حفظ النسخة الاحتياطية في localStorage
        localStorage.setItem(`backup_${backupId}`, jsonData);

        // إضافة إلى السجل
        const backupRecord = {
            id: backupId,
            createdAt: backupData.createdAt,
            type: type,
            includes: includes,
            size: dataSize,
            status: 'success'
        };

        backupHistory.unshift(backupRecord);
        db.saveBackupHistory(backupHistory);

        updateProgress(100, 'تم إنشاء النسخة الاحتياطية بنجاح');

        // إخفاء نافذة التقدم بعد ثانيتين
        setTimeout(() => {
            hideProgressModal();

            // تحديث العرض
            loadBackupHistory();
            updateBackupStats();

            // عرض رسالة نجاح مع خيار التحميل
            app.showConfirm(
                'تم إنشاء النسخة الاحتياطية',
                'تم إنشاء النسخة الاحتياطية بنجاح. هل تريد تحميلها الآن؟',
                () => downloadBackup(backupId),
                'تحميل',
                'إغلاق'
            );
        }, 2000);

    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        hideProgressModal();
        app.showAlert('خطأ', 'حدث خطأ في إنشاء النسخة الاحتياطية');
    }
}

/**
 * عرض نافذة الاستعادة
 */
function showRestoreModal() {
    // تحديث قائمة النسخ الاحتياطي في السجل
    const historySelect = document.getElementById('historyBackupSelect');
    const successfulBackups = backupHistory.filter(b => b.status === 'success');

    historySelect.innerHTML = '<option value="">اختر نسخة احتياطية</option>' +
        successfulBackups.map(backup => `
            <option value="${backup.id}">
                ${db.formatDateTime(backup.createdAt)} - ${getDataTypeName(backup.includes.join(','))}
            </option>
        `).join('');

    app.showModal('restoreModal');
}

/**
 * تبديل طريقة الاستعادة
 */
function toggleRestoreMethod() {
    const method = document.querySelector('input[name="restoreMethod"]:checked').value;
    const fileSection = document.getElementById('fileRestoreSection');
    const historySection = document.getElementById('historyRestoreSection');

    if (method === 'file') {
        fileSection.style.display = 'block';
        historySection.style.display = 'none';
    } else {
        fileSection.style.display = 'none';
        historySection.style.display = 'block';
    }

    toggleRestoreButton();
}

/**
 * التحقق من ملف الاستعادة
 */
function validateRestoreFile() {
    const fileInput = document.getElementById('restoreFile');
    const fileInfo = document.getElementById('fileInfo');
    const file = fileInput.files[0];

    if (!file) {
        fileInfo.style.display = 'none';
        toggleRestoreButton();
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);

            // التحقق من صحة البيانات
            if (!backupData.version || !backupData.data || !backupData.includes) {
                throw new Error('ملف غير صحيح');
            }

            // عرض معلومات الملف
            fileInfo.innerHTML = `
                <div class="backup-file-info">
                    <h4>معلومات النسخة الاحتياطية</h4>
                    <p><strong>التاريخ:</strong> ${db.formatDateTime(backupData.createdAt)}</p>
                    <p><strong>الإصدار:</strong> ${backupData.version}</p>
                    <p><strong>البيانات المشمولة:</strong></p>
                    <ul>
                        ${backupData.includes.map(item => `<li>${getDataTypeName(item)}</li>`).join('')}
                    </ul>
                </div>
            `;
            fileInfo.style.display = 'block';

        } catch (error) {
            fileInfo.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    ملف النسخة الاحتياطية غير صحيح أو تالف
                </div>
            `;
            fileInfo.style.display = 'block';
        }

        toggleRestoreButton();
    };

    reader.readAsText(file);
}

/**
 * تفعيل/إلغاء زر الاستعادة
 */
function toggleRestoreButton() {
    const restoreButton = document.getElementById('restoreButton');
    const confirmRestore = document.getElementById('confirmRestore').checked;
    const method = document.querySelector('input[name="restoreMethod"]:checked').value;

    let hasValidSource = false;

    if (method === 'file') {
        const fileInput = document.getElementById('restoreFile');
        hasValidSource = fileInput.files.length > 0;
    } else {
        const historySelect = document.getElementById('historyBackupSelect');
        hasValidSource = historySelect.value !== '';
    }

    restoreButton.disabled = !(confirmRestore && hasValidSource);
}

/**
 * تنفيذ عملية الاستعادة
 */
async function performRestore() {
    try {
        const method = document.querySelector('input[name="restoreMethod"]:checked').value;
        let backupData;

        // الحصول على بيانات النسخة الاحتياطية
        if (method === 'file') {
            const fileInput = document.getElementById('restoreFile');
            const file = fileInput.files[0];
            const fileContent = await readFileAsText(file);
            backupData = JSON.parse(fileContent);
        } else {
            const backupId = document.getElementById('historyBackupSelect').value;
            const storedData = localStorage.getItem(`backup_${backupId}`);
            backupData = JSON.parse(storedData);
        }

        // إخفاء نافذة الاستعادة
        app.hideModal('restoreModal');

        // إظهار نافذة التقدم
        showProgressModal('استعادة البيانات', 'جاري تحضير الاستعادة...');

        const totalSteps = backupData.includes.length;

        // استعادة البيانات
        for (let i = 0; i < backupData.includes.length; i++) {
            const dataType = backupData.includes[i];
            updateProgress(Math.round((i / totalSteps) * 90), `جاري استعادة ${getDataTypeName(dataType)}...`);

            switch (dataType) {
                case 'products':
                    if (backupData.data.products) {
                        localStorage.setItem('products', JSON.stringify(backupData.data.products));
                    }
                    break;
                case 'customers':
                    if (backupData.data.customers) {
                        localStorage.setItem('customers', JSON.stringify(backupData.data.customers));
                    }
                    break;
                case 'suppliers':
                    if (backupData.data.suppliers) {
                        localStorage.setItem('suppliers', JSON.stringify(backupData.data.suppliers));
                    }
                    break;
                case 'sales':
                    if (backupData.data.sales) {
                        localStorage.setItem('sales', JSON.stringify(backupData.data.sales));
                    }
                    break;
                case 'purchases':
                    if (backupData.data.purchases) {
                        localStorage.setItem('purchases', JSON.stringify(backupData.data.purchases));
                    }
                    break;
                case 'payments':
                    if (backupData.data.payments) {
                        localStorage.setItem('payments', JSON.stringify(backupData.data.payments));
                    }
                    break;
                case 'settings':
                    if (backupData.data.settings) {
                        // حفظ الإعدادات بالمفتاح الصحيح
                        localStorage.setItem('technoflash_settings', JSON.stringify(backupData.data.settings));
                        localStorage.setItem('settings', JSON.stringify(backupData.data.settings)); // للتوافق مع النسخ القديمة
                    }
                    break;
            }

            // محاكاة وقت المعالجة
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        updateProgress(100, 'تم استعادة البيانات بنجاح');

        // إخفاء نافذة التقدم بعد ثانيتين
        setTimeout(() => {
            hideProgressModal();

            app.showConfirm(
                'تم استعادة البيانات',
                'تم استعادة البيانات بنجاح. يُنصح بإعادة تحميل الصفحة لتطبيق التغييرات.',
                () => location.reload(),
                'إعادة تحميل',
                'إغلاق'
            );
        }, 2000);

    } catch (error) {
        console.error('خطأ في استعادة البيانات:', error);
        hideProgressModal();
        app.showAlert('خطأ', 'حدث خطأ في استعادة البيانات');
    }
}

/**
 * استعادة من السجل
 */
function restoreFromHistory(backupId) {
    document.querySelector('input[name="restoreMethod"][value="history"]').checked = true;
    document.getElementById('historyBackupSelect').value = backupId;
    toggleRestoreMethod();
    showRestoreModal();
}

/**
 * تحميل نسخة احتياطية
 */
function downloadBackup(backupId) {
    try {
        const backupData = localStorage.getItem(`backup_${backupId}`);
        if (!backupData) {
            app.showAlert('خطأ', 'لم يتم العثور على النسخة الاحتياطية');
            return;
        }

        const backup = backupHistory.find(b => b.id === backupId);
        const filename = `technoflash_backup_${db.formatDate(backup.createdAt.split('T')[0])}.json`;

        const blob = new Blob([backupData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        app.showNotification('تم تحميل النسخة الاحتياطية', 'success');

    } catch (error) {
        console.error('خطأ في تحميل النسخة الاحتياطية:', error);
        app.showAlert('خطأ', 'حدث خطأ في تحميل النسخة الاحتياطية');
    }
}

/**
 * حذف نسخة احتياطية
 */
function deleteBackup(backupId) {
    app.showConfirm(
        'حذف النسخة الاحتياطية',
        'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.',
        () => {
            try {
                // حذف من localStorage
                localStorage.removeItem(`backup_${backupId}`);

                // حذف من السجل
                backupHistory = backupHistory.filter(b => b.id !== backupId);
                db.saveBackupHistory(backupHistory);

                // تحديث العرض
                displayBackupHistory();
                updateBackupStats();

                app.showNotification('تم حذف النسخة الاحتياطية', 'success');

            } catch (error) {
                console.error('خطأ في حذف النسخة الاحتياطية:', error);
                app.showAlert('خطأ', 'حدث خطأ في حذف النسخة الاحتياطية');
            }
        }
    );
}

/**
 * تصفية سجل النسخ الاحتياطي
 */
function filterBackupHistory() {
    const searchTerm = document.getElementById('backupSearch').value.toLowerCase();

    if (!searchTerm) {
        displayBackupHistory();
        return;
    }

    const filteredHistory = backupHistory.filter(backup => {
        const searchableText = [
            db.formatDateTime(backup.createdAt),
            backup.type === 'auto' ? 'تلقائي' : 'يدوي',
            backup.includes.map(item => getDataTypeName(item)).join(' '),
            backup.status === 'success' ? 'نجح' : 'فشل'
        ].join(' ').toLowerCase();

        return searchableText.includes(searchTerm);
    });

    displayBackupHistory(filteredHistory);
}

/**
 * تحديد جميع خيارات النسخ الاحتياطي
 */
function selectAllBackupOptions() {
    document.querySelectorAll('.backup-option input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

/**
 * إلغاء تحديد جميع خيارات النسخ الاحتياطي
 */
function clearAllBackupOptions() {
    document.querySelectorAll('.backup-option input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}

/**
 * إظهار نافذة التقدم
 */
function showProgressModal(title, details) {
    document.getElementById('progressTitle').textContent = title;
    document.getElementById('progressDetails').textContent = details;
    document.getElementById('progressFill').style.width = '0%';
    document.getElementById('progressText').textContent = '٠%';
    app.showModal('progressModal');
}

/**
 * تحديث شريط التقدم
 */
function updateProgress(percentage, details) {
    document.getElementById('progressFill').style.width = percentage + '%';
    document.getElementById('progressText').textContent = db.toArabicNumerals(percentage) + '%';
    document.getElementById('progressDetails').textContent = details;
}

/**
 * إخفاء نافذة التقدم
 */
function hideProgressModal() {
    app.hideModal('progressModal');
}

/**
 * قراءة ملف كنص
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = e => reject(e);
        reader.readAsText(file);
    });
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '٠ بايت';

    const k = 1024;
    const sizes = ['بايت', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
    return db.toArabicNumerals(size) + ' ' + sizes[i];
}

/**
 * الحصول على اسم نوع البيانات
 */
function getDataTypeName(dataType) {
    const names = {
        'products': 'المنتجات',
        'customers': 'العملاء',
        'suppliers': 'الموردين',
        'sales': 'المبيعات',
        'purchases': 'المشتريات',
        'payments': 'المدفوعات',
        'settings': 'الإعدادات'
    };

    return names[dataType] || dataType;
}

/**
 * إنشاء نسخة احتياطية تلقائية
 */
async function createAutoBackup() {
    const settings = db.getSettings();
    if (!settings?.backup?.autoBackup) return;

    const includes = ['products', 'customers', 'suppliers', 'sales', 'purchases', 'payments', 'settings'];
    await performBackup(includes, 'auto');

    // تنظيف النسخ القديمة
    cleanupOldBackups();
}

/**
 * تنظيف النسخ الاحتياطية القديمة
 */
function cleanupOldBackups() {
    const settings = db.getSettings();
    const maxBackups = settings?.backup?.maxBackups || 7;

    const autoBackups = backupHistory
        .filter(b => b.type === 'auto' && b.status === 'success')
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    if (autoBackups.length > maxBackups) {
        const backupsToDelete = autoBackups.slice(maxBackups);

        backupsToDelete.forEach(backup => {
            // حذف من localStorage
            localStorage.removeItem(`backup_${backup.id}`);

            // حذف من السجل
            backupHistory = backupHistory.filter(b => b.id !== backup.id);
        });

        db.saveBackupHistory(backupHistory);
    }
}

/**
 * جدولة النسخ الاحتياطي التلقائي
 */
function scheduleAutoBackup() {
    const settings = db.getSettings();
    if (!settings?.backup?.autoBackup) return;

    const frequency = settings.backup.backupFrequency;
    const backupTime = settings.backup.backupTime;

    // هذه وظيفة مبسطة - في التطبيق الحقيقي ستحتاج إلى مجدول أكثر تعقيداً
    const now = new Date();
    const [hours, minutes] = backupTime.split(':');
    const scheduledTime = new Date();
    scheduledTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    if (scheduledTime <= now) {
        scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const timeUntilBackup = scheduledTime.getTime() - now.getTime();

    setTimeout(() => {
        createAutoBackup();
        // إعادة جدولة للمرة القادمة
        scheduleAutoBackup();
    }, timeUntilBackup);
}

/**
 * إظهار نافذة ضبط المصنع
 */
function showFactoryResetModal() {
    // إعادة ضبط النموذج
    document.getElementById('createBackupBeforeReset').checked = true;
    document.getElementById('resetSettings').checked = false;
    document.getElementById('factoryResetConfirmation').value = '';
    document.getElementById('factoryResetButton').disabled = true;

    app.showModal('factoryResetModal');
}

/**
 * التحقق من تأكيد ضبط المصنع
 */
function validateFactoryResetConfirmation() {
    try {
        const confirmationInput = document.getElementById('factoryResetConfirmation');
        const resetButton = document.getElementById('factoryResetButton');

        if (!confirmationInput || !resetButton) {
            console.error('لم يتم العثور على عناصر التأكيد');
            return;
        }

        const confirmationText = confirmationInput.value.trim();

        if (confirmationText === 'ضبط المصنع') {
            resetButton.disabled = false;
            resetButton.classList.remove('btn-secondary');
            resetButton.classList.add('btn-danger');
        } else {
            resetButton.disabled = true;
            resetButton.classList.remove('btn-danger');
            resetButton.classList.add('btn-secondary');
        }
    } catch (error) {
        console.error('خطأ في التحقق من التأكيد:', error);
    }
}

/**
 * تنفيذ ضبط المصنع
 */
async function performFactoryReset() {
    // التحقق من وجود العناصر المطلوبة
    const backupCheckbox = document.getElementById('createBackupBeforeReset');
    const settingsCheckbox = document.getElementById('resetSettings');

    if (!backupCheckbox || !settingsCheckbox) {
        app.showAlert('خطأ', 'لم يتم العثور على عناصر النموذج المطلوبة');
        return;
    }

    const shouldCreateBackup = backupCheckbox.checked;
    const resetSettings = settingsCheckbox.checked;

    try {
        // إظهار نافذة التقدم
        showProgressModal('ضبط المصنع', 'جاري التحضير...');

        let currentStep = 0;
        const totalSteps = shouldCreateBackup ? 8 : 7;

        // إنشاء نسخة احتياطية قبل الحذف إذا طُلب ذلك
        if (shouldCreateBackup) {
            updateProgress(++currentStep, totalSteps, 'إنشاء نسخة احتياطية...');
            try {
                await createBackup();
            } catch (backupError) {
                console.warn('فشل في إنشاء النسخة الاحتياطية:', backupError);
                // المتابعة حتى لو فشلت النسخة الاحتياطية
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // حذف البيانات خطوة بخطوة
        updateProgress(++currentStep, totalSteps, 'حذف المنتجات...');
        localStorage.removeItem('products');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف العملاء...');
        localStorage.removeItem('customers');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف الموردين...');
        localStorage.removeItem('suppliers');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف المبيعات...');
        localStorage.removeItem('sales');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف المشتريات...');
        localStorage.removeItem('purchases');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف المدفوعات والديون...');
        localStorage.removeItem('payments');
        localStorage.removeItem('debts');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(++currentStep, totalSteps, 'حذف النسخ الاحتياطي...');
        // حذف جميع النسخ الاحتياطي
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('backup_')) {
                localStorage.removeItem(key);
            }
        });
        localStorage.removeItem('backupHistory');
        backupHistory = [];
        await new Promise(resolve => setTimeout(resolve, 500));

        // إعادة ضبط الإعدادات إذا طُلب ذلك
        if (resetSettings) {
            updateProgress(++currentStep, totalSteps, 'إعادة ضبط الإعدادات...');
            // حذف الإعدادات بالمفاتيح الصحيحة
            localStorage.removeItem('technoflash_settings');
            localStorage.removeItem('settings');
            localStorage.removeItem('technoflash_db');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        updateProgress(totalSteps, totalSteps, 'تم ضبط المصنع بنجاح!');

        // إنشاء إعدادات افتراضية جديدة إذا تم حذف الإعدادات
        if (resetSettings) {
            try {
                // إنشاء إعدادات افتراضية
                const defaultSettings = {
                    general: {
                        language: 'ar',
                        currency: 'EGP',
                        dateType: 'gregorian',
                        numberType: 'arabic',
                        darkMode: false,
                        soundEffects: true
                    },
                    company: {
                        companyName: 'تكنوفلاش',
                        commercialRegister: '',
                        taxNumber: '',
                        phone: '',
                        address: '',
                        email: '',
                        website: '',
                        logo: ''
                    },
                    pos: {
                        defaultPaymentMethod: 'cash',
                        invoiceNumberLength: 6,
                        autoSave: true,
                        autoPrint: false,
                        barcodeScanner: true,
                        lowStockAlert: true,
                        lowStockThreshold: 10
                    }
                };

                // حفظ الإعدادات الافتراضية
                localStorage.setItem('technoflash_settings', JSON.stringify(defaultSettings));
                console.log('تم إنشاء إعدادات افتراضية جديدة بعد ضبط المصنع');
            } catch (settingsError) {
                console.error('خطأ في إنشاء الإعدادات الافتراضية:', settingsError);
            }
        }

        // إخفاء نافذة التقدم بعد ثانيتين
        setTimeout(() => {
            app.hideModal('progressModal');
            app.hideModal('factoryResetModal');

            // إظهار رسالة نجاح
            app.showAlert('نجح ضبط المصنع',
                'تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');

            // إعادة تحميل الصفحة بعد 3 ثوان
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }, 2000);

    } catch (error) {
        console.error('خطأ في ضبط المصنع:', error);
        console.error('تفاصيل الخطأ:', error.stack);

        // إخفاء نافذة التقدم
        try {
            if (typeof app !== 'undefined' && app.hideModal) {
                app.hideModal('progressModal');
            }
        } catch (modalError) {
            console.error('خطأ في إخفاء نافذة التقدم:', modalError);
        }

        // إظهار رسالة الخطأ
        try {
            if (typeof app !== 'undefined' && app.showAlert) {
                app.showAlert('خطأ', 'حدث خطأ أثناء ضبط المصنع: ' + (error.message || 'خطأ غير معروف'));
            } else {
                alert('حدث خطأ أثناء ضبط المصنع: ' + (error.message || 'خطأ غير معروف'));
            }
        } catch (alertError) {
            console.error('خطأ في إظهار رسالة الخطأ:', alertError);
            alert('حدث خطأ أثناء ضبط المصنع');
        }
    }
}

/**
 * إظهار نافذة التقدم
 */
function showProgressModal(title, details) {
    try {
        const titleElement = document.getElementById('progressTitle');
        const detailsElement = document.getElementById('progressDetails');
        const fillElement = document.getElementById('progressFill');
        const textElement = document.getElementById('progressText');

        if (titleElement) titleElement.textContent = title;
        if (detailsElement) detailsElement.textContent = details;
        if (fillElement) fillElement.style.width = '0%';
        if (textElement) textElement.textContent = '٠%';

        if (typeof app !== 'undefined' && app.showModal) {
            app.showModal('progressModal');
        }
    } catch (error) {
        console.error('خطأ في إظهار نافذة التقدم:', error);
    }
}

/**
 * تحديث شريط التقدم
 */
function updateProgress(current, total, details) {
    try {
        const percentage = Math.round((current / total) * 100);

        const fillElement = document.getElementById('progressFill');
        const textElement = document.getElementById('progressText');
        const detailsElement = document.getElementById('progressDetails');

        if (fillElement) {
            fillElement.style.width = percentage + '%';
        }

        if (textElement) {
            // استخدام db.toArabicNumerals إذا كان متاحاً، وإلا استخدام الرقم العادي
            const percentageText = (typeof db !== 'undefined' && db.toArabicNumerals)
                ? db.toArabicNumerals(percentage) + '%'
                : percentage + '%';
            textElement.textContent = percentageText;
        }

        if (detailsElement) {
            detailsElement.textContent = details;
        }
    } catch (error) {
        console.error('خطأ في تحديث شريط التقدم:', error);
    }
}
