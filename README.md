# تكنوفلاش - نظام نقطة البيع العربي

## نظرة عامة

تكنوفلاش هو نظام نقطة بيع شامل مصمم خصيصاً للأسواق العربية. يدعم النظام اللغة العربية بالكامل مع التصميم من اليمين إلى اليسار (RTL) والأرقام العربية الهندية.

## المميزات الرئيسية

### 🔐 المصادقة والأمان
- نظام تسجيل دخول آمن
- إدارة جلسات المستخدمين
- حماية البيانات المحلية

### 📊 لوحة المعلومات
- إحصائيات شاملة للمبيعات والمشتريات
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية
- قائمة أفضل المنتجات مبيعاً
- متابعة العملاء المدينين

### 🛍️ إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- دعم الصور والباركود
- تتبع المخزون
- فئات المنتجات
- تنبيهات نفاد المخزون
- استيراد المنتجات من ملف Excel

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع الديون والمدفوعات
- سجل المعاملات
- إحصائيات العملاء

### 🏢 إدارة الموردين
- معلومات الموردين
- سجل المشتريات
- إدارة الديون للموردين
- تقارير الموردين

### 💰 المبيعات
- واجهة بيع سريعة وسهلة
- بحث المنتجات بالاسم أو الباركود
- حساب الضرائب والخصومات
- طباعة الفواتير
- دعم طرق الدفع المختلفة

### 📦 المشتريات
- تسجيل المشتريات
- ربط المشتريات بالموردين
- تحديث المخزون تلقائياً
- تتبع تكاليف المنتجات

### 💳 إدارة الديون والمدفوعات
- تتبع ديون العملاء
- تسجيل المدفوعات
- تقارير الديون
- تنبيهات الاستحقاق

### 📈 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير المشتريات
- تقارير المخزون
- التقارير المالية
- تصدير التقارير بصيغ مختلفة

### ⚙️ الإعدادات
- إعدادات الشركة
- إعدادات نقطة البيع
- إدارة الضرائب
- إعدادات التنبيهات
- تخصيص الواجهة

### 💾 النسخ الاحتياطي والاستعادة
- إنشاء نسخ احتياطية تلقائية
- استعادة البيانات
- تصدير واستيراد البيانات
- حماية البيانات

## التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (Vanilla)** - المنطق والتفاعل
- **LocalStorage** - تخزين البيانات المحلي
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي

## متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- دعم JavaScript
- دعم LocalStorage

## التثبيت والتشغيل

1. قم بتحميل جميع الملفات
2. افتح ملف `index.html` في المتصفح
3. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## هيكل الملفات

```
├── index.html          # الصفحة الرئيسية
├── style.css          # ملف الأنماط
├── app.js             # التطبيق الرئيسي
├── main.js            # إدارة التنقل
├── database.js        # إدارة قاعدة البيانات
├── dashboard.js       # لوحة المعلومات
├── products.js        # إدارة المنتجات
├── customers.js       # إدارة العملاء
├── suppliers.js       # إدارة الموردين
├── sales.js           # المبيعات
├── purchases.js       # المشتريات
├── debts.js           # الديون والمدفوعات
├── reports.js         # التقارير
├── settings.js        # الإعدادات
├── backup.js          # النسخ الاحتياطي
└── README.md          # هذا الملف
```

## الاستخدام

### تسجيل الدخول
1. افتح التطبيق في المتصفح
2. أدخل اسم المستخدم وكلمة المرور
3. انقر على "تسجيل الدخول"

### إضافة منتج جديد
1. انتقل إلى صفحة "المنتجات"
2. انقر على "إضافة منتج جديد"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إجراء عملية بيع
1. انتقل إلى صفحة "المبيعات"
2. ابحث عن المنتج أو امسح الباركود
3. أضف المنتجات إلى السلة
4. أكمل عملية البيع

### إنشاء تقرير
1. انتقل إلى صفحة "التقارير"
2. اختر نوع التقرير والفترة الزمنية
3. انقر على "إنشاء التقرير"
4. يمكنك تصدير التقرير أو طباعته

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## المساهمة

نرحب بالمساهمات لتحسين النظام. يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

---

**تكنوفلاش** - نظام نقطة البيع العربي الشامل
