<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الطباعة الحرارية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #34495e;
            margin-bottom: 15px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #2980b9;
        }
        .success {
            background: #27ae60;
        }
        .warning {
            background: #f39c12;
        }
        .danger {
            background: #e74c3c;
        }
        .info {
            background: #17a2b8;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ اختبار نظام الطباعة الحرارية المحسن</h1>
        
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <button onclick="checkSystemStatus()" class="info">فحص حالة النظام</button>
            <button onclick="showPrinterInfo()" class="info">عرض معلومات النظام</button>
            <div id="systemStatus"></div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الطباعة</h3>
            <button onclick="testPrinter()" class="success">اختبار الطابعة الحرارية</button>
            <button onclick="testThermalInvoice()" class="warning">اختبار فاتورة حرارية</button>
            <button onclick="testBarcodeLabel()" class="warning">اختبار ملصق باركود</button>
            <button onclick="testStandardInvoice()" class="warning">اختبار فاتورة عادية</button>
        </div>

        <div class="test-section">
            <h3>⚙️ إدارة الإعدادات</h3>
            <button onclick="showCurrentSettings()" class="info">عرض الإعدادات الحالية</button>
            <button onclick="resetSettings()" class="danger">إعادة تعيين الإعدادات</button>
            <button onclick="exportSettings()" class="success">تصدير الإعدادات</button>
            <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importSettings(this.files[0])">
            <button onclick="document.getElementById('importFile').click()" class="success">استيراد الإعدادات</button>
        </div>

        <div class="test-section">
            <h3>📋 سجل الأحداث</h3>
            <button onclick="clearLog()" class="danger">مسح السجل</button>
            <div id="logContainer" style="background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto; margin-top: 10px;"></div>
        </div>
    </div>

    <!-- تضمين ملف النظام -->
    <script src="thermal-printer.js"></script>
    
    <!-- محاكاة قاعدة البيانات للاختبار -->
    <script>
        // محاكاة قاعدة البيانات
        const db = {
            getSale: (id) => ({
                id: id || 'test-sale-001',
                date: new Date().toISOString(),
                customerId: 'guest',
                items: [
                    { productId: 'prod-001', quantity: 2, price: 25.50 },
                    { productId: 'prod-002', quantity: 1, price: 15.00 }
                ],
                total: 66.00,
                subtotal: 66.00,
                tax: 0,
                discount: 0,
                paymentMethod: 'cash',
                amountPaid: 70.00
            }),
            getProduct: (id) => ({
                id: id,
                name: 'منتج تجريبي ' + id,
                code: 'CODE-' + id,
                price: 25.50,
                barcode: '*********0123'
            }),
            getCustomer: (id) => id === 'guest' ? null : ({
                id: id,
                name: 'عميل تجريبي',
                phone: '0*********0',
                email: '<EMAIL>',
                address: 'عنوان تجريبي'
            }),
            getSettings: () => ({
                company: {
                    companyName: 'متجر تكنوفلاش التجريبي',
                    phone: '0*********0',
                    address: 'القاهرة، مصر',
                    email: '<EMAIL>',
                    taxNumber: '*********',
                    commercialRegister: 'CR-123456'
                }
            }),
            formatCurrency: (amount) => parseFloat(amount).toFixed(2) + ' ج.م',
            formatDate: (date, includeTime = false) => {
                const d = new Date(date);
                return d.toLocaleDateString('ar-SA') + (includeTime ? ' ' + d.toLocaleTimeString('ar-SA') : '');
            }
        };

        // محاكاة تطبيق النظام
        const app = {
            showAlert: (title, message) => {
                alert(title + ': ' + message);
                logMessage('تنبيه: ' + title + ' - ' + message, 'error');
            },
            showNotification: (message, type = 'info') => {
                logMessage('إشعار: ' + message, type);
            }
        };

        // دالة تسجيل الأحداث
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}\n`;
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دوال الاختبار
        function checkSystemStatus() {
            const status = checkPrinterAvailability();
            const statusDiv = document.getElementById('systemStatus');
            
            if (status.available) {
                statusDiv.innerHTML = '<div class="status success">✅ النظام جاهز والطابعة متاحة</div>';
                logMessage('فحص النظام: جاهز', 'success');
            } else {
                statusDiv.innerHTML = `<div class="status error">❌ مشكلة: ${status.error}</div>`;
                logMessage('فحص النظام: ' + status.error, 'error');
            }
        }

        function showPrinterInfo() {
            const info = showThermalPrinterInfo();
            logMessage('معلومات النظام: ' + JSON.stringify(info, null, 2), 'info');
        }

        function testPrinter() {
            logMessage('بدء اختبار الطابعة...', 'info');
            testThermalPrinter();
        }

        function testThermalInvoice() {
            logMessage('بدء اختبار الفاتورة الحرارية...', 'info');
            printThermalInvoice(db.getSale());
        }

        function testBarcodeLabel() {
            logMessage('بدء اختبار ملصق الباركود...', 'info');
            printProductBarcodeLabel('prod-001');
        }

        function testStandardInvoice() {
            logMessage('بدء اختبار الفاتورة العادية...', 'info');
            printStandardInvoice(db.getSale());
        }

        function showCurrentSettings() {
            const settings = getThermalPrinterSettings();
            logMessage('الإعدادات الحالية: ' + JSON.stringify(settings, null, 2), 'info');
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) {
                resetThermalPrinterSettings();
                logMessage('تم إعادة تعيين الإعدادات', 'success');
            }
        }

        function exportSettings() {
            exportThermalPrinterSettings();
            logMessage('تم تصدير الإعدادات', 'success');
        }

        function importSettings(file) {
            if (file) {
                importThermalPrinterSettings(file)
                    .then(() => {
                        logMessage('تم استيراد الإعدادات بنجاح', 'success');
                    })
                    .catch(error => {
                        logMessage('فشل استيراد الإعدادات: ' + error.message, 'error');
                    });
            }
        }

        function clearLog() {
            document.getElementById('logContainer').textContent = '';
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            logMessage('تم تحميل صفحة الاختبار', 'success');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
