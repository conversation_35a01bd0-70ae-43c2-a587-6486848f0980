<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات النهائية - تكنوفلاش</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-check-double"></i>
            اختبار الإصلاحات النهائية للمبيعات
        </h1>

        <!-- اختبار دوال الطباعة -->
        <div class="test-section">
            <h3><i class="fas fa-print"></i> اختبار دوال الطباعة</h3>
            <div id="printResults"></div>
            <button class="btn btn-success" onclick="testPrintFunctions()">
                <i class="fas fa-play"></i> اختبار الطباعة
            </button>
        </div>

        <!-- اختبار التنسيق -->
        <div class="test-section">
            <h3><i class="fas fa-palette"></i> اختبار التنسيق</h3>
            <div id="cssResults"></div>
            <button class="btn btn-info" onclick="testCSS()">
                <i class="fas fa-play"></i> اختبار التنسيق
            </button>
        </div>

        <!-- جدول تجريبي -->
        <div class="test-section">
            <h3><i class="fas fa-table"></i> جدول المبيعات التجريبي</h3>
            <div class="sales-container">
                <div class="card">
                    <div class="card-header">
                        <h4>المنتجات المضافة</h4>
                    </div>
                    <div class="card-body">
                        <table class="table" id="testTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>منتج تجريبي طويل الاسم</td>
                                    <td>25.50 ج.م</td>
                                    <td>2</td>
                                    <td>51.00 ج.م</td>
                                    <td>
                                        <button class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- النتائج النهائية -->
        <div class="test-section">
            <h3><i class="fas fa-trophy"></i> النتائج النهائية</h3>
            <div id="finalResults"></div>
            <button class="btn btn-warning" onclick="runAllTests()">
                <i class="fas fa-play-circle"></i> تشغيل جميع الاختبارات
            </button>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    <script src="fix-sales-css.js"></script>
    <script src="main.js"></script>
    <script src="app.js"></script>
    <script src="sales.js"></script>

    <script>
        let testResults = {};

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result test-${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function testPrintFunctions() {
            const resultsDiv = document.getElementById('printResults');
            resultsDiv.innerHTML = '';
            
            const functions = [
                'printThermalInvoice',
                'printStandardInvoice', 
                'printSaleItemsLabels',
                'createStandardInvoiceHTML',
                'convertNumberToArabicWords',
                'testThermalPrinter',
                'ESC_POS_COMMANDS'
            ];
            
            let allAvailable = true;
            functions.forEach(funcName => {
                const available = typeof window[funcName] !== 'undefined';
                if (!available) allAvailable = false;
                showResult('printResults',
                    `${available ? '✅' : '❌'} ${funcName}: ${available ? 'متوفر' : 'غير متوفر'}`,
                    available ? 'success' : 'error'
                );
            });
            
            testResults.printFunctions = allAvailable;
        }

        function testCSS() {
            const resultsDiv = document.getElementById('cssResults');
            resultsDiv.innerHTML = '';
            
            // اختبار وجود الأنماط
            const fixStyle = document.getElementById('sales-css-fix');
            const hasFixCSS = fixStyle !== null;
            showResult('cssResults',
                `${hasFixCSS ? '✅' : '❌'} إصلاحات CSS: ${hasFixCSS ? 'مطبقة' : 'غير مطبقة'}`,
                hasFixCSS ? 'success' : 'error'
            );
            
            // اختبار الجدول
            const table = document.getElementById('testTable');
            if (table) {
                const computedStyle = window.getComputedStyle(table);
                const hasFullWidth = computedStyle.width === '100%' || table.offsetWidth > 0;
                showResult('cssResults',
                    `${hasFullWidth ? '✅' : '❌'} عرض الجدول: ${hasFullWidth ? 'صحيح' : 'خطأ'}`,
                    hasFullWidth ? 'success' : 'error'
                );
            }
            
            // اختبار الدوال المساعدة
            const hasFunctions = typeof window.fixSalesCSS === 'function';
            showResult('cssResults',
                `${hasFunctions ? '✅' : '❌'} دوال الإصلاح: ${hasFunctions ? 'متوفرة' : 'غير متوفرة'}`,
                hasFunctions ? 'success' : 'error'
            );
            
            testResults.css = hasFixCSS && hasFunctions;
        }

        function runAllTests() {
            const resultsDiv = document.getElementById('finalResults');
            resultsDiv.innerHTML = '';
            
            testPrintFunctions();
            testCSS();
            
            setTimeout(() => {
                const allPassed = Object.values(testResults).every(result => result === true);
                showResult('finalResults',
                    `<h4>${allPassed ? '🎉 جميع الاختبارات نجحت!' : '⚠️ بعض الاختبارات فشلت'}</h4>`,
                    allPassed ? 'success' : 'error'
                );
                
                if (allPassed) {
                    showResult('finalResults',
                        '✅ تم حل جميع مشاكل المبيعات بنجاح!<br>✅ دالة createStandardInvoiceHTML متوفرة<br>✅ دالة convertNumberToArabicWords متوفرة<br>✅ دالة printSaleItemsLabels متوفرة<br>✅ التنسيق يعمل بشكل صحيح',
                        'success'
                    );
                } else {
                    showResult('finalResults',
                        'يرجى مراجعة الأخطاء أعلاه وإصلاحها.',
                        'error'
                    );
                }
            }, 1000);
        }

        // تشغيل الاختبارات تلقائياً عند التحميل
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 2000);
        });
    </script>
</body>
</html>
