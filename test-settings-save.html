<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ الإعدادات - تكنوفلاش</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn-test {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-test:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> اختبار حفظ الإعدادات</h1>
        
        <div class="test-section">
            <h3>اختبار حفظ الإعدادات الأساسية</h3>
            <button class="btn-test" onclick="testBasicSave()">
                <i class="fas fa-play"></i> اختبار الحفظ الأساسي
            </button>
            <div id="basicSaveResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار localStorage</h3>
            <button class="btn-test" onclick="testLocalStorage()">
                <i class="fas fa-database"></i> اختبار localStorage
            </button>
            <div id="localStorageResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار العملة المصرية</h3>
            <button class="btn-test" onclick="testEgyptianCurrency()">
                <i class="fas fa-coins"></i> اختبار العملة
            </button>
            <div id="currencyResult"></div>
        </div>

        <div class="test-section">
            <h3>اختبار كامل للإعدادات</h3>
            <button class="btn-test" onclick="runFullTest()">
                <i class="fas fa-check-double"></i> تشغيل الاختبار الكامل
            </button>
            <div id="fullTestResult"></div>
        </div>

        <div class="test-section">
            <h3>عرض الإعدادات المحفوظة</h3>
            <button class="btn-test" onclick="showSavedSettings()">
                <i class="fas fa-eye"></i> عرض الإعدادات
            </button>
            <div id="savedSettingsResult"></div>
        </div>
    </div>

    <script src="database.js"></script>
    <script>
        // اختبار الحفظ الأساسي
        function testBasicSave() {
            const resultDiv = document.getElementById('basicSaveResult');
            resultDiv.innerHTML = '<div class="info">جاري الاختبار...</div>';
            
            try {
                // إنشاء بيانات تجريبية
                const testSettings = {
                    general: {
                        language: 'ar',
                        currency: 'EGP',
                        dateType: 'gregorian',
                        numberType: 'arabic'
                    },
                    company: {
                        companyName: 'شركة تجريبية',
                        phone: '0123456789',
                        address: 'عنوان تجريبي'
                    }
                };

                // محاولة الحفظ
                localStorage.setItem('test_settings', JSON.stringify(testSettings));
                
                // التحقق من الحفظ
                const saved = localStorage.getItem('test_settings');
                if (saved) {
                    const parsed = JSON.parse(saved);
                    if (parsed.general.currency === 'EGP') {
                        resultDiv.innerHTML = '<div class="success">✅ تم الحفظ بنجاح! العملة: ' + parsed.general.currency + '</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ خطأ في العملة المحفوظة</div>';
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ فشل في الحفظ</div>';
                }
                
                // تنظيف
                localStorage.removeItem('test_settings');
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ: ' + error.message + '</div>';
            }
        }

        // اختبار localStorage
        function testLocalStorage() {
            const resultDiv = document.getElementById('localStorageResult');
            resultDiv.innerHTML = '<div class="info">جاري فحص localStorage...</div>';
            
            try {
                // فحص الدعم
                if (typeof Storage === "undefined") {
                    resultDiv.innerHTML = '<div class="error">❌ localStorage غير مدعوم</div>';
                    return;
                }

                // فحص المساحة المتاحة
                const testKey = 'storage_test';
                const testValue = 'test_value_' + Date.now();
                
                localStorage.setItem(testKey, testValue);
                const retrieved = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);
                
                if (retrieved === testValue) {
                    resultDiv.innerHTML = '<div class="success">✅ localStorage يعمل بشكل صحيح</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ مشكلة في localStorage</div>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في localStorage: ' + error.message + '</div>';
            }
        }

        // اختبار العملة المصرية
        function testEgyptianCurrency() {
            const resultDiv = document.getElementById('currencyResult');
            resultDiv.innerHTML = '<div class="info">جاري اختبار العملة...</div>';
            
            try {
                // فحص إعدادات العملة الحالية
                const currentSettings = localStorage.getItem('technoflash_settings');
                let currency = 'غير محدد';
                
                if (currentSettings) {
                    const settings = JSON.parse(currentSettings);
                    currency = settings.general ? settings.general.currency : 'غير موجود';
                }
                
                // فحص تنسيق العملة
                if (typeof db !== 'undefined' && db.formatCurrency) {
                    const formatted = db.formatCurrency(100);
                    resultDiv.innerHTML = `
                        <div class="success">✅ العملة المحفوظة: ${currency}</div>
                        <div class="info">تنسيق العملة: ${formatted}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="success">✅ العملة المحفوظة: ${currency}</div>
                        <div class="error">❌ دالة تنسيق العملة غير متاحة</div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في اختبار العملة: ' + error.message + '</div>';
            }
        }

        // تشغيل الاختبار الكامل
        function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.innerHTML = '<div class="info">جاري تشغيل الاختبار الكامل...</div>';
            
            setTimeout(() => {
                testBasicSave();
                setTimeout(() => {
                    testLocalStorage();
                    setTimeout(() => {
                        testEgyptianCurrency();
                        resultDiv.innerHTML = '<div class="success">✅ تم تشغيل جميع الاختبارات</div>';
                    }, 500);
                }, 500);
            }, 500);
        }

        // عرض الإعدادات المحفوظة
        function showSavedSettings() {
            const resultDiv = document.getElementById('savedSettingsResult');
            
            try {
                const settings = localStorage.getItem('technoflash_settings');
                if (settings) {
                    const parsed = JSON.parse(settings);
                    resultDiv.innerHTML = `
                        <div class="success">✅ الإعدادات المحفوظة:</div>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
${JSON.stringify(parsed, null, 2)}
                        </pre>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="info">لا توجد إعدادات محفوظة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ خطأ في عرض الإعدادات: ' + error.message + '</div>';
            }
        }

        // تشغيل اختبار أولي عند التحميل
        window.addEventListener('load', function() {
            console.log('🧪 صفحة اختبار الإعدادات جاهزة');
        });
    </script>
</body>
</html>
