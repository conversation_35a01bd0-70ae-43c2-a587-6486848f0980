<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات المبيعات - تكنوفلاش</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #f0f2f5;
            padding: 20px;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .test-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1><i class="fas fa-tools"></i> اختبار إصلاحات المبيعات</h1>
        <p>فحص إصلاحات إتمام البيع وتنسيق الجداول</p>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار تنسيق الجدول</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testTableLayout()">
                            <i class="fas fa-table"></i> فحص الجدول
                        </button>
                        <button class="btn btn-success" onclick="fixTable()">
                            <i class="fas fa-wrench"></i> إصلاح الجدول
                        </button>
                        <div id="tableResults"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار إتمام البيع</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning" onclick="testSaleCompletion()">
                            <i class="fas fa-shopping-cart"></i> اختبار إتمام البيع
                        </button>
                        <div id="salesResults"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- محاكاة جدول المبيعات -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="sales-container">
                    <div class="card">
                        <div class="card-header">
                            <h3>جدول المنتجات (تجريبي)</h3>
                        </div>
                        <div class="card-body">
                            <div class="sale-items">
                                <table class="table" id="saleItemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>منتج تجريبي طويل الاسم جداً للاختبار</td>
                                            <td>25.50 ج.م</td>
                                            <td>2</td>
                                            <td>51.00 ج.م</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>منتج آخر</td>
                                            <td>15.00 ج.م</td>
                                            <td>1</td>
                                            <td>15.00 ج.م</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين الملفات -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    <script src="fix-sales-css.js"></script>
    
    <script>
        // محاكاة app object
        window.app = {
            showAlert: function(title, message) {
                alert(title + ': ' + message);
            },
            showNotification: function(message, type) {
                console.log(`${type}: ${message}`);
            },
            showLoading: function() {
                console.log('Loading...');
            },
            hideLoading: function() {
                console.log('Loading hidden');
            }
        };

        // تهيئة قاعدة البيانات
        let db;
        try {
            db = new Database();
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
        }

        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function testTableLayout() {
            const results = document.getElementById('tableResults');
            results.innerHTML = '';
            
            const table = document.getElementById('saleItemsTable');
            if (!table) {
                showResult('tableResults', '❌ لم يتم العثور على الجدول', 'error');
                return;
            }
            
            const computedStyle = window.getComputedStyle(table);
            const parentWidth = table.parentElement.offsetWidth;
            const tableWidth = table.offsetWidth;
            const widthPercentage = (tableWidth / parentWidth * 100).toFixed(2);
            
            showResult('tableResults', 
                `📏 عرض الجدول: ${widthPercentage}% (${tableWidth}px من ${parentWidth}px)`,
                'success'
            );
            
            showResult('tableResults',
                `🎨 CSS width: ${computedStyle.width}`,
                'success'
            );
            
            showResult('tableResults',
                `📐 Table layout: ${computedStyle.tableLayout}`,
                'success'
            );
            
            const isCorrect = computedStyle.width === '100%' && widthPercentage >= 95;
            showResult('tableResults',
                isCorrect ? '✅ الجدول يعمل بشكل صحيح!' : '❌ الجدول يحتاج إصلاح',
                isCorrect ? 'success' : 'error'
            );
        }

        function fixTable() {
            const results = document.getElementById('tableResults');
            showResult('tableResults', '🔧 جاري إصلاح الجدول...', 'warning');
            
            // تطبيق الإصلاحات
            if (typeof fixSalesCSS === 'function') {
                fixSalesCSS();
            }
            
            // إصلاح مباشر
            const table = document.getElementById('saleItemsTable');
            if (table) {
                table.style.width = '100%';
                table.style.maxWidth = '100%';
                table.style.tableLayout = 'fixed';
                table.style.borderCollapse = 'collapse';
                
                // إصلاح الحاوي
                const container = table.closest('.card-body');
                if (container) {
                    container.style.width = '100%';
                    container.style.overflow = 'visible';
                }
                
                // إجبار إعادة رسم
                table.style.display = 'none';
                table.offsetHeight;
                table.style.display = 'table';
            }
            
            setTimeout(() => {
                showResult('tableResults', '✅ تم تطبيق الإصلاحات!', 'success');
                setTimeout(testTableLayout, 500);
            }, 1000);
        }

        function testSaleCompletion() {
            const results = document.getElementById('salesResults');
            results.innerHTML = '';
            
            try {
                // محاكاة بيانات البيع
                const mockSale = {
                    items: [
                        {
                            productId: 'test1',
                            name: 'منتج تجريبي',
                            quantity: 2,
                            price: 25.50,
                            total: 51.00
                        }
                    ],
                    customerId: 'guest',
                    paymentMethod: 'cash',
                    paidAmount: 51.00,
                    discount: 0,
                    tax: 0,
                    notes: 'بيع تجريبي'
                };
                
                showResult('salesResults', '✅ تم إنشاء بيانات البيع التجريبي', 'success');
                showResult('salesResults', `📄 عدد المنتجات: ${mockSale.items.length}`, 'success');
                showResult('salesResults', `💰 المجموع: ${mockSale.items[0].total} ج.م`, 'success');
                
                // اختبار حفظ البيع
                if (db && typeof db.addSale === 'function') {
                    const savedSale = db.addSale(mockSale);
                    if (savedSale) {
                        showResult('salesResults', '✅ تم حفظ البيع في قاعدة البيانات', 'success');
                        showResult('salesResults', `🆔 معرف البيع: ${savedSale.id}`, 'success');
                    } else {
                        showResult('salesResults', '❌ فشل في حفظ البيع', 'error');
                    }
                } else {
                    showResult('salesResults', '❌ قاعدة البيانات غير متوفرة', 'error');
                }
                
            } catch (error) {
                showResult('salesResults', `❌ خطأ في اختبار البيع: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار أولي
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testTableLayout();
            }, 1000);
        });
    </script>
</body>
</html>
