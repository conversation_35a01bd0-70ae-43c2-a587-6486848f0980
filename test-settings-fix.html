<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الإعدادات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح مشكلة الإعدادات</h1>
        <p>هذا الاختبار يتحقق من وجود النوافذ المطلوبة لزر ضبط المصنع</p>
        
        <div id="testResults"></div>
        
        <button onclick="runTests()">تشغيل الاختبارات</button>
        <button onclick="testSettingsLoad()">اختبار تحميل الإعدادات</button>
        <button onclick="clearResults()">مسح النتائج</button>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function runTests() {
            clearResults();
            addResult('بدء الاختبارات...', 'info');

            // اختبار وجود النوافذ المطلوبة
            const requiredModals = [
                'factoryResetModal',
                'progressModal',
                'confirmModal',
                'alertModal'
            ];

            let allModalsExist = true;
            requiredModals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    addResult(`✅ النافذة ${modalId} موجودة`, 'success');
                } else {
                    addResult(`❌ النافذة ${modalId} غير موجودة`, 'error');
                    allModalsExist = false;
                }
            });

            // اختبار وجود العناصر المطلوبة لضبط المصنع
            const requiredElements = [
                'createBackupBeforeReset',
                'resetSettings',
                'factoryResetConfirmation',
                'factoryResetButton'
            ];

            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    addResult(`✅ العنصر ${elementId} موجود`, 'success');
                } else {
                    addResult(`❌ العنصر ${elementId} غير موجود`, 'error');
                    allModalsExist = false;
                }
            });

            // اختبار وجود الوظائف المطلوبة
            const requiredFunctions = [
                'showFactoryResetModal',
                'validateFactoryResetConfirmation',
                'performFactoryReset'
            ];

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ الوظيفة ${funcName} موجودة`, 'success');
                } else {
                    addResult(`❌ الوظيفة ${funcName} غير موجودة`, 'error');
                    allModalsExist = false;
                }
            });

            if (allModalsExist) {
                addResult('🎉 جميع العناصر المطلوبة موجودة! يجب أن تعمل الإعدادات الآن.', 'success');
            } else {
                addResult('⚠️ بعض العناصر مفقودة. قد تحتاج لإضافتها.', 'error');
            }
        }

        function testSettingsLoad() {
            addResult('اختبار تحميل الإعدادات...', 'info');
            
            try {
                // محاكاة تحميل الإعدادات
                if (typeof loadSettings === 'function') {
                    addResult('✅ وظيفة loadSettings موجودة', 'success');
                    
                    // محاولة تشغيلها
                    loadSettings().then(() => {
                        addResult('✅ تم تحميل الإعدادات بنجاح', 'success');
                    }).catch(error => {
                        addResult(`❌ خطأ في تحميل الإعدادات: ${error.message}`, 'error');
                    });
                } else {
                    addResult('❌ وظيفة loadSettings غير موجودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار تحميل الإعدادات: ${error.message}`, 'error');
            }
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>

    <!-- تضمين الملفات المطلوبة للاختبار -->
    <script src="database.js"></script>
    <script src="main.js"></script>
    <script src="settings.js"></script>
    <script src="backup.js"></script>
</body>
</html>