/**
 * إدارة الموردين - تكنوفلاش
 */

let currentSupplier = null;
let suppliersData = [];

/**
 * تحميل صفحة الموردين
 */
async function loadSuppliers() {
    const mainContent = document.getElementById('mainContent');

    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-truck"></i> إدارة الموردين</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddSupplierModal()">
                    <i class="fas fa-plus"></i> إضافة مورد
                </button>
                <button class="btn btn-info" onclick="exportSuppliers()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="btn btn-warning" onclick="showImportModal()">
                    <i class="fas fa-upload"></i> استيراد
                </button>
            </div>
        </div>

        <!-- شريط البحث والفلاتر -->
        <div class="filters-section">
            <div class="card">
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <div class="search-bar">
                                <input type="text" class="search-input" id="supplierSearchInput"
                                       placeholder="البحث بالاسم أو الهاتف...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="balanceFilter">
                                <option value="">جميع الموردين</option>
                                <option value="with-balance">لديهم رصيد</option>
                                <option value="no-balance">بدون رصيد</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الموردين -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-value" id="totalSuppliersCount">٠</div>
                <div class="stat-label">إجمالي الموردين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-value" id="activeSuppliersCount">٠</div>
                <div class="stat-label">الموردين النشطون</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-value" id="totalOwedAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي المستحقات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-value" id="totalPurchasesThisMonth">٠</div>
                <div class="stat-label">مشتريات هذا الشهر</div>
            </div>
        </div>

        <!-- جدول الموردين -->
        <div class="card">
            <div class="card-body">
                <div class="table-container">
                    <table class="table" id="suppliersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الشركة</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم تحميل البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل مورد -->
        <div id="supplierModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="supplierModalTitle">إضافة مورد جديد</h3>
                    <button class="modal-close" onclick="app.hideModal('supplierModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="supplierForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اسم المورد *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="form-group">
                                <label>اسم الشركة</label>
                                <input type="text" class="form-control" name="company">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                            <div class="form-group">
                                <label>البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الموقع الإلكتروني</label>
                                <input type="url" class="form-control" name="website">
                            </div>
                            <div class="form-group">
                                <label>الرقم الضريبي</label>
                                <input type="text" class="form-control" name="taxNumber">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>العنوان</label>
                            <textarea class="form-control" name="address" rows="3"
                                      placeholder="العنوان الكامل..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الرصيد الحالي</label>
                                <input type="number" class="form-control" name="balance"
                                       step="0.01" value="0" readonly>
                            </div>
                            <div class="form-group">
                                <label>شروط الدفع (بالأيام)</label>
                                <input type="number" class="form-control" name="paymentTerms"
                                       min="0" value="30">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="isActive"
                                       id="isActiveCheck" checked>
                                <label class="form-check-label" for="isActiveCheck">
                                    مورد نشط
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('supplierModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة تعديل الرصيد -->
        <div id="balanceModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تعديل رصيد المورد</h3>
                    <button class="modal-close" onclick="app.hideModal('balanceModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="balanceForm">
                        <div class="customer-info">
                            <h4 id="balanceSupplierName"></h4>
                            <p>الرصيد الحالي: <span id="currentBalance"></span></p>
                        </div>

                        <div class="form-group">
                            <label>نوع العملية</label>
                            <select class="form-control" name="operationType" required>
                                <option value="add">إضافة مبلغ (مستحق)</option>
                                <option value="subtract">خصم مبلغ (دفع)</option>
                                <option value="set">تعيين رصيد جديد</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>المبلغ *</label>
                            <input type="number" class="form-control" name="amount"
                                   step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"
                                      placeholder="سبب التعديل..."></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> تطبيق
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('balanceModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة الاستيراد -->
        <div id="importModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>استيراد الموردين</h3>
                    <button class="modal-close" onclick="app.hideModal('importModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="import-section">
                        <p>يمكنك استيراد الموردين من ملف JSON:</p>
                        <input type="file" id="importFile" accept=".json" class="form-control">
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="importSuppliers()">
                                <i class="fas fa-upload"></i> استيراد
                            </button>
                            <button class="btn btn-info" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i> تحميل نموذج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeSuppliersPage();
}

/**
 * تهيئة صفحة الموردين
 */
async function initializeSuppliersPage() {
    // تحميل البيانات
    loadSuppliersData();

    // إعداد مستمعي الأحداث
    setupSuppliersEventListeners();

    // عرض الموردين والإحصائيات
    displaySuppliers();
    updateSuppliersStats();
}

/**
 * تحميل بيانات الموردين
 */
function loadSuppliersData() {
    suppliersData = db.getSuppliers();
}

/**
 * إعداد مستمعي الأحداث
 */
function setupSuppliersEventListeners() {
    // البحث
    document.getElementById('supplierSearchInput').addEventListener('input', filterSuppliers);

    // الفلاتر
    document.getElementById('statusFilter').addEventListener('change', filterSuppliers);
    document.getElementById('balanceFilter').addEventListener('change', filterSuppliers);

    // نماذج
    document.getElementById('supplierForm').addEventListener('submit', handleSupplierSubmit);
    document.getElementById('balanceForm').addEventListener('submit', handleBalanceSubmit);
}

/**
 * عرض الموردين
 */
function displaySuppliers(suppliers = suppliersData) {
    const tbody = document.querySelector('#suppliersTable tbody');

    if (suppliers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">لا توجد موردين</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = suppliers.map(supplier => `
        <tr>
            <td>
                <strong>${supplier.name}</strong>
                ${supplier.notes ? `<br><small>${supplier.notes}</small>` : ''}
            </td>
            <td>${supplier.company || '-'}</td>
            <td>${supplier.phone || '-'}</td>
            <td>${supplier.email || '-'}</td>
            <td>
                <span class="balance-amount ${supplier.balance > 0 ? 'debt' : 'clear'}">
                    ${db.formatCurrency(supplier.balance)}
                </span>
            </td>
            <td>${db.formatDate(supplier.createdAt)}</td>
            <td>
                <span class="badge badge-${supplier.isActive ? 'success' : 'secondary'}">
                    ${supplier.isActive ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="editSupplier('${supplier.id}')"
                            title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adjustBalance('${supplier.id}')"
                            title="تعديل الرصيد">
                        <i class="fas fa-money-bill-wave"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="viewSupplierHistory('${supplier.id}')"
                            title="السجل">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSupplier('${supplier.id}')"
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * تحديث إحصائيات الموردين
 */
function updateSuppliersStats() {
    const totalSuppliers = suppliersData.length;
    const activeSuppliers = suppliersData.filter(s => s.isActive).length;
    const totalOwed = suppliersData.reduce((sum, s) => sum + (s.balance > 0 ? s.balance : 0), 0);

    // المشتريات هذا الشهر
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const purchases = db.getPurchases().filter(p =>
        new Date(p.date) >= thisMonth
    );
    const totalPurchasesThisMonth = purchases.length;

    document.getElementById('totalSuppliersCount').textContent = db.toArabicNumerals(totalSuppliers);
    document.getElementById('activeSuppliersCount').textContent = db.toArabicNumerals(activeSuppliers);
    document.getElementById('totalOwedAmount').textContent = db.formatCurrency(totalOwed);
    document.getElementById('totalPurchasesThisMonth').textContent = db.toArabicNumerals(totalPurchasesThisMonth);
}

/**
 * فلترة الموردين
 */
function filterSuppliers() {
    const searchTerm = document.getElementById('supplierSearchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const balanceFilter = document.getElementById('balanceFilter').value;

    let filteredSuppliers = suppliersData;

    // فلترة بالبحث
    if (searchTerm) {
        filteredSuppliers = filteredSuppliers.filter(supplier =>
            supplier.name.toLowerCase().includes(searchTerm) ||
            (supplier.company && supplier.company.toLowerCase().includes(searchTerm)) ||
            (supplier.phone && supplier.phone.includes(searchTerm)) ||
            (supplier.email && supplier.email.toLowerCase().includes(searchTerm))
        );
    }

    // فلترة بالحالة
    if (statusFilter) {
        filteredSuppliers = filteredSuppliers.filter(supplier => {
            switch (statusFilter) {
                case 'active':
                    return supplier.isActive;
                case 'inactive':
                    return !supplier.isActive;
                default:
                    return true;
            }
        });
    }

    // فلترة بالرصيد
    if (balanceFilter) {
        filteredSuppliers = filteredSuppliers.filter(supplier => {
            switch (balanceFilter) {
                case 'with-balance':
                    return supplier.balance > 0;
                case 'no-balance':
                    return supplier.balance <= 0;
                default:
                    return true;
            }
        });
    }

    displaySuppliers(filteredSuppliers);
}

/**
 * إظهار نافذة إضافة مورد
 */
function showAddSupplierModal() {
    currentSupplier = null;
    document.getElementById('supplierModalTitle').textContent = 'إضافة مورد جديد';
    document.getElementById('supplierForm').reset();
    document.getElementById('isActiveCheck').checked = true;
    app.showModal('supplierModal');
}

/**
 * تعديل مورد
 */
function editSupplier(supplierId) {
    currentSupplier = db.getSupplier(supplierId);
    if (!currentSupplier) {
        app.showAlert('خطأ', 'المورد غير موجود');
        return;
    }

    document.getElementById('supplierModalTitle').textContent = 'تعديل المورد';

    // ملء النموذج
    const form = document.getElementById('supplierForm');
    form.name.value = currentSupplier.name;
    form.company.value = currentSupplier.company || '';
    form.phone.value = currentSupplier.phone || '';
    form.email.value = currentSupplier.email || '';
    form.website.value = currentSupplier.website || '';
    form.taxNumber.value = currentSupplier.taxNumber || '';
    form.address.value = currentSupplier.address || '';
    form.balance.value = currentSupplier.balance || 0;
    form.paymentTerms.value = currentSupplier.paymentTerms || 30;
    form.notes.value = currentSupplier.notes || '';
    form.isActive.checked = currentSupplier.isActive !== false;

    app.showModal('supplierModal');
}

/**
 * معالجة إرسال نموذج المورد
 */
async function handleSupplierSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const supplierData = {
        name: formData.get('name').trim(),
        company: formData.get('company').trim(),
        phone: formData.get('phone').trim(),
        email: formData.get('email').trim(),
        website: formData.get('website').trim(),
        taxNumber: formData.get('taxNumber').trim(),
        address: formData.get('address').trim(),
        paymentTerms: parseInt(formData.get('paymentTerms')) || 30,
        notes: formData.get('notes').trim(),
        isActive: formData.has('isActive')
    };

    // التحقق من صحة البيانات
    const validation = Validator.validateForm(supplierData, {
        name: ['required', 'minLength:2'],
        phone: supplierData.phone ? ['phone'] : [],
        email: supplierData.email ? ['email'] : [],
        website: supplierData.website ? ['url'] : []
    });

    if (!validation.isValid) {
        Validator.displayFormErrors(validation.errors);
        return;
    }

    app.showLoading();

    try {
        if (currentSupplier) {
            // تحديث مورد موجود (مع الحفاظ على الرصيد)
            supplierData.balance = currentSupplier.balance;
            db.updateSupplier(currentSupplier.id, supplierData);
            app.showNotification('تم تحديث المورد بنجاح', 'success');
        } else {
            // إضافة مورد جديد
            supplierData.balance = 0;
            db.addSupplier(supplierData);
            app.showNotification('تم إضافة المورد بنجاح', 'success');
        }

        // تحديث العرض
        loadSuppliersData();
        displaySuppliers();
        updateSuppliersStats();

        // إغلاق النافذة
        app.hideModal('supplierModal');

    } catch (error) {
        console.error('خطأ في حفظ المورد:', error);
        app.showAlert('خطأ', 'حدث خطأ في حفظ المورد');
    }

    app.hideLoading();
}

/**
 * تعديل رصيد المورد
 */
function adjustBalance(supplierId) {
    const supplier = db.getSupplier(supplierId);
    if (!supplier) {
        app.showAlert('خطأ', 'المورد غير موجود');
        return;
    }

    currentSupplier = supplier;
    document.getElementById('balanceSupplierName').textContent = supplier.name;
    document.getElementById('currentBalance').textContent = db.formatCurrency(supplier.balance);
    document.getElementById('balanceForm').reset();

    app.showModal('balanceModal');
}

/**
 * معالجة تعديل الرصيد
 */
async function handleBalanceSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const operationType = formData.get('operationType');
    const amount = parseFloat(formData.get('amount'));
    const notes = formData.get('notes').trim();

    if (!amount || amount <= 0) {
        app.showAlert('خطأ', 'يرجى إدخال مبلغ صحيح');
        return;
    }

    let newBalance = currentSupplier.balance;

    switch (operationType) {
        case 'add':
            newBalance += amount;
            break;
        case 'subtract':
            newBalance -= amount;
            break;
        case 'set':
            newBalance = amount;
            break;
    }

    app.showLoading();

    try {
        // تحديث الرصيد
        db.updateSupplier(currentSupplier.id, { balance: newBalance });

        // إضافة سجل للعملية
        db.addSupplierBalanceTransaction(
            currentSupplier.id,
            amount,
            operationType,
            notes || 'تعديل رصيد المورد'
        );

        app.showNotification('تم تحديث الرصيد بنجاح', 'success');

        // تحديث العرض
        loadSuppliersData();
        displaySuppliers();
        updateSuppliersStats();

        // إغلاق النافذة
        app.hideModal('balanceModal');

    } catch (error) {
        console.error('خطأ في تحديث الرصيد:', error);
        app.showAlert('خطأ', 'حدث خطأ في تحديث الرصيد');
    }

    app.hideLoading();
}

/**
 * عرض سجل المورد
 */
function viewSupplierHistory(supplierId) {
    const supplier = db.getSupplier(supplierId);
    if (!supplier) {
        app.showAlert('خطأ', 'المورد غير موجود');
        return;
    }

    // سيتم تنفيذ هذه الوظيفة لاحقاً
    app.showAlert('قريباً', 'هذه الميزة ستكون متاحة قريباً');
}

/**
 * حذف مورد
 */
function deleteSupplier(supplierId) {
    const supplier = db.getSupplier(supplierId);
    if (!supplier) {
        app.showAlert('خطأ', 'المورد غير موجود');
        return;
    }

    // التحقق من وجود مشتريات للمورد
    const purchases = db.getPurchases().filter(purchase => purchase.supplierId === supplierId);
    if (purchases.length > 0) {
        app.showAlert('تحذير', 'لا يمكن حذف المورد لأن له مشتريات مسجلة');
        return;
    }

    app.showConfirm(
        'حذف المورد',
        `هل أنت متأكد من حذف المورد "${supplier.name}"؟`,
        () => {
            db.deleteSupplier(supplierId);
            app.showNotification('تم حذف المورد بنجاح', 'success');

            loadSuppliersData();
            displaySuppliers();
            updateSuppliersStats();
        }
    );
}

/**
 * تصدير الموردين
 */
function exportSuppliers() {
    const suppliers = db.getSuppliers();
    const exportData = suppliers.map(supplier => ({
        name: supplier.name,
        company: supplier.company,
        phone: supplier.phone,
        email: supplier.email,
        website: supplier.website,
        taxNumber: supplier.taxNumber,
        address: supplier.address,
        balance: supplier.balance,
        paymentTerms: supplier.paymentTerms,
        notes: supplier.notes,
        isActive: supplier.isActive
    }));

    Utils.downloadJSON(exportData, `suppliers_${new Date().toISOString().split('T')[0]}.json`);
    app.showNotification('تم تصدير الموردين بنجاح', 'success');
}

/**
 * إظهار نافذة الاستيراد
 */
function showImportModal() {
    app.showModal('importModal');
}

/**
 * استيراد الموردين
 */
async function importSuppliers() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        app.showAlert('خطأ', 'يرجى اختيار ملف');
        return;
    }

    app.showLoading();

    try {
        const data = await Utils.readJSONFile(file);

        if (!Array.isArray(data)) {
            throw new Error('تنسيق الملف غير صحيح');
        }

        let importedCount = 0;
        let errorCount = 0;

        for (const supplierData of data) {
            try {
                // التحقق من البيانات الأساسية
                if (!supplierData.name) {
                    errorCount++;
                    continue;
                }

                db.addSupplier(supplierData);
                importedCount++;

            } catch (error) {
                errorCount++;
            }
        }

        app.showNotification(
            `تم استيراد ${importedCount} مورد بنجاح${errorCount > 0 ? ` (${errorCount} خطأ)` : ''}`,
            'success'
        );

        // تحديث العرض
        loadSuppliersData();
        displaySuppliers();
        updateSuppliersStats();

        // إغلاق النافذة
        app.hideModal('importModal');

    } catch (error) {
        console.error('خطأ في الاستيراد:', error);
        app.showAlert('خطأ', 'حدث خطأ في استيراد الملف');
    }

    app.hideLoading();
}

/**
 * تحميل نموذج الاستيراد
 */
function downloadTemplate() {
    const template = [
        {
            name: "مورد تجريبي",
            company: "شركة تجريبية",
            phone: "0501234567",
            email: "<EMAIL>",
            website: "https://example.com",
            taxNumber: "*********",
            address: "الرياض، المملكة العربية السعودية",
            balance: 0,
            paymentTerms: 30,
            notes: "ملاحظات المورد",
            isActive: true
        }
    ];

    Utils.downloadJSON(template, 'suppliers_template.json');
    app.showNotification('تم تحميل النموذج بنجاح', 'info');
}