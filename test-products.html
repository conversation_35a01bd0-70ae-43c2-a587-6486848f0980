<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>اختبار وظائف المنتجات</h1>
    
    <div class="test-section">
        <h3>اختبار قاعدة البيانات</h3>
        <button onclick="testDatabase()">اختبار قاعدة البيانات</button>
        <div id="dbResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>اختبار إضافة منتج</h3>
        <button onclick="testAddProduct()">إضافة منتج تجريبي</button>
        <div id="addResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>اختبار عرض المنتجات</h3>
        <button onclick="testGetProducts()">عرض جميع المنتجات</button>
        <div id="getResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>اختبار localStorage</h3>
        <button onclick="testLocalStorage()">اختبار التخزين المحلي</button>
        <div id="storageResult" class="result"></div>
    </div>

    <script src="database.js"></script>
    <script>
        function testDatabase() {
            const result = document.getElementById('dbResult');
            try {
                const data = db.getData();
                if (data) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>نجح الاتصال بقاعدة البيانات!</strong><br>
                        عدد المنتجات: ${data.products ? data.products.length : 0}<br>
                        عدد العملاء: ${data.customers ? data.customers.length : 0}<br>
                        الإصدار: ${data.version || 'غير محدد'}
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = 'فشل في الاتصال بقاعدة البيانات';
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function testAddProduct() {
            const result = document.getElementById('addResult');
            try {
                const testProduct = {
                    name: 'منتج تجريبي ' + Date.now(),
                    barcode: 'TEST' + Date.now(),
                    category: 'electronics',
                    unit: 'piece',
                    costPrice: 50,
                    price: 100,
                    quantity: 10,
                    minStock: 5,
                    description: 'منتج للاختبار'
                };

                console.log('إضافة منتج تجريبي:', testProduct);
                const addedProduct = db.addProduct(testProduct);
                
                if (addedProduct) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>تم إضافة المنتج بنجاح!</strong><br>
                        المعرف: ${addedProduct.id}<br>
                        الاسم: ${addedProduct.name}<br>
                        السعر: ${addedProduct.price}
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = 'فشل في إضافة المنتج';
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
                console.error('خطأ في إضافة المنتج:', error);
            }
        }

        function testGetProducts() {
            const result = document.getElementById('getResult');
            try {
                const products = db.getProducts();
                result.className = 'result success';
                result.innerHTML = `
                    <strong>المنتجات المحفوظة (${products.length}):</strong><br>
                    ${products.map(p => `- ${p.name} (${p.price} ريال)`).join('<br>')}
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ: ${error.message}`;
            }
        }

        function testLocalStorage() {
            const result = document.getElementById('storageResult');
            try {
                // اختبار الكتابة
                const testData = { test: 'بيانات تجريبية', timestamp: Date.now() };
                localStorage.setItem('test_key', JSON.stringify(testData));
                
                // اختبار القراءة
                const retrievedData = JSON.parse(localStorage.getItem('test_key'));
                
                if (retrievedData && retrievedData.test === testData.test) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>localStorage يعمل بشكل صحيح!</strong><br>
                        البيانات المحفوظة: ${JSON.stringify(retrievedData, null, 2)}
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = 'مشكلة في localStorage';
                }
                
                // تنظيف
                localStorage.removeItem('test_key');
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `خطأ في localStorage: ${error.message}`;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('تحميل صفحة الاختبار');
            testDatabase();
        });
    </script>
</body>
</html>
