/**
 * إصلاح تنسيقات CSS لصفحة المبيعات
 * يتم تشغيل هذا الملف عند تحميل صفحة المبيعات لضمان التنسيق الصحيح
 */

function fixSalesCSS() {
    console.log('🎨 تطبيق إصلاحات CSS للمبيعات...');
    
    // إنشاء أنماط إضافية للإصلاح
    const fixCSS = `
        <style id="sales-css-fix">
            /* إصلاحات عاجلة للمبيعات */
            .sales-container {
                width: 100% !important;
                max-width: 100% !important;
                overflow-x: hidden !important;
                padding: 0 15px !important;
            }
            
            .sales-container .row {
                display: flex !important;
                flex-wrap: wrap !important;
                margin-right: -15px !important;
                margin-left: -15px !important;
            }
            
            .sales-container .col-md-8 {
                flex: 0 0 66.666667% !important;
                max-width: 66.666667% !important;
                padding-right: 15px !important;
                padding-left: 15px !important;
            }
            
            .sales-container .col-md-4 {
                flex: 0 0 33.333333% !important;
                max-width: 33.333333% !important;
                padding-right: 15px !important;
                padding-left: 15px !important;
            }
            
            @media (max-width: 767px) {
                .sales-container .col-md-8,
                .sales-container .col-md-4 {
                    flex: 0 0 100% !important;
                    max-width: 100% !important;
                }
            }
            
            /* إصلاح البطاقات */
            .sales-container .card {
                background-color: #ffffff !important;
                border: 1px solid rgba(0,0,0,.125) !important;
                border-radius: 12px !important;
                box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff !important;
                margin-bottom: 1.5rem !important;
            }
            
            .sales-container .card-header {
                background-color: #f0f2f5 !important;
                border-bottom: 1px solid rgba(0,0,0,.125) !important;
                padding: 1.5rem !important;
            }
            
            .sales-container .card-body {
                padding: 1.5rem !important;
            }
            
            /* تحسين الجداول - مبسط ومحسن */
            .sales-container {
                position: relative !important;
            }

            .sale-items {
                background: #ffffff !important;
                border-radius: 8px !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                overflow: hidden !important;
                margin-bottom: 1rem !important;
            }

            /* التأكد من أن الجدول يأخذ العرض الكامل */
            .sales-container table,
            .sales-container .table,
            #saleItemsTable {
                width: 100% !important;
                border-collapse: collapse !important;
                margin: 0 !important;
                background: #ffffff !important;
            }

            /* تحسين الخلايا */
            .sales-container table th,
            .sales-container table td {
                padding: 12px 8px !important;
                text-align: right !important;
                vertical-align: middle !important;
                border-bottom: 1px solid #dee2e6 !important;
            }

            /* تحسين رأس الجدول */
            .sales-container table thead th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
                text-align: center !important;
                border-bottom: none !important;
            }
            
            /* إصلاح الأزرار */
            .sales-container .btn {
                display: inline-block !important;
                font-weight: 400 !important;
                text-align: center !important;
                vertical-align: middle !important;
                border: 1px solid transparent !important;
                padding: 0.375rem 0.75rem !important;
                font-size: 1rem !important;
                line-height: 1.5 !important;
                border-radius: 8px !important;
                transition: all 0.15s ease-in-out !important;
                cursor: pointer !important;
                text-decoration: none !important;
            }
            
            .sales-container .btn-primary {
                color: #fff !important;
                background-color: #667eea !important;
                border-color: #667eea !important;
            }
            
            .sales-container .btn-success {
                color: #fff !important;
                background-color: #48bb78 !important;
                border-color: #48bb78 !important;
            }
            
            .sales-container .btn-warning {
                color: #212529 !important;
                background-color: #ed8936 !important;
                border-color: #ed8936 !important;
            }
            
            .sales-container .btn-info {
                color: #fff !important;
                background-color: #4299e1 !important;
                border-color: #4299e1 !important;
            }
            
            .sales-container .btn:disabled {
                opacity: 0.65 !important;
                cursor: not-allowed !important;
            }
            
            /* إصلاح النماذج */
            .sales-container .form-control,
            .sales-container .search-input {
                display: block !important;
                width: 100% !important;
                padding: 0.375rem 0.75rem !important;
                font-size: 1rem !important;
                line-height: 1.5 !important;
                color: #2d3748 !important;
                background-color: #ffffff !important;
                border: 1px solid #ced4da !important;
                border-radius: 8px !important;
                transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out !important;
            }
            
            .sales-container .form-control:focus,
            .sales-container .search-input:focus {
                border-color: #667eea !important;
                outline: 0 !important;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            }
            
            /* إصلاح البحث */
            .search-bar {
                position: relative !important;
                display: flex !important;
                align-items: center !important;
            }
            
            .search-icon {
                position: absolute !important;
                left: 10px !important;
                color: #a0aec0 !important;
                pointer-events: none !important;
                z-index: 2 !important;
            }
            
            .search-input {
                padding-left: 35px !important;
            }
            
            .search-results {
                position: absolute !important;
                top: 100% !important;
                right: 0 !important;
                left: 0 !important;
                background: #ffffff !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 12px !important;
                box-shadow: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff !important;
                max-height: 300px !important;
                overflow-y: auto !important;
                z-index: 1000 !important;
                display: none !important;
            }
            
            .search-result-item {
                padding: 1rem !important;
                border-bottom: 1px solid #a0aec0 !important;
                cursor: pointer !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                transition: background-color 0.15s ease-in-out !important;
            }
            
            .search-result-item:hover {
                background: #f0f2f5 !important;
            }
            
            .search-result-item:last-child {
                border-bottom: none !important;
            }
            
            /* إصلاح ملخص البيع */
            .sale-summary {
                background: #f0f2f5 !important;
                border-radius: 12px !important;
                padding: 1.5rem !important;
                margin-top: 1.5rem !important;
            }
            
            .summary-row {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                padding: 0.5rem 0 !important;
                border-bottom: 1px solid #a0aec0 !important;
            }
            
            .summary-row:last-child {
                border-bottom: none !important;
            }
            
            .summary-row.total {
                font-weight: 600 !important;
                font-size: 1.125rem !important;
                color: #667eea !important;
            }
            
            /* إصلاح التحكم في الكمية */
            .quantity-controls {
                display: flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }
            
            .quantity-value {
                min-width: 30px !important;
                text-align: center !important;
                font-weight: 600 !important;
            }
            
            /* إصلاح رسائل عدم وجود عناصر */
            .no-items td {
                text-align: center !important;
                color: #a0aec0 !important;
                font-style: italic !important;
                padding: 2rem !important;
            }
        </style>
    `;
    
    // إزالة الأنماط القديمة إن وجدت
    const oldFix = document.getElementById('sales-css-fix');
    if (oldFix) {
        oldFix.remove();
    }
    
    // إضافة الأنماط الجديدة
    document.head.insertAdjacentHTML('beforeend', fixCSS);

    // انتظار قصير ثم إصلاح الجداول بشكل مخصص
    setTimeout(() => {
        fixTablesSpecifically();
    }, 100);

    console.log('✅ تم تطبيق إصلاحات CSS للمبيعات');
}

// تشغيل الإصلاح عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixSalesCSS);
} else {
    fixSalesCSS();
}

// تشغيل الإصلاح عند تحميل صفحة المبيعات
window.addEventListener('load', function() {
    // انتظار قصير للتأكد من تحميل جميع العناصر
    setTimeout(fixSalesCSS, 500);
});

// إضافة مراقب للتغييرات في DOM لإصلاح المشاكل تلقائياً
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        let needsFix = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // التحقق من إضافة عناصر جديدة تحتاج إصلاح
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && (
                            node.classList.contains('sales-container') ||
                            node.classList.contains('table') ||
                            node.id === 'saleItemsTable'
                        )) {
                            needsFix = true;
                        }
                    }
                });
            }
        });

        if (needsFix) {
            console.log('🔧 تم اكتشاف تغييرات في DOM، تطبيق الإصلاحات المحسنة...');
            optimizedFixSalesCSS();
        }
    });

    // بدء مراقبة التغييرات
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// إضافة وظيفة للتحقق من التنسيق وإصلاحه عند الحاجة
function checkAndFixSalesLayout() {
    const salesContainer = document.querySelector('.sales-container');
    if (salesContainer) {
        console.log('🔍 فحص تخطيط المبيعات...');
        
        // التحقق من وجود المشاكل الشائعة
        const cards = salesContainer.querySelectorAll('.card');
        const tables = salesContainer.querySelectorAll('table');
        const buttons = salesContainer.querySelectorAll('.btn');
        
        let needsFix = false;
        
        // فحص البطاقات
        cards.forEach(card => {
            const computedStyle = window.getComputedStyle(card);
            if (computedStyle.backgroundColor === 'rgba(0, 0, 0, 0)' || 
                computedStyle.backgroundColor === 'transparent') {
                needsFix = true;
            }
        });
        
        // فحص الجداول
        tables.forEach(table => {
            const computedStyle = window.getComputedStyle(table);
            const parentWidth = table.parentElement.offsetWidth;
            const tableWidth = table.offsetWidth;

            // التحقق من أن الجدول يأخذ العرض الكامل
            if (computedStyle.width !== '100%' || tableWidth < parentWidth * 0.95) {
                needsFix = true;
                console.log('🔧 جدول يحتاج إصلاح:', {
                    computedWidth: computedStyle.width,
                    actualWidth: tableWidth,
                    parentWidth: parentWidth,
                    percentage: (tableWidth / parentWidth * 100).toFixed(2) + '%'
                });
            }
        });
        
        if (needsFix) {
            console.log('⚠️ تم اكتشاف مشاكل في التنسيق، تطبيق الإصلاحات...');
            fixSalesCSS();
        } else {
            console.log('✅ التنسيق يعمل بشكل صحيح');
        }
    }
}

/**
 * إصلاح مخصص للجداول - محسن
 */
function fixTablesSpecifically() {
    console.log('🔧 إصلاح مخصص للجداول...');

    const tables = document.querySelectorAll('.sales-container table, #saleItemsTable');

    tables.forEach((table, index) => {
        console.log(`🔍 إصلاح الجدول ${index + 1}:`, table);

        // التأكد من الأنماط الأساسية
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';
        table.style.tableLayout = 'fixed';
        table.style.background = '#ffffff';
        table.style.borderRadius = '8px';
        table.style.overflow = 'hidden';
        table.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';

        // إصلاح الحاوي
        const container = table.closest('.sale-items');
        if (container) {
            container.style.position = 'relative';
            container.style.overflowX = 'auto';
            container.style.overflowY = 'visible';
            container.style.borderRadius = '8px';
            container.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        }

        // إصلاح رأس الجدول
        const headerCells = table.querySelectorAll('thead th');
        headerCells.forEach(th => {
            th.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            th.style.color = '#ffffff';
            th.style.fontWeight = '600';
            th.style.textAlign = 'center';
            th.style.padding = '12px 8px';
            th.style.borderBottom = 'none';
        });

        // إصلاح خلايا الجسم
        const bodyCells = table.querySelectorAll('tbody td');
        bodyCells.forEach(td => {
            td.style.padding = '12px 8px';
            td.style.textAlign = 'right';
            td.style.verticalAlign = 'middle';
            td.style.borderBottom = '1px solid #dee2e6';
            td.style.wordWrap = 'break-word';
        });

        // إضافة تأثير hover للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                this.style.transition = 'all 0.2s ease';
            });

            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });

        console.log(`✅ تم إصلاح الجدول ${index + 1}`);
    });

    console.log('✅ تم إصلاح جميع الجداول');
}

/**
 * إزالة الأنماط المتعارضة
 */
function removeConflictingStyles() {
    console.log('🧹 إزالة الأنماط المتعارضة...');

    // إزالة الأنماط القديمة المتعارضة
    const conflictingSelectors = [
        'table.table',
        '.table-responsive',
        '.table-container'
    ];

    const styleSheets = document.styleSheets;
    for (let i = 0; i < styleSheets.length; i++) {
        try {
            const rules = styleSheets[i].cssRules || styleSheets[i].rules;
            for (let j = rules.length - 1; j >= 0; j--) {
                const rule = rules[j];
                if (rule.selectorText) {
                    conflictingSelectors.forEach(selector => {
                        if (rule.selectorText.includes(selector) &&
                            !rule.selectorText.includes('sales-container') &&
                            !rule.selectorText.includes('#saleItemsTable')) {
                            // لا نحذف القواعد، فقط نتجاهلها
                            console.log('تم تجاهل قاعدة متعارضة:', rule.selectorText);
                        }
                    });
                }
            }
        } catch (e) {
            // تجاهل الأخطاء في الوصول للأنماط الخارجية
        }
    }

    console.log('✅ تم تنظيف الأنماط المتعارضة');
}

/**
 * تحسين الأداء وتجنب إعادة التطبيق المتكررة
 */
let isFixing = false;
let fixTimeout = null;

function optimizedFixSalesCSS() {
    if (isFixing) {
        console.log('⏳ الإصلاح قيد التنفيذ، تم تجاهل الطلب');
        return;
    }

    if (fixTimeout) {
        clearTimeout(fixTimeout);
    }

    fixTimeout = setTimeout(() => {
        isFixing = true;
        console.log('🚀 بدء الإصلاح المحسن...');

        removeConflictingStyles();
        fixSalesCSS();

        setTimeout(() => {
            isFixing = false;
            console.log('✅ انتهى الإصلاح المحسن');
        }, 100);
    }, 50);
}

// تصدير الوظائف للاستخدام الخارجي
if (typeof window !== 'undefined') {
    window.fixSalesCSS = fixSalesCSS;
    window.optimizedFixSalesCSS = optimizedFixSalesCSS;
    window.checkAndFixSalesLayout = checkAndFixSalesLayout;
    window.fixTablesSpecifically = fixTablesSpecifically;
    window.removeConflictingStyles = removeConflictingStyles;
}
