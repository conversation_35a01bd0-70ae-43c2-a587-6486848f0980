/**
 * إصلاح تنسيقات CSS لصفحة المبيعات
 * يتم تشغيل هذا الملف عند تحميل صفحة المبيعات لضمان التنسيق الصحيح
 */

function fixSalesCSS() {
    console.log('🎨 تطبيق إصلاحات CSS للمبيعات...');
    
    // إنشاء أنماط إضافية للإصلاح
    const fixCSS = `
        <style id="sales-css-fix">
            /* إصلاحات عاجلة للمبيعات */
            .sales-container {
                width: 100% !important;
                max-width: 100% !important;
                overflow-x: hidden !important;
                padding: 0 15px !important;
            }
            
            .sales-container .row {
                display: flex !important;
                flex-wrap: wrap !important;
                margin-right: -15px !important;
                margin-left: -15px !important;
            }
            
            .sales-container .col-md-8 {
                flex: 0 0 66.666667% !important;
                max-width: 66.666667% !important;
                padding-right: 15px !important;
                padding-left: 15px !important;
            }
            
            .sales-container .col-md-4 {
                flex: 0 0 33.333333% !important;
                max-width: 33.333333% !important;
                padding-right: 15px !important;
                padding-left: 15px !important;
            }
            
            @media (max-width: 767px) {
                .sales-container .col-md-8,
                .sales-container .col-md-4 {
                    flex: 0 0 100% !important;
                    max-width: 100% !important;
                }
            }
            
            /* إصلاح البطاقات */
            .sales-container .card {
                background-color: #ffffff !important;
                border: 1px solid rgba(0,0,0,.125) !important;
                border-radius: 12px !important;
                box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff !important;
                margin-bottom: 1.5rem !important;
            }
            
            .sales-container .card-header {
                background-color: #f0f2f5 !important;
                border-bottom: 1px solid rgba(0,0,0,.125) !important;
                padding: 1.5rem !important;
            }
            
            .sales-container .card-body {
                padding: 1.5rem !important;
            }
            
            /* إصلاح الجداول */
            .sales-container table,
            .sales-container .table,
            #saleItemsTable {
                width: 100% !important;
                max-width: 100% !important;
                margin-bottom: 1rem !important;
                background-color: transparent !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
            }
            
            .sales-container table th,
            .sales-container table td,
            .sales-container .table th,
            .sales-container .table td,
            #saleItemsTable th,
            #saleItemsTable td {
                padding: 0.75rem !important;
                vertical-align: top !important;
                border-top: 1px solid #dee2e6 !important;
                text-align: right !important;
                word-wrap: break-word !important;
            }

            .sales-container table thead th,
            .sales-container .table thead th,
            #saleItemsTable thead th {
                vertical-align: bottom !important;
                border-bottom: 2px solid #dee2e6 !important;
                background-color: #f0f2f5 !important;
                font-weight: 600 !important;
                white-space: nowrap !important;
            }

            /* إصلاح عرض أعمدة الجدول */
            .sales-container table tbody tr,
            .sales-container .table tbody tr,
            #saleItemsTable tbody tr {
                width: 100% !important;
            }

            /* إصلاح حاوي الجدول */
            .sale-items,
            .table-responsive {
                width: 100% !important;
                overflow-x: auto !important;
            }
            
            /* إصلاح الأزرار */
            .sales-container .btn {
                display: inline-block !important;
                font-weight: 400 !important;
                text-align: center !important;
                vertical-align: middle !important;
                border: 1px solid transparent !important;
                padding: 0.375rem 0.75rem !important;
                font-size: 1rem !important;
                line-height: 1.5 !important;
                border-radius: 8px !important;
                transition: all 0.15s ease-in-out !important;
                cursor: pointer !important;
                text-decoration: none !important;
            }
            
            .sales-container .btn-primary {
                color: #fff !important;
                background-color: #667eea !important;
                border-color: #667eea !important;
            }
            
            .sales-container .btn-success {
                color: #fff !important;
                background-color: #48bb78 !important;
                border-color: #48bb78 !important;
            }
            
            .sales-container .btn-warning {
                color: #212529 !important;
                background-color: #ed8936 !important;
                border-color: #ed8936 !important;
            }
            
            .sales-container .btn-info {
                color: #fff !important;
                background-color: #4299e1 !important;
                border-color: #4299e1 !important;
            }
            
            .sales-container .btn:disabled {
                opacity: 0.65 !important;
                cursor: not-allowed !important;
            }
            
            /* إصلاح النماذج */
            .sales-container .form-control,
            .sales-container .search-input {
                display: block !important;
                width: 100% !important;
                padding: 0.375rem 0.75rem !important;
                font-size: 1rem !important;
                line-height: 1.5 !important;
                color: #2d3748 !important;
                background-color: #ffffff !important;
                border: 1px solid #ced4da !important;
                border-radius: 8px !important;
                transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out !important;
            }
            
            .sales-container .form-control:focus,
            .sales-container .search-input:focus {
                border-color: #667eea !important;
                outline: 0 !important;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            }
            
            /* إصلاح البحث */
            .search-bar {
                position: relative !important;
                display: flex !important;
                align-items: center !important;
            }
            
            .search-icon {
                position: absolute !important;
                left: 10px !important;
                color: #a0aec0 !important;
                pointer-events: none !important;
                z-index: 2 !important;
            }
            
            .search-input {
                padding-left: 35px !important;
            }
            
            .search-results {
                position: absolute !important;
                top: 100% !important;
                right: 0 !important;
                left: 0 !important;
                background: #ffffff !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 12px !important;
                box-shadow: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff !important;
                max-height: 300px !important;
                overflow-y: auto !important;
                z-index: 1000 !important;
                display: none !important;
            }
            
            .search-result-item {
                padding: 1rem !important;
                border-bottom: 1px solid #a0aec0 !important;
                cursor: pointer !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                transition: background-color 0.15s ease-in-out !important;
            }
            
            .search-result-item:hover {
                background: #f0f2f5 !important;
            }
            
            .search-result-item:last-child {
                border-bottom: none !important;
            }
            
            /* إصلاح ملخص البيع */
            .sale-summary {
                background: #f0f2f5 !important;
                border-radius: 12px !important;
                padding: 1.5rem !important;
                margin-top: 1.5rem !important;
            }
            
            .summary-row {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                padding: 0.5rem 0 !important;
                border-bottom: 1px solid #a0aec0 !important;
            }
            
            .summary-row:last-child {
                border-bottom: none !important;
            }
            
            .summary-row.total {
                font-weight: 600 !important;
                font-size: 1.125rem !important;
                color: #667eea !important;
            }
            
            /* إصلاح التحكم في الكمية */
            .quantity-controls {
                display: flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }
            
            .quantity-value {
                min-width: 30px !important;
                text-align: center !important;
                font-weight: 600 !important;
            }
            
            /* إصلاح رسائل عدم وجود عناصر */
            .no-items td {
                text-align: center !important;
                color: #a0aec0 !important;
                font-style: italic !important;
                padding: 2rem !important;
            }
        </style>
    `;
    
    // إزالة الأنماط القديمة إن وجدت
    const oldFix = document.getElementById('sales-css-fix');
    if (oldFix) {
        oldFix.remove();
    }
    
    // إضافة الأنماط الجديدة
    document.head.insertAdjacentHTML('beforeend', fixCSS);

    // انتظار قصير ثم إصلاح الجداول بشكل مخصص
    setTimeout(() => {
        fixTablesSpecifically();
    }, 100);

    console.log('✅ تم تطبيق إصلاحات CSS للمبيعات');
}

// تشغيل الإصلاح عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixSalesCSS);
} else {
    fixSalesCSS();
}

// تشغيل الإصلاح عند تحميل صفحة المبيعات
window.addEventListener('load', function() {
    // انتظار قصير للتأكد من تحميل جميع العناصر
    setTimeout(fixSalesCSS, 500);
});

// إضافة مراقب للتغييرات في DOM لإصلاح المشاكل تلقائياً
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        let needsFix = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // التحقق من إضافة عناصر جديدة تحتاج إصلاح
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && (
                            node.classList.contains('sales-container') ||
                            node.classList.contains('table') ||
                            node.id === 'saleItemsTable'
                        )) {
                            needsFix = true;
                        }
                    }
                });
            }
        });

        if (needsFix) {
            console.log('🔧 تم اكتشاف تغييرات في DOM، تطبيق الإصلاحات...');
            setTimeout(fixSalesCSS, 100);
        }
    });

    // بدء مراقبة التغييرات
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// إضافة وظيفة للتحقق من التنسيق وإصلاحه عند الحاجة
function checkAndFixSalesLayout() {
    const salesContainer = document.querySelector('.sales-container');
    if (salesContainer) {
        console.log('🔍 فحص تخطيط المبيعات...');
        
        // التحقق من وجود المشاكل الشائعة
        const cards = salesContainer.querySelectorAll('.card');
        const tables = salesContainer.querySelectorAll('table');
        const buttons = salesContainer.querySelectorAll('.btn');
        
        let needsFix = false;
        
        // فحص البطاقات
        cards.forEach(card => {
            const computedStyle = window.getComputedStyle(card);
            if (computedStyle.backgroundColor === 'rgba(0, 0, 0, 0)' || 
                computedStyle.backgroundColor === 'transparent') {
                needsFix = true;
            }
        });
        
        // فحص الجداول
        tables.forEach(table => {
            const computedStyle = window.getComputedStyle(table);
            const parentWidth = table.parentElement.offsetWidth;
            const tableWidth = table.offsetWidth;

            // التحقق من أن الجدول يأخذ العرض الكامل
            if (computedStyle.width !== '100%' || tableWidth < parentWidth * 0.95) {
                needsFix = true;
                console.log('🔧 جدول يحتاج إصلاح:', {
                    computedWidth: computedStyle.width,
                    actualWidth: tableWidth,
                    parentWidth: parentWidth,
                    percentage: (tableWidth / parentWidth * 100).toFixed(2) + '%'
                });
            }
        });
        
        if (needsFix) {
            console.log('⚠️ تم اكتشاف مشاكل في التنسيق، تطبيق الإصلاحات...');
            fixSalesCSS();
        } else {
            console.log('✅ التنسيق يعمل بشكل صحيح');
        }
    }
}

/**
 * إصلاح مخصص للجداول
 */
function fixTablesSpecifically() {
    console.log('🔧 إصلاح مخصص للجداول...');

    const tables = document.querySelectorAll('.sales-container table, #saleItemsTable, .table');

    tables.forEach((table, index) => {
        console.log(`🔍 إصلاح الجدول ${index + 1}:`, table);

        // إجبار العرض على 100%
        table.style.width = '100%';
        table.style.maxWidth = '100%';
        table.style.minWidth = '100%';
        table.style.tableLayout = 'fixed';
        table.style.borderCollapse = 'collapse';

        // إصلاح الحاوي
        const container = table.closest('.card-body, .sale-items');
        if (container) {
            container.style.width = '100%';
            container.style.overflow = 'visible';
        }

        // إصلاح الخلايا
        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.wordWrap = 'break-word';
            cell.style.textAlign = 'right';
        });

        console.log(`✅ تم إصلاح الجدول ${index + 1}`);
    });

    // إجبار إعادة رسم الصفحة
    document.body.style.display = 'none';
    document.body.offsetHeight; // trigger reflow
    document.body.style.display = '';

    console.log('✅ تم إصلاح جميع الجداول');
}

// تصدير الوظائف للاستخدام الخارجي
if (typeof window !== 'undefined') {
    window.fixSalesCSS = fixSalesCSS;
    window.checkAndFixSalesLayout = checkAndFixSalesLayout;
    window.fixTablesSpecifically = fixTablesSpecifically;
}
