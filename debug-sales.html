<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصحيح أخطاء المبيعات - تكنوفلاش</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .status-success { border-left-color: #28a745; background: #d4edda; }
        .status-error { border-left-color: #dc3545; background: #f8d7da; }
        .status-warning { border-left-color: #ffc107; background: #fff3cd; }
        .test-btn {
            margin: 10px 5px;
            padding: 10px 20px;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1><i class="fas fa-bug"></i> تصحيح أخطاء المبيعات</h1>
        <p>هذه الصفحة لتصحيح مشاكل الطباعة في نظام المبيعات</p>

        <div class="row">
            <div class="col-md-6">
                <h3>فحص المكتبات</h3>
                <div id="libraryStatus"></div>
                
                <h3>فحص الوظائف</h3>
                <div id="functionStatus"></div>
            </div>
            
            <div class="col-md-6">
                <h3>اختبارات</h3>
                <button class="btn btn-primary test-btn" onclick="testLibraries()">
                    <i class="fas fa-check"></i> فحص المكتبات
                </button>
                <button class="btn btn-success test-btn" onclick="testPrintFunctions()">
                    <i class="fas fa-print"></i> فحص وظائف الطباعة
                </button>
                <button class="btn btn-warning test-btn" onclick="testSaleCreation()">
                    <i class="fas fa-shopping-cart"></i> اختبار إنشاء بيع
                </button>
                <button class="btn btn-info test-btn" onclick="testModal()">
                    <i class="fas fa-window-maximize"></i> اختبار المودال
                </button>
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
            === سجل التصحيح ===<br>
        </div>
    </div>

    <!-- تضمين المكتبات -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- ملفات النظام -->
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    
    <script>
        // محاكاة app object
        window.app = {
            showAlert: function(title, message) {
                log(`ALERT: ${title} - ${message}`, 'error');
                alert(title + ': ' + message);
            },
            showNotification: function(message, type) {
                log(`NOTIFICATION (${type}): ${message}`, type);
            },
            showLoading: function() {
                log('LOADING: عرض شاشة التحميل', 'info');
            },
            hideLoading: function() {
                log('LOADING: إخفاء شاشة التحميل', 'info');
            }
        };

        // تهيئة قاعدة البيانات
        let db;
        try {
            db = new Database();
            log('✅ تم تهيئة قاعدة البيانات بنجاح', 'success');
        } catch (error) {
            log('❌ فشل في تهيئة قاعدة البيانات: ' + error.message, 'error');
        }

        function log(message, type = 'info') {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            output.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            output.scrollTop = output.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function testLibraries() {
            const statusDiv = document.getElementById('libraryStatus');
            let html = '';
            
            const libraries = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'Database', check: () => typeof Database !== 'undefined' },
                { name: 'BarcodeGenerator', check: () => typeof BarcodeGenerator !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                const available = lib.check();
                const status = available ? 'success' : 'error';
                const icon = available ? 'check' : 'times';
                html += `<div class="status-item status-${status}">
                    <i class="fas fa-${icon}"></i> ${lib.name}: ${available ? 'متوفر' : 'غير متوفر'}
                </div>`;
                log(`${lib.name}: ${available ? 'متوفر' : 'غير متوفر'}`, status);
            });
            
            statusDiv.innerHTML = html;
        }

        function testPrintFunctions() {
            const statusDiv = document.getElementById('functionStatus');
            let html = '';
            
            const functions = [
                'printThermalInvoice',
                'printStandardInvoice',
                'printSaleItemsLabels',
                'testThermalPrinter',
                'sendToThermalPrinter',
                'ESC_POS_COMMANDS'
            ];
            
            functions.forEach(funcName => {
                const available = typeof window[funcName] !== 'undefined';
                const status = available ? 'success' : 'error';
                const icon = available ? 'check' : 'times';
                html += `<div class="status-item status-${status}">
                    <i class="fas fa-${icon}"></i> ${funcName}: ${available ? 'متوفر' : 'غير متوفر'}
                </div>`;
                log(`${funcName}: ${available ? 'متوفر' : 'غير متوفر'}`, status);
            });
            
            statusDiv.innerHTML = html;
        }

        function testSaleCreation() {
            try {
                log('🛒 اختبار إنشاء بيع تجريبي...', 'info');
                
                const testSale = {
                    customerId: 'guest',
                    items: [
                        {
                            productId: 'test1',
                            name: 'منتج تجريبي 1',
                            quantity: 2,
                            price: 25.50,
                            total: 51.00
                        }
                    ],
                    subtotal: 51.00,
                    discount: 0,
                    discountAmount: 0,
                    tax: 0,
                    taxAmount: 0,
                    total: 51.00,
                    paymentMethod: 'cash',
                    amountPaid: 51.00,
                    notes: 'بيع تجريبي'
                };
                
                log('📄 بيانات البيع التجريبي: ' + JSON.stringify(testSale, null, 2), 'info');
                
                // محاولة حفظ البيع
                if (db && typeof db.addSale === 'function') {
                    const savedSale = db.addSale(testSale);
                    if (savedSale) {
                        log('✅ تم حفظ البيع التجريبي بنجاح: ' + savedSale.id, 'success');
                        
                        // اختبار استرجاع البيع
                        const retrievedSale = db.getSale(savedSale.id);
                        if (retrievedSale) {
                            log('✅ تم استرجاع البيع بنجاح', 'success');
                            
                            // اختبار الطباعة
                            if (typeof printThermalInvoice !== 'undefined') {
                                log('🖨️ اختبار طباعة الفاتورة الحرارية...', 'info');
                                printThermalInvoice(retrievedSale);
                                log('✅ تم إرسال الفاتورة للطباعة', 'success');
                            } else {
                                log('❌ وظيفة printThermalInvoice غير متوفرة', 'error');
                            }
                        } else {
                            log('❌ فشل في استرجاع البيع', 'error');
                        }
                    } else {
                        log('❌ فشل في حفظ البيع', 'error');
                    }
                } else {
                    log('❌ قاعدة البيانات أو وظيفة addSale غير متوفرة', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في اختبار إنشاء البيع: ' + error.message, 'error');
            }
        }

        function testModal() {
            try {
                log('🪟 اختبار المودال...', 'info');
                
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = 'testModal';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">اختبار المودال</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>هذا اختبار للمودال. إذا ظهر هذا النص، فالمودال يعمل بشكل صحيح.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const bootstrapModal = new bootstrap.Modal(modal);
                    bootstrapModal.show();
                    log('✅ تم عرض المودال بنجاح باستخدام Bootstrap 5', 'success');
                    
                    modal.addEventListener('hidden.bs.modal', function() {
                        document.body.removeChild(modal);
                        log('✅ تم إغلاق المودال وإزالته', 'success');
                    });
                } else {
                    log('❌ Bootstrap غير متوفر', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في اختبار المودال: ' + error.message, 'error');
            }
        }

        // تشغيل الفحوصات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة التصحيح', 'info');
            setTimeout(() => {
                testLibraries();
                testPrintFunctions();
            }, 500);
        });
    </script>
</body>
</html>
