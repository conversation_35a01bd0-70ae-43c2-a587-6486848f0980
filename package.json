{"name": "technoflash-pos", "version": "1.0.0", "description": "نظام نقطة البيع العربي الشامل - تكنوفلاش", "main": "electron-main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["pos", "point-of-sale", "arabic", "retail", "inventory", "sales", "electron"], "author": {"name": "TechnoFlash", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.technoflash.pos", "productName": "تكنوفلاش - نقطة البيع", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!.git", "!*.md"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "تكنوفلاش - نقطة البيع"}}}