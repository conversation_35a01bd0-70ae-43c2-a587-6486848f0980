<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات المحسن - تكنوفلاش</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: var(--bg-card);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }
        
        .test-btn.primary {
            background: var(--primary-color);
            color: white;
        }
        
        .test-btn.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid #ddd;
        }
        
        .currency-test {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .number-test {
            font-size: 20px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام الإعدادات</h1>
        
        <div class="test-controls">
            <button class="test-btn primary" onclick="toggleDarkMode()">تبديل الوضع الليلي</button>
            <button class="test-btn secondary" onclick="changeCurrency('USD')">دولار أمريكي</button>
            <button class="test-btn secondary" onclick="changeCurrency('SAR')">ريال سعودي</button>
            <button class="test-btn secondary" onclick="changeCurrency('EUR')">يورو</button>
            <button class="test-btn secondary" onclick="changeNumbers('arabic')">أرقام عربية</button>
            <button class="test-btn secondary" onclick="changeNumbers('english')">أرقام إنجليزية</button>
        </div>
        
        <div class="test-section">
            <h3>اختبار العملة</h3>
            <div class="currency-test currency">1500</div>
            <div class="currency-test currency">2750</div>
            <div class="currency-test currency">999</div>
        </div>
        
        <div class="test-section">
            <h3>اختبار الأرقام</h3>
            <div class="number-test number">الرقم: 12345</div>
            <div class="number-test number">التاريخ: 2024/01/15</div>
            <div class="number-test number">الوقت: 14:30</div>
        </div>
        
        <div class="test-section">
            <h3>اختبار مختلط</h3>
            <div class="number currency">المبلغ: 5000</div>
            <div class="number">الكمية: 25</div>
            <div class="currency">السعر: 200</div>
        </div>
        
        <div class="test-section">
            <h3>معلومات النظام</h3>
            <p>الوضع الحالي: <span id="currentMode">عادي</span></p>
            <p>العملة الحالية: <span id="currentCurrency">SAR</span></p>
            <p>نوع الأرقام: <span id="currentNumbers">عربية</span></p>
        </div>
    </div>

    <script>
        let isDarkMode = false;
        let currentCurrency = 'SAR';
        let currentNumberType = 'arabic';
        
        function toggleDarkMode() {
            isDarkMode = !isDarkMode;
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
                document.getElementById('currentMode').textContent = 'ليلي';
            } else {
                document.body.classList.remove('dark-mode');
                document.getElementById('currentMode').textContent = 'عادي';
            }
            
            // إرسال حدث تحديث الإعدادات
            window.dispatchEvent(new CustomEvent('settingsUpdated', {
                detail: {
                    general: {
                        darkMode: isDarkMode,
                        currency: currentCurrency,
                        numberType: currentNumberType
                    }
                }
            }));
        }
        
        function changeCurrency(currency) {
            currentCurrency = currency;
            document.documentElement.setAttribute('data-currency', currency);
            document.getElementById('currentCurrency').textContent = currency;
            
            // إرسال حدث تحديث الإعدادات
            window.dispatchEvent(new CustomEvent('settingsUpdated', {
                detail: {
                    general: {
                        darkMode: isDarkMode,
                        currency: currency,
                        numberType: currentNumberType
                    }
                }
            }));
        }
        
        function changeNumbers(numberType) {
            currentNumberType = numberType;
            document.documentElement.setAttribute('data-number-type', numberType);
            document.getElementById('currentNumbers').textContent = numberType === 'arabic' ? 'عربية' : 'إنجليزية';
            
            // إرسال حدث تحديث الإعدادات
            window.dispatchEvent(new CustomEvent('settingsUpdated', {
                detail: {
                    general: {
                        darkMode: isDarkMode,
                        currency: currentCurrency,
                        numberType: numberType
                    }
                }
            }));
        }
        
        // تطبيق الإعدادات الافتراضية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            document.documentElement.setAttribute('data-currency', 'EGP');
            document.documentElement.setAttribute('data-number-type', 'arabic');
        });
    </script>
</body>
</html>
