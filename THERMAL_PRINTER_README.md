# نظام الطباعة الحرارية المحسن - تكنوفلاش POS

## نظرة عامة

نظام طباعة حرارية متقدم ومحسن لنظام تكنوفلاش POS يدعم طباعة الفواتير وملصقات الباركود على الطابعات الحرارية بجودة عالية وموثوقية ممتازة.

## ✅ المشاكل التي تم حلها

- **إزالة الدوال المكررة**: تم حذف جميع الدوال المكررة مثل `repeatChar` و `createStandardInvoiceHTML` و `printMultipleBarcodeLabels`
- **استبدال document.write المهجور**: تم استبدال جميع استخدامات `document.write` بطرق حديثة وآمنة
- **تحسين معالجة الأخطاء**: إضافة معالجة شاملة للأخطاء مع رسائل واضحة
- **فحص توفر الطابعة**: إضافة نظام فحص تلقائي لحالة الطابعة
- **تحسين إدارة النوافذ**: معالجة أفضل لنوافذ الطباعة وإغلاقها

## 🚀 الميزات الرئيسية

### 1. طباعة الفواتير الحرارية
- دعم أحجام الورق 58mm و 80mm
- تنسيق محسن للنصوص العربية
- عرض معلومات الشركة والعميل
- جداول منتجات منسقة
- حساب المجاميع والضرائب والخصومات

### 2. طباعة ملصقات الباركود
- دعم أنواع باركود متعددة (Code128, EAN13, UPC-A)
- ملصقات صغيرة محسنة (40mm x 30mm)
- ملصقات مخصصة بأحجام متغيرة
- طباعة ملصقات متعددة

### 3. طباعة الفواتير العادية (A4)
- تصميم احترافي للفواتير
- دعم كامل للطباعة على ورق A4
- تنسيق متقدم مع CSS

### 4. اختبار شامل للطابعة
- اختبار جميع أوامر ESC/POS
- فحص الخطوط والأحجام
- اختبار المحاذاة والتنسيق
- اختبار طباعة الباركود

## 🛠️ الدوال الرئيسية

### دوال الطباعة
```javascript
// طباعة فاتورة حرارية
printThermalInvoice(saleData)

// طباعة ملصق باركود
printProductBarcodeLabel(productId)

// طباعة فاتورة عادية A4
printStandardInvoice(saleData)

// طباعة ملصقات متعددة
printMultipleBarcodeLabels(productIds)

// طباعة ملصق مخصص
printCustomSizeBarcodeLabel(product, options)
```

### دوال الاختبار والفحص
```javascript
// اختبار شامل للطابعة
testThermalPrinter()

// فحص توفر الطابعة
checkPrinterAvailability()

// عرض حالة الطابعة
showPrinterStatus()

// عرض معلومات النظام
showThermalPrinterInfo()
```

### دوال إدارة الإعدادات
```javascript
// تكوين إعدادات الطابعة
configureThermalPrinter(settings)

// الحصول على الإعدادات الحالية
getThermalPrinterSettings()

// إعادة تعيين الإعدادات
resetThermalPrinterSettings()

// تصدير الإعدادات
exportThermalPrinterSettings()

// استيراد الإعدادات
importThermalPrinterSettings(file)
```

## ⚙️ الإعدادات المتاحة

```javascript
const THERMAL_SETTINGS = {
    PAPER_WIDTH_58MM: 32,    // عرض الورق 58mm بالأحرف
    PAPER_WIDTH_80MM: 48,    // عرض الورق 80mm بالأحرف
    BARCODE_HEIGHT: 50,      // ارتفاع الباركود
    BARCODE_WIDTH: 2,        // عرض خطوط الباركود
    LABEL_WIDTH: 32,         // عرض الملصق
    LABEL_HEIGHT: 8          // ارتفاع الملصق
};
```

## 🎯 أوامر ESC/POS المدعومة

### أوامر التهيئة
- `INIT`: تهيئة الطابعة
- `RESET`: إعادة تعيين
- `WAKE_UP`: تنشيط الطابعة

### أوامر النص
- `BOLD_ON/OFF`: خط عريض
- `UNDERLINE_ON/OFF`: تسطير
- `DOUBLE_HEIGHT/WIDTH/SIZE`: أحجام مضاعفة
- `NORMAL_SIZE`: الحجم العادي

### أوامر المحاذاة
- `ALIGN_LEFT`: محاذاة يسار
- `ALIGN_CENTER`: محاذاة وسط
- `ALIGN_RIGHT`: محاذاة يمين

### أوامر الباركود
- `BARCODE_CODE128`: باركود Code128
- `BARCODE_EAN13`: باركود EAN13
- `BARCODE_UPC_A`: باركود UPC-A
- `BARCODE_HEIGHT/WIDTH`: تحكم في الأبعاد

### أوامر القطع والتغذية
- `CUT_FULL/PARTIAL`: قطع كامل/جزئي
- `FEED_LINES`: تغذية أسطر
- `FEED_DOTS`: تغذية نقاط

## 📋 كيفية الاستخدام

### 1. تضمين الملف
```html
<script src="thermal-printer.js"></script>
```

### 2. طباعة فاتورة حرارية
```javascript
const saleData = {
    id: 'sale-001',
    date: new Date().toISOString(),
    customerId: 'customer-001',
    items: [
        { productId: 'prod-001', quantity: 2, price: 25.50 },
        { productId: 'prod-002', quantity: 1, price: 15.00 }
    ],
    total: 66.00,
    paymentMethod: 'cash'
};

printThermalInvoice(saleData);
```

### 3. طباعة ملصق باركود
```javascript
printProductBarcodeLabel('product-id-123');
```

### 4. اختبار الطابعة
```javascript
testThermalPrinter();
```

## 🧪 ملف الاختبار

يتضمن النظام ملف اختبار شامل `thermal-printer-test.html` يحتوي على:

- واجهة مستخدم لاختبار جميع الوظائف
- محاكاة قاعدة البيانات للاختبار
- سجل أحداث مفصل
- إدارة الإعدادات

### تشغيل الاختبار
1. افتح `thermal-printer-test.html` في المتصفح
2. اضغط على "فحص حالة النظام"
3. جرب الوظائف المختلفة
4. راقب سجل الأحداث

## 🔧 متطلبات النظام

- متصفح حديث يدعم ES6+
- السماح بالنوافذ المنبثقة للطباعة
- طابعة حرارية متوافقة مع ESC/POS

## 📝 ملاحظات مهمة

1. **النوافذ المنبثقة**: تأكد من السماح بالنوافذ المنبثقة في المتصفح
2. **الطابعة**: تأكد من تشغيل الطابعة وتوصيلها بشكل صحيح
3. **الإعدادات**: يمكن تخصيص الإعدادات حسب نوع الطابعة
4. **الاختبار**: استخدم ملف الاختبار للتأكد من عمل جميع الوظائف

## 🆕 الإصدار 2.0.0

### التحسينات الجديدة
- حل جميع المشاكل البرمجية
- تحسين الأداء والموثوقية
- إضافة نظام إدارة الإعدادات
- تحسين معالجة الأخطاء
- إضافة ملف اختبار شامل
- توثيق كامل للنظام

### التوافق
- متوافق مع جميع إصدارات تكنوفلاش POS
- يعمل مع جميع الطابعات الحرارية المتوافقة مع ESC/POS
- دعم كامل للغة العربية والنصوص RTL

---

**تم تطوير هذا النظام بواسطة فريق تكنوفلاش POS**
**الإصدار: 2.0.0**
**التاريخ: 2025**
