<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .card-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            border-radius: 8px 8px 0 0;
        }
        .card-body {
            padding: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <h1>اختبار صفحة الإعدادات</h1>
    
    <button class="btn btn-primary" onclick="loadSettings()">تحميل صفحة الإعدادات</button>
    <button class="btn btn-success" onclick="testSettings()">اختبار الإعدادات</button>
    
    <div id="mainContent">
        <!-- سيتم تحميل محتوى الإعدادات هنا -->
    </div>

    <script src="settings.js"></script>
    <script>
        function testSettings() {
            console.log('🧪 بدء اختبار الإعدادات...');
            
            // اختبار الدوال الأساسية
            try {
                const defaultSettings = getDefaultSettings();
                console.log('✅ getDefaultSettings تعمل:', defaultSettings);
                
                loadCurrentSettings();
                console.log('✅ loadCurrentSettings تعمل');
                
                // اختبار ملء النماذج
                setTimeout(() => {
                    try {
                        populateSettingsForms();
                        console.log('✅ populateSettingsForms تعمل');
                        
                        // التحقق من ملء الإعدادات العامة
                        const generalForm = document.getElementById('generalSettingsForm');
                        if (generalForm) {
                            const languageSelect = generalForm.querySelector('[name="language"]');
                            const currencySelect = generalForm.querySelector('[name="currency"]');
                            
                            if (languageSelect && languageSelect.value === 'ar') {
                                console.log('✅ تم ملء اللغة بشكل صحيح');
                            }
                            if (currencySelect && currencySelect.value === 'EGP') {
                                console.log('✅ تم ملء العملة بشكل صحيح');
                            }
                        }
                        
                        console.log('✅ جميع الاختبارات نجحت!');
                        alert('✅ جميع الاختبارات نجحت! قسم الإعدادات العامة يعمل الآن.');
                    } catch (populateError) {
                        console.error('❌ خطأ في ملء النماذج:', populateError);
                        alert('❌ خطأ في ملء النماذج: ' + populateError.message);
                    }
                }, 200);
                
            } catch (error) {
                console.error('❌ فشل الاختبار:', error);
                alert('❌ فشل الاختبار: ' + error.message);
            }
        }
    </script>
</body>
</html>