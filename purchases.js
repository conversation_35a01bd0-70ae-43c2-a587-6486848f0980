/**
 * إدارة المشتريات - تكنوفلاش
 */

let currentPurchase = null;
let purchasesData = [];
let purchaseItems = [];

/**
 * تحميل صفحة المشتريات
 */
async function loadPurchases() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-shopping-cart"></i> إدارة المشتريات</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddPurchaseModal()">
                    <i class="fas fa-plus"></i> إضافة مشترى
                </button>
                <button class="btn btn-info" onclick="exportPurchases()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>

        <!-- شريط البحث والفلاتر -->
        <div class="filters-section">
            <div class="card">
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <div class="search-bar">
                                <input type="text" class="search-input" id="purchaseSearchInput" 
                                       placeholder="البحث برقم الفاتورة أو المورد...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="supplierFilter">
                                <option value="">جميع الموردين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="date" class="form-control" id="dateFromFilter">
                        </div>
                        <div class="form-group">
                            <input type="date" class="form-control" id="dateToFilter">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المشتريات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-value" id="totalPurchasesCount">٠</div>
                <div class="stat-label">إجمالي المشتريات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-value" id="totalPurchasesAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي قيمة المشتريات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-value" id="todayPurchasesCount">٠</div>
                <div class="stat-label">مشتريات اليوم</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="stat-value" id="monthPurchasesAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">مشتريات هذا الشهر</div>
            </div>
        </div>

        <!-- جدول المشتريات -->
        <div class="card">
            <div class="card-body">
                <div class="table-container">
                    <table class="table" id="purchasesTable">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المورد</th>
                                <th>التاريخ</th>
                                <th>عدد الأصناف</th>
                                <th>الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم تحميل البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل مشترى -->
        <div id="purchaseModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3 id="purchaseModalTitle">إضافة مشترى جديد</h3>
                    <button class="modal-close" onclick="app.hideModal('purchaseModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="purchaseForm">
                        <!-- معلومات المشترى -->
                        <div class="form-section">
                            <h4>معلومات المشترى</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>رقم الفاتورة *</label>
                                    <input type="text" class="form-control" name="invoiceNumber" required>
                                </div>
                                <div class="form-group">
                                    <label>المورد *</label>
                                    <select class="form-control" name="supplierId" required>
                                        <option value="">اختر المورد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>تاريخ المشترى *</label>
                                    <input type="date" class="form-control" name="date" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="2" 
                                          placeholder="ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <!-- إضافة منتج -->
                        <div class="form-section">
                            <h4>إضافة منتج</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المنتج</label>
                                    <select class="form-control" id="productSelect">
                                        <option value="">اختر المنتج</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الكمية</label>
                                    <input type="number" class="form-control" id="quantityInput" 
                                           min="1" value="1">
                                </div>
                                <div class="form-group">
                                    <label>سعر الشراء</label>
                                    <input type="number" class="form-control" id="costPriceInput" 
                                           step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <button type="button" class="btn btn-primary" onclick="addPurchaseItem()">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة المنتجات -->
                        <div class="form-section">
                            <h4>المنتجات المضافة</h4>
                            <div class="table-container">
                                <table class="table" id="purchaseItemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الشراء</th>
                                            <th>الإجمالي</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم تحميل المنتجات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- ملخص المشترى -->
                        <div class="form-section">
                            <div class="purchase-summary">
                                <div class="summary-row">
                                    <span>عدد الأصناف:</span>
                                    <span id="totalItems">٠</span>
                                </div>
                                <div class="summary-row">
                                    <span>إجمالي الكمية:</span>
                                    <span id="totalQuantity">٠</span>
                                </div>
                                <div class="summary-row total">
                                    <span>الإجمالي:</span>
                                    <span id="totalAmount">٠.٠٠ ر.س</span>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الدفع -->
                        <div class="form-section">
                            <h4>معلومات الدفع</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المبلغ المدفوع</label>
                                    <input type="number" class="form-control" name="paidAmount" 
                                           step="0.01" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label>طريقة الدفع</label>
                                    <select class="form-control" name="paymentMethod">
                                        <option value="cash">نقداً</option>
                                        <option value="card">بطاقة</option>
                                        <option value="transfer">تحويل</option>
                                        <option value="credit">آجل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المشترى
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('purchaseModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة عرض تفاصيل المشترى -->
        <div id="purchaseDetailsModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>تفاصيل المشترى</h3>
                    <button class="modal-close" onclick="app.hideModal('purchaseDetailsModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="purchaseDetailsContent">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializePurchasesPage();
}

/**
 * تهيئة صفحة المشتريات
 */
async function initializePurchasesPage() {
    // تحميل البيانات
    loadPurchasesData();
    
    // تحميل الموردين والمنتجات
    loadSuppliersOptions();
    loadProductsOptions();
    
    // إعداد مستمعي الأحداث
    setupPurchasesEventListeners();
    
    // عرض المشتريات والإحصائيات
    displayPurchases();
    updatePurchasesStats();
}

/**
 * تحميل بيانات المشتريات
 */
function loadPurchasesData() {
    purchasesData = db.getPurchases();
}

/**
 * تحميل خيارات الموردين
 */
function loadSuppliersOptions() {
    const suppliers = db.getSuppliers().filter(s => s.isActive);
    const supplierSelect = document.querySelector('[name="supplierId"]');
    const supplierFilter = document.getElementById('supplierFilter');
    
    // مسح الخيارات الحالية
    supplierSelect.innerHTML = '<option value="">اختر المورد</option>';
    supplierFilter.innerHTML = '<option value="">جميع الموردين</option>';
    
    suppliers.forEach(supplier => {
        const option1 = document.createElement('option');
        option1.value = supplier.id;
        option1.textContent = supplier.name;
        supplierSelect.appendChild(option1);
        
        const option2 = document.createElement('option');
        option2.value = supplier.id;
        option2.textContent = supplier.name;
        supplierFilter.appendChild(option2);
    });
}

/**
 * تحميل خيارات المنتجات
 */
function loadProductsOptions() {
    const products = db.getProducts();
    const productSelect = document.getElementById('productSelect');
    
    // مسح الخيارات الحالية
    productSelect.innerHTML = '<option value="">اختر المنتج</option>';
    
    products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} - ${db.formatCurrency(product.costPrice || product.price)}`;
        option.dataset.costPrice = product.costPrice || product.price;
        productSelect.appendChild(option);
    });
}

/**
 * إعداد مستمعي الأحداث
 */
function setupPurchasesEventListeners() {
    // البحث والفلاتر
    document.getElementById('purchaseSearchInput').addEventListener('input', filterPurchases);
    document.getElementById('supplierFilter').addEventListener('change', filterPurchases);
    document.getElementById('dateFromFilter').addEventListener('change', filterPurchases);
    document.getElementById('dateToFilter').addEventListener('change', filterPurchases);
    
    // نموذج المشترى
    document.getElementById('purchaseForm').addEventListener('submit', handlePurchaseSubmit);
    
    // اختيار المنتج
    document.getElementById('productSelect').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.costPrice) {
            document.getElementById('costPriceInput').value = selectedOption.dataset.costPrice;
        }
    });
    
    // تعيين التاريخ الحالي
    document.querySelector('[name="date"]').value = new Date().toISOString().split('T')[0];
}

/**
 * عرض المشتريات
 */
function displayPurchases(purchases = purchasesData) {
    const tbody = document.querySelector('#purchasesTable tbody');

    if (purchases.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">لا توجد مشتريات</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = purchases.map(purchase => {
        const supplier = db.getSupplier(purchase.supplierId);
        const remaining = purchase.total - purchase.paidAmount;

        return `
            <tr>
                <td><strong>${purchase.invoiceNumber}</strong></td>
                <td>${supplier ? supplier.name : 'غير محدد'}</td>
                <td>${db.formatDate(purchase.date)}</td>
                <td>${db.toArabicNumerals(purchase.items.length)}</td>
                <td>${db.formatCurrency(purchase.total)}</td>
                <td>${db.formatCurrency(purchase.paidAmount)}</td>
                <td>
                    <span class="balance-amount ${remaining > 0 ? 'debt' : 'clear'}">
                        ${db.formatCurrency(remaining)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="viewPurchaseDetails('${purchase.id}')"
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editPurchase('${purchase.id}')"
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="printPurchase('${purchase.id}')"
                                title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deletePurchase('${purchase.id}')"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * تحديث إحصائيات المشتريات
 */
function updatePurchasesStats() {
    const totalPurchases = purchasesData.length;
    const totalAmount = purchasesData.reduce((sum, p) => sum + p.total, 0);

    // مشتريات اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayPurchases = purchasesData.filter(p => p.date === today);

    // مشتريات هذا الشهر
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const monthPurchases = purchasesData.filter(p =>
        new Date(p.date) >= thisMonth
    );
    const monthAmount = monthPurchases.reduce((sum, p) => sum + p.total, 0);

    document.getElementById('totalPurchasesCount').textContent = db.toArabicNumerals(totalPurchases);
    document.getElementById('totalPurchasesAmount').textContent = db.formatCurrency(totalAmount);
    document.getElementById('todayPurchasesCount').textContent = db.toArabicNumerals(todayPurchases.length);
    document.getElementById('monthPurchasesAmount').textContent = db.formatCurrency(monthAmount);
}

/**
 * فلترة المشتريات
 */
function filterPurchases() {
    const searchTerm = document.getElementById('purchaseSearchInput').value.toLowerCase();
    const supplierFilter = document.getElementById('supplierFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;

    let filteredPurchases = purchasesData;

    // فلترة بالبحث
    if (searchTerm) {
        filteredPurchases = filteredPurchases.filter(purchase => {
            const supplier = db.getSupplier(purchase.supplierId);
            return purchase.invoiceNumber.toLowerCase().includes(searchTerm) ||
                   (supplier && supplier.name.toLowerCase().includes(searchTerm));
        });
    }

    // فلترة بالمورد
    if (supplierFilter) {
        filteredPurchases = filteredPurchases.filter(purchase =>
            purchase.supplierId === supplierFilter
        );
    }

    // فلترة بالتاريخ
    if (dateFrom) {
        filteredPurchases = filteredPurchases.filter(purchase =>
            purchase.date >= dateFrom
        );
    }

    if (dateTo) {
        filteredPurchases = filteredPurchases.filter(purchase =>
            purchase.date <= dateTo
        );
    }

    displayPurchases(filteredPurchases);
}

/**
 * إظهار نافذة إضافة مشترى
 */
function showAddPurchaseModal() {
    currentPurchase = null;
    purchaseItems = [];

    document.getElementById('purchaseModalTitle').textContent = 'إضافة مشترى جديد';
    document.getElementById('purchaseForm').reset();

    // توليد رقم فاتورة تلقائي
    const invoiceNumber = 'PUR-' + Date.now().toString().slice(-6);
    document.querySelector('[name="invoiceNumber"]').value = invoiceNumber;

    // تعيين التاريخ الحالي
    document.querySelector('[name="date"]').value = new Date().toISOString().split('T')[0];

    // مسح جدول المنتجات
    updatePurchaseItemsTable();
    updatePurchaseSummary();

    app.showModal('purchaseModal');
}

/**
 * إضافة منتج للمشترى
 */
function addPurchaseItem() {
    const productSelect = document.getElementById('productSelect');
    const quantityInput = document.getElementById('quantityInput');
    const costPriceInput = document.getElementById('costPriceInput');

    const productId = productSelect.value;
    const quantity = parseInt(quantityInput.value);
    const costPrice = parseFloat(costPriceInput.value);

    if (!productId) {
        app.showAlert('خطأ', 'يرجى اختيار منتج');
        return;
    }

    if (!quantity || quantity <= 0) {
        app.showAlert('خطأ', 'يرجى إدخال كمية صحيحة');
        return;
    }

    if (!costPrice || costPrice <= 0) {
        app.showAlert('خطأ', 'يرجى إدخال سعر صحيح');
        return;
    }

    const product = db.getProduct(productId);
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }

    // التحقق من وجود المنتج مسبقاً
    const existingItemIndex = purchaseItems.findIndex(item => item.productId === productId);

    if (existingItemIndex >= 0) {
        // تحديث الكمية والسعر
        purchaseItems[existingItemIndex].quantity += quantity;
        purchaseItems[existingItemIndex].costPrice = costPrice;
        purchaseItems[existingItemIndex].total = purchaseItems[existingItemIndex].quantity * costPrice;
    } else {
        // إضافة منتج جديد
        purchaseItems.push({
            productId: productId,
            productName: product.name,
            quantity: quantity,
            costPrice: costPrice,
            total: quantity * costPrice
        });
    }

    // مسح الحقول
    productSelect.value = '';
    quantityInput.value = '1';
    costPriceInput.value = '';

    // تحديث الجدول والملخص
    updatePurchaseItemsTable();
    updatePurchaseSummary();
}

/**
 * تحديث جدول منتجات المشترى
 */
function updatePurchaseItemsTable() {
    const tbody = document.querySelector('#purchaseItemsTable tbody');

    if (purchaseItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">لم يتم إضافة منتجات</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = purchaseItems.map((item, index) => `
        <tr>
            <td>${item.productName}</td>
            <td>${db.toArabicNumerals(item.quantity)}</td>
            <td>${db.formatCurrency(item.costPrice)}</td>
            <td>${db.formatCurrency(item.total)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="removePurchaseItem(${index})"
                        title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

/**
 * حذف منتج من المشترى
 */
function removePurchaseItem(index) {
    purchaseItems.splice(index, 1);
    updatePurchaseItemsTable();
    updatePurchaseSummary();
}

/**
 * تحديث ملخص المشترى
 */
function updatePurchaseSummary() {
    const totalItems = purchaseItems.length;
    const totalQuantity = purchaseItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = purchaseItems.reduce((sum, item) => sum + item.total, 0);

    document.getElementById('totalItems').textContent = db.toArabicNumerals(totalItems);
    document.getElementById('totalQuantity').textContent = db.toArabicNumerals(totalQuantity);
    document.getElementById('totalAmount').textContent = db.formatCurrency(totalAmount);

    // تحديث المبلغ المدفوع ليكون مساوياً للإجمالي افتراضياً
    const paidAmountInput = document.querySelector('[name="paidAmount"]');
    if (paidAmountInput.value === '0' || paidAmountInput.value === '') {
        paidAmountInput.value = totalAmount;
    }
}

/**
 * معالجة إرسال نموذج المشترى
 */
async function handlePurchaseSubmit(e) {
    e.preventDefault();

    if (purchaseItems.length === 0) {
        app.showAlert('خطأ', 'يرجى إضافة منتجات للمشترى');
        return;
    }

    const formData = new FormData(e.target);
    const purchaseData = {
        invoiceNumber: formData.get('invoiceNumber').trim(),
        supplierId: formData.get('supplierId'),
        date: formData.get('date'),
        notes: formData.get('notes').trim(),
        items: [...purchaseItems],
        total: purchaseItems.reduce((sum, item) => sum + item.total, 0),
        paidAmount: parseFloat(formData.get('paidAmount')) || 0,
        paymentMethod: formData.get('paymentMethod')
    };

    // التحقق من صحة البيانات
    const validation = Validator.validateForm(purchaseData, {
        invoiceNumber: ['required'],
        supplierId: ['required'],
        date: ['required']
    });

    if (!validation.isValid) {
        Validator.displayFormErrors(validation.errors);
        return;
    }

    // التحقق من تفرد رقم الفاتورة
    const existingPurchase = purchasesData.find(p =>
        p.invoiceNumber === purchaseData.invoiceNumber &&
        (!currentPurchase || p.id !== currentPurchase.id)
    );

    if (existingPurchase) {
        app.showAlert('خطأ', 'رقم الفاتورة موجود مسبقاً');
        return;
    }

    app.showLoading();

    try {
        if (currentPurchase) {
            // تحديث مشترى موجود
            db.updatePurchase(currentPurchase.id, purchaseData);
            app.showNotification('تم تحديث المشترى بنجاح', 'success');
        } else {
            // إضافة مشترى جديد
            const purchaseId = db.addPurchase(purchaseData);

            // تحديث مخزون المنتجات
            for (const item of purchaseItems) {
                const product = db.getProduct(item.productId);
                if (product) {
                    const newQuantity = product.quantity + item.quantity;
                    const newCostPrice = item.costPrice;

                    db.updateProduct(item.productId, {
                        quantity: newQuantity,
                        costPrice: newCostPrice
                    });
                }
            }

            // تحديث رصيد المورد إذا كان هناك مبلغ متبقي
            const remaining = purchaseData.total - purchaseData.paidAmount;
            if (remaining > 0) {
                const supplier = db.getSupplier(purchaseData.supplierId);
                if (supplier) {
                    db.updateSupplier(supplier.id, {
                        balance: supplier.balance + remaining
                    });
                }
            }

            app.showNotification('تم إضافة المشترى بنجاح', 'success');
        }

        // تحديث العرض
        loadPurchasesData();
        displayPurchases();
        updatePurchasesStats();

        // إغلاق النافذة
        app.hideModal('purchaseModal');

    } catch (error) {
        console.error('خطأ في حفظ المشترى:', error);
        app.showAlert('خطأ', 'حدث خطأ في حفظ المشترى');
    }

    app.hideLoading();
}

/**
 * عرض تفاصيل المشترى
 */
function viewPurchaseDetails(purchaseId) {
    const purchase = db.getPurchase(purchaseId);
    if (!purchase) {
        app.showAlert('خطأ', 'المشترى غير موجود');
        return;
    }

    const supplier = db.getSupplier(purchase.supplierId);
    const remaining = purchase.total - purchase.paidAmount;

    const content = `
        <div class="purchase-details">
            <div class="details-header">
                <h4>فاتورة مشترى رقم: ${purchase.invoiceNumber}</h4>
                <p>التاريخ: ${db.formatDate(purchase.date)}</p>
            </div>

            <div class="supplier-info">
                <h5>معلومات المورد</h5>
                <p><strong>الاسم:</strong> ${supplier ? supplier.name : 'غير محدد'}</p>
                ${supplier && supplier.company ? `<p><strong>الشركة:</strong> ${supplier.company}</p>` : ''}
                ${supplier && supplier.phone ? `<p><strong>الهاتف:</strong> ${supplier.phone}</p>` : ''}
            </div>

            <div class="items-table">
                <h5>المنتجات</h5>
                <table class="table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الشراء</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${purchase.items.map(item => `
                            <tr>
                                <td>${item.productName}</td>
                                <td>${db.toArabicNumerals(item.quantity)}</td>
                                <td>${db.formatCurrency(item.costPrice)}</td>
                                <td>${db.formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="purchase-summary">
                <div class="summary-row">
                    <span>عدد الأصناف:</span>
                    <span>${db.toArabicNumerals(purchase.items.length)}</span>
                </div>
                <div class="summary-row">
                    <span>إجمالي الكمية:</span>
                    <span>${db.toArabicNumerals(purchase.items.reduce((sum, item) => sum + item.quantity, 0))}</span>
                </div>
                <div class="summary-row total">
                    <span>الإجمالي:</span>
                    <span>${db.formatCurrency(purchase.total)}</span>
                </div>
                <div class="summary-row">
                    <span>المدفوع:</span>
                    <span>${db.formatCurrency(purchase.paidAmount)}</span>
                </div>
                <div class="summary-row ${remaining > 0 ? 'debt' : 'clear'}">
                    <span>المتبقي:</span>
                    <span>${db.formatCurrency(remaining)}</span>
                </div>
            </div>

            ${purchase.notes ? `
                <div class="notes-section">
                    <h5>ملاحظات</h5>
                    <p>${purchase.notes}</p>
                </div>
            ` : ''}
        </div>
    `;

    document.getElementById('purchaseDetailsContent').innerHTML = content;
    app.showModal('purchaseDetailsModal');
}

/**
 * تعديل مشترى
 */
function editPurchase(purchaseId) {
    const purchase = db.getPurchase(purchaseId);
    if (!purchase) {
        app.showAlert('خطأ', 'المشترى غير موجود');
        return;
    }

    currentPurchase = purchase;
    purchaseItems = [...purchase.items];

    document.getElementById('purchaseModalTitle').textContent = 'تعديل المشترى';

    // ملء النموذج
    const form = document.getElementById('purchaseForm');
    form.invoiceNumber.value = purchase.invoiceNumber;
    form.supplierId.value = purchase.supplierId;
    form.date.value = purchase.date;
    form.notes.value = purchase.notes || '';
    form.paidAmount.value = purchase.paidAmount;
    form.paymentMethod.value = purchase.paymentMethod;

    // تحديث الجدول والملخص
    updatePurchaseItemsTable();
    updatePurchaseSummary();

    app.showModal('purchaseModal');
}

/**
 * طباعة المشترى
 */
function printPurchase(purchaseId) {
    const purchase = db.getPurchase(purchaseId);
    if (!purchase) {
        app.showAlert('خطأ', 'المشترى غير موجود');
        return;
    }

    const supplier = db.getSupplier(purchase.supplierId);
    const remaining = purchase.total - purchase.paidAmount;

    const printContent = `
        <div class="print-content" dir="rtl">
            <div class="print-header">
                <h2>تكنوفلاش - نظام نقاط البيع</h2>
                <h3>فاتورة مشترى</h3>
            </div>

            <div class="print-info">
                <div class="info-row">
                    <span>رقم الفاتورة: ${purchase.invoiceNumber}</span>
                    <span>التاريخ: ${db.formatDate(purchase.date)}</span>
                </div>
                <div class="info-row">
                    <span>المورد: ${supplier ? supplier.name : 'غير محدد'}</span>
                    <span>طريقة الدفع: ${getPaymentMethodName(purchase.paymentMethod)}</span>
                </div>
            </div>

            <table class="print-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>سعر الشراء</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${purchase.items.map(item => `
                        <tr>
                            <td>${item.productName}</td>
                            <td>${db.toArabicNumerals(item.quantity)}</td>
                            <td>${db.formatCurrency(item.costPrice)}</td>
                            <td>${db.formatCurrency(item.total)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="print-summary">
                <div class="summary-row">
                    <span>الإجمالي:</span>
                    <span>${db.formatCurrency(purchase.total)}</span>
                </div>
                <div class="summary-row">
                    <span>المدفوع:</span>
                    <span>${db.formatCurrency(purchase.paidAmount)}</span>
                </div>
                <div class="summary-row">
                    <span>المتبقي:</span>
                    <span>${db.formatCurrency(remaining)}</span>
                </div>
            </div>

            ${purchase.notes ? `<div class="print-notes">ملاحظات: ${purchase.notes}</div>` : ''}

            <div class="print-footer">
                <p>شكراً لتعاملكم معنا</p>
                <p>تم الطباعة في: ${db.formatDate(new Date().toISOString())}</p>
            </div>
        </div>
    `;

    Utils.printContent(printContent);
}

/**
 * حذف مشترى
 */
function deletePurchase(purchaseId) {
    const purchase = db.getPurchase(purchaseId);
    if (!purchase) {
        app.showAlert('خطأ', 'المشترى غير موجود');
        return;
    }

    app.showConfirm(
        'حذف المشترى',
        `هل أنت متأكد من حذف المشترى رقم "${purchase.invoiceNumber}"؟`,
        () => {
            // إرجاع المخزون
            for (const item of purchase.items) {
                const product = db.getProduct(item.productId);
                if (product) {
                    const newQuantity = Math.max(0, product.quantity - item.quantity);
                    db.updateProduct(item.productId, { quantity: newQuantity });
                }
            }

            // تحديث رصيد المورد
            const remaining = purchase.total - purchase.paidAmount;
            if (remaining > 0) {
                const supplier = db.getSupplier(purchase.supplierId);
                if (supplier) {
                    db.updateSupplier(supplier.id, {
                        balance: Math.max(0, supplier.balance - remaining)
                    });
                }
            }

            db.deletePurchase(purchaseId);
            app.showNotification('تم حذف المشترى بنجاح', 'success');

            loadPurchasesData();
            displayPurchases();
            updatePurchasesStats();
        }
    );
}

/**
 * تصدير المشتريات
 */
function exportPurchases() {
    const purchases = db.getPurchases();
    const exportData = purchases.map(purchase => {
        const supplier = db.getSupplier(purchase.supplierId);
        return {
            invoiceNumber: purchase.invoiceNumber,
            supplier: supplier ? supplier.name : 'غير محدد',
            date: purchase.date,
            items: purchase.items,
            total: purchase.total,
            paidAmount: purchase.paidAmount,
            paymentMethod: purchase.paymentMethod,
            notes: purchase.notes
        };
    });

    Utils.downloadJSON(exportData, `purchases_${new Date().toISOString().split('T')[0]}.json`);
    app.showNotification('تم تصدير المشتريات بنجاح', 'success');
}

/**
 * الحصول على اسم طريقة الدفع
 */
function getPaymentMethodName(method) {
    const methods = {
        cash: 'نقداً',
        card: 'بطاقة',
        transfer: 'تحويل',
        credit: 'آجل'
    };
    return methods[method] || method;
}
