/**
 * مولد الباركود أحادي البُعد
 * يدعم Code 128, EAN-13, UPC-A
 */

class BarcodeGenerator {
    constructor() {
        this.patterns = {
            // Code 128 patterns
            code128: {
                startA: '11010000100',
                startB: '11010010000',
                startC: '11010011000',
                stop: '1100011101011',
                patterns: {
                    '0': '11011001100', '1': '11001101100', '2': '11001100110',
                    '3': '10010011000', '4': '10010001100', '5': '10001001100',
                    '6': '10011001000', '7': '10011000100', '8': '10001100100',
                    '9': '11001001000', 'A': '11001000100', 'B': '11000100100',
                    'C': '10110011100', 'D': '10011011100', 'E': '10011001110',
                    'F': '10111001100', 'G': '10011101100', 'H': '10011100110',
                    'I': '11001110010', 'J': '11001011100', 'K': '11001001110',
                    'L': '11011100100', 'M': '11001110100', 'N': '11101101110',
                    'O': '11101001100', 'P': '11100101100', 'Q': '11100100110',
                    'R': '11101100100', 'S': '11100110100', 'T': '11100110010',
                    'U': '11011011000', 'V': '11011000110', 'W': '11000110110',
                    'X': '10100011000', 'Y': '10001011000', 'Z': '10001000110',
                    ' ': '10110001000', '-': '10001101000', '.': '10001100010'
                }
            }
        };
    }

    /**
     * توليد باركود Code 128
     */
    generateCode128(data) {
        if (!data || data.length === 0) {
            throw new Error('البيانات مطلوبة لتوليد الباركود');
        }

        let pattern = this.patterns.code128.startB;
        let checksum = 104; // Start B value

        // إضافة البيانات
        for (let i = 0; i < data.length; i++) {
            const char = data[i].toUpperCase();
            const charPattern = this.patterns.code128.patterns[char];
            
            if (!charPattern) {
                throw new Error(`الحرف غير مدعوم: ${char}`);
            }
            
            pattern += charPattern;
            checksum += this.getCharValue(char) * (i + 1);
        }

        // إضافة checksum
        const checksumChar = this.getCharFromValue(checksum % 103);
        pattern += this.patterns.code128.patterns[checksumChar];

        // إضافة stop pattern
        pattern += this.patterns.code128.stop;

        return pattern;
    }

    /**
     * توليد EAN-13 باركود
     */
    generateEAN13(data) {
        // تنظيف البيانات وضمان 12 رقم
        let cleanData = data.replace(/\D/g, '');
        
        if (cleanData.length > 12) {
            cleanData = cleanData.substring(0, 12);
        } else if (cleanData.length < 12) {
            cleanData = cleanData.padStart(12, '0');
        }

        // حساب check digit
        const checkDigit = this.calculateEAN13CheckDigit(cleanData);
        const fullCode = cleanData + checkDigit;

        return this.generateEAN13Pattern(fullCode);
    }

    /**
     * حساب check digit لـ EAN-13
     */
    calculateEAN13CheckDigit(data) {
        let sum = 0;
        for (let i = 0; i < 12; i++) {
            const digit = parseInt(data[i]);
            sum += (i % 2 === 0) ? digit : digit * 3;
        }
        return (10 - (sum % 10)) % 10;
    }

    /**
     * توليد نمط EAN-13
     */
    generateEAN13Pattern(data) {
        const leftPatterns = {
            '0': ['0001101', '0100111'], '1': ['0011001', '0110011'],
            '2': ['0010011', '0011011'], '3': ['0111101', '0100001'],
            '4': ['0100011', '0011101'], '5': ['0110001', '0111001'],
            '6': ['0101111', '0000101'], '7': ['0111011', '0010001'],
            '8': ['0110111', '0001001'], '9': ['0001011', '0010111']
        };

        const rightPattern = {
            '0': '1110010', '1': '1100110', '2': '1101100',
            '3': '1000010', '4': '1011100', '5': '1001110',
            '6': '1010000', '7': '1000100', '8': '1001000',
            '9': '1110100'
        };

        const firstDigitPatterns = [
            [0,0,0,0,0,0], [0,0,1,0,1,1], [0,0,1,1,0,1],
            [0,0,1,1,1,0], [0,1,0,0,1,1], [0,1,1,0,0,1],
            [0,1,1,1,0,0], [0,1,0,1,0,1], [0,1,0,1,1,0],
            [0,1,1,0,1,0]
        ];

        let pattern = '101'; // Start guard
        const firstDigit = parseInt(data[0]);
        const patternArray = firstDigitPatterns[firstDigit];

        // Left group
        for (let i = 1; i <= 6; i++) {
            const digit = data[i];
            const patternIndex = patternArray[i-1];
            pattern += leftPatterns[digit][patternIndex];
        }

        pattern += '01010'; // Center guard

        // Right group
        for (let i = 7; i <= 12; i++) {
            const digit = data[i];
            pattern += rightPattern[digit];
        }

        pattern += '101'; // End guard

        return pattern;
    }

    /**
     * توليد باركود فريد للمنتج
     */
    generateProductBarcode(productId, type = 'CODE128') {
        const timestamp = Date.now().toString().slice(-8);
        const productCode = productId.slice(-4).toUpperCase();
        
        switch (type.toUpperCase()) {
            case 'CODE128':
                return `TF${productCode}${timestamp}`;
            case 'EAN13':
                // تحويل إلى 12 رقم للـ EAN-13
                const numericId = parseInt(productId.replace(/\D/g, '')) || 0;
                const baseCode = `200${numericId.toString().padStart(8, '0')}`;
                return baseCode.substring(0, 12);
            default:
                throw new Error('نوع الباركود غير مدعوم');
        }
    }

    /**
     * تحويل النمط إلى Canvas
     */
    renderToCanvas(pattern, options = {}) {
        const {
            width = 300,
            height = 100,
            showText = true,
            text = '',
            fontSize = 12,
            margin = 10
        } = options;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = width;
        canvas.height = height;

        // خلفية بيضاء
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, width, height);

        // حساب عرض الخط
        const barcodeWidth = width - (margin * 2);
        const barWidth = barcodeWidth / pattern.length;
        const barcodeHeight = showText ? height - fontSize - 20 : height - (margin * 2);

        // رسم الباركود
        ctx.fillStyle = 'black';
        for (let i = 0; i < pattern.length; i++) {
            if (pattern[i] === '1') {
                const x = margin + (i * barWidth);
                ctx.fillRect(x, margin, barWidth, barcodeHeight);
            }
        }

        // إضافة النص
        if (showText && text) {
            ctx.fillStyle = 'black';
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, width / 2, height - 5);
        }

        return canvas;
    }

    /**
     * تحويل Canvas إلى صورة
     */
    canvasToImage(canvas, format = 'png') {
        return canvas.toDataURL(`image/${format}`);
    }

    /**
     * الحصول على قيمة الحرف
     */
    getCharValue(char) {
        if (char >= '0' && char <= '9') return char.charCodeAt(0) - 48;
        if (char >= 'A' && char <= 'Z') return char.charCodeAt(0) - 55;
        if (char === ' ') return 32;
        if (char === '-') return 45;
        if (char === '.') return 46;
        return 0;
    }

    /**
     * الحصول على الحرف من القيمة
     */
    getCharFromValue(value) {
        if (value >= 0 && value <= 9) return String.fromCharCode(value + 48);
        if (value >= 10 && value <= 35) return String.fromCharCode(value + 55);
        if (value === 32) return ' ';
        if (value === 45) return '-';
        if (value === 46) return '.';
        return '0';
    }

    /**
     * التحقق من صحة الباركود
     */
    validateBarcode(barcode, type = 'CODE128') {
        switch (type.toUpperCase()) {
            case 'CODE128':
                return /^[A-Z0-9\-\. ]+$/.test(barcode) && barcode.length <= 20;
            case 'EAN13':
                return /^\d{12,13}$/.test(barcode);
            default:
                return false;
        }
    }
}

// تصدير الكلاس
window.BarcodeGenerator = BarcodeGenerator;
