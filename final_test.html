<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - الإعدادات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-controls {
            padding: 20px;
            background: #e9ecef;
            border-bottom: 1px solid #ddd;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .status.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        #mainContent {
            min-height: 400px;
        }
        
        /* استيراد أنماط الإعدادات الأساسية */
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .tabs-container {
            border-bottom: 1px solid #ddd;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
        }
        
        .tab-button {
            padding: 12px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .page-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> اختبار نهائي - نظام الإعدادات</h1>
            <p>اختبار شامل لحل مشكلة قسم الإعدادات العامة الفارغ</p>
        </div>
        
        <div class="test-controls">
            <h3>أدوات الاختبار</h3>
            <button class="btn btn-primary" onclick="runFullTest()">
                <i class="fas fa-play"></i> تشغيل الاختبار الشامل
            </button>
            <button class="btn btn-success" onclick="loadSettings()">
                <i class="fas fa-download"></i> تحميل صفحة الإعدادات
            </button>
            <button class="btn btn-warning" onclick="testIndividualFunctions()">
                <i class="fas fa-wrench"></i> اختبار الدوال الفردية
            </button>
            <button class="btn btn-danger" onclick="clearStorage()">
                <i class="fas fa-trash"></i> مسح التخزين المحلي
            </button>
        </div>
        
        <div id="testResults"></div>
        
        <div id="mainContent">
            <div class="status info">
                <strong>جاهز للاختبار!</strong><br>
                اضغط على "تشغيل الاختبار الشامل" للبدء أو "تحميل صفحة الإعدادات" لرؤية النتيجة مباشرة.
            </div>
        </div>
    </div>

    <script src="settings.js"></script>
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            resultsDiv.appendChild(statusDiv);
            
            // التمرير إلى النتيجة الجديدة
            statusDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        async function runFullTest() {
            clearResults();
            showResult('<i class="fas fa-play"></i> <strong>بدء الاختبار الشامل...</strong>', 'info');
            
            let passedTests = 0;
            let totalTests = 0;
            
            // اختبار 1: وجود الدوال الأساسية
            totalTests++;
            try {
                if (typeof getDefaultSettings === 'function' && 
                    typeof loadCurrentSettings === 'function' && 
                    typeof populateSettingsForms === 'function') {
                    showResult('✅ <strong>اختبار 1:</strong> جميع الدوال الأساسية موجودة', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 1:</strong> بعض الدوال الأساسية مفقودة', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 1:</strong> خطأ في فحص الدوال: ' + error.message, 'error');
            }
            
            // اختبار 2: الإعدادات الافتراضية
            totalTests++;
            try {
                const defaultSettings = getDefaultSettings();
                if (defaultSettings && defaultSettings.general && defaultSettings.general.currency === 'EGP') {
                    showResult('✅ <strong>اختبار 2:</strong> الإعدادات الافتراضية صحيحة (العملة: جنيه مصري)', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 2:</strong> الإعدادات الافتراضية غير صحيحة', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 2:</strong> خطأ في الإعدادات الافتراضية: ' + error.message, 'error');
            }
            
            // اختبار 3: تحميل الإعدادات
            totalTests++;
            try {
                loadCurrentSettings();
                if (currentSettings && typeof currentSettings === 'object') {
                    showResult('✅ <strong>اختبار 3:</strong> تم تحميل الإعدادات بنجاح', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 3:</strong> فشل في تحميل الإعدادات', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 3:</strong> خطأ في تحميل الإعدادات: ' + error.message, 'error');
            }
            
            // اختبار 4: تحميل صفحة الإعدادات
            totalTests++;
            try {
                await loadSettings();
                
                // التحقق من وجود النماذج
                setTimeout(() => {
                    const generalForm = document.getElementById('generalSettingsForm');
                    if (generalForm) {
                        showResult('✅ <strong>اختبار 4:</strong> تم إنشاء صفحة الإعدادات بنجاح', 'success');
                        passedTests++;
                        
                        // اختبار 5: ملء النماذج
                        totalTests++;
                        setTimeout(() => {
                            const languageSelect = generalForm.querySelector('[name="language"]');
                            const currencySelect = generalForm.querySelector('[name="currency"]');
                            
                            if (languageSelect && languageSelect.value === 'ar' && 
                                currencySelect && currencySelect.value === 'EGP') {
                                showResult('✅ <strong>اختبار 5:</strong> تم ملء قسم الإعدادات العامة بشكل صحيح!', 'success');
                                passedTests++;
                            } else {
                                showResult('❌ <strong>اختبار 5:</strong> قسم الإعدادات العامة لم يتم ملؤه بشكل صحيح', 'error');
                            }
                            
                            // عرض النتيجة النهائية
                            setTimeout(() => {
                                const percentage = Math.round((passedTests / totalTests) * 100);
                                if (percentage >= 80) {
                                    showResult(`🎉 <strong>النتيجة النهائية:</strong> ${passedTests}/${totalTests} اختبارات نجحت (${percentage}%) - المشكلة محلولة!`, 'success');
                                } else {
                                    showResult(`⚠️ <strong>النتيجة النهائية:</strong> ${passedTests}/${totalTests} اختبارات نجحت (${percentage}%) - تحتاج إلى مراجعة`, 'error');
                                }
                            }, 500);
                        }, 300);
                    } else {
                        showResult('❌ <strong>اختبار 4:</strong> فشل في إنشاء صفحة الإعدادات', 'error');
                    }
                }, 200);
                
            } catch (error) {
                showResult('❌ <strong>اختبار 4:</strong> خطأ في تحميل صفحة الإعدادات: ' + error.message, 'error');
            }
        }
        
        function testIndividualFunctions() {
            clearResults();
            showResult('<i class="fas fa-wrench"></i> <strong>اختبار الدوال الفردية...</strong>', 'info');
            
            const functions = [
                'getDefaultSettings',
                'loadCurrentSettings', 
                'populateSettingsForms',
                'populateGeneralSettings',
                'populateCompanySettings',
                'populatePosSettings',
                'populateInvoiceSettings'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    showResult(`✅ ${funcName} - موجودة`, 'success');
                } else {
                    showResult(`❌ ${funcName} - مفقودة`, 'error');
                }
            });
        }
        
        function clearStorage() {
            localStorage.removeItem('technoflash_settings');
            localStorage.removeItem('technoflash_db');
            showResult('🗑️ تم مسح التخزين المحلي', 'info');
        }
    </script>
</body>
</html><!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - الإعدادات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-controls {
            padding: 20px;
            background: #e9ecef;
            border-bottom: 1px solid #ddd;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .status.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        #mainContent {
            min-height: 400px;
        }
        
        /* استيراد أنماط الإعدادات الأساسية */
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .tabs-container {
            border-bottom: 1px solid #ddd;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
        }
        
        .tab-button {
            padding: 12px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .page-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> اختبار نهائي - نظام الإعدادات</h1>
            <p>اختبار شامل لحل مشكلة قسم الإعدادات العامة الفارغ</p>
        </div>
        
        <div class="test-controls">
            <h3>أدوات الاختبار</h3>
            <button class="btn btn-primary" onclick="runFullTest()">
                <i class="fas fa-play"></i> تشغيل الاختبار الشامل
            </button>
            <button class="btn btn-success" onclick="loadSettings()">
                <i class="fas fa-download"></i> تحميل صفحة الإعدادات
            </button>
            <button class="btn btn-warning" onclick="testIndividualFunctions()">
                <i class="fas fa-wrench"></i> اختبار الدوال الفردية
            </button>
            <button class="btn btn-danger" onclick="clearStorage()">
                <i class="fas fa-trash"></i> مسح التخزين المحلي
            </button>
        </div>
        
        <div id="testResults"></div>
        
        <div id="mainContent">
            <div class="status info">
                <strong>جاهز للاختبار!</strong><br>
                اضغط على "تشغيل الاختبار الشامل" للبدء أو "تحميل صفحة الإعدادات" لرؤية النتيجة مباشرة.
            </div>
        </div>
    </div>

    <script src="settings.js"></script>
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            resultsDiv.appendChild(statusDiv);
            
            // التمرير إلى النتيجة الجديدة
            statusDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        async function runFullTest() {
            clearResults();
            showResult('<i class="fas fa-play"></i> <strong>بدء الاختبار الشامل...</strong>', 'info');
            
            let passedTests = 0;
            let totalTests = 0;
            
            // اختبار 1: وجود الدوال الأساسية
            totalTests++;
            try {
                if (typeof getDefaultSettings === 'function' && 
                    typeof loadCurrentSettings === 'function' && 
                    typeof populateSettingsForms === 'function') {
                    showResult('✅ <strong>اختبار 1:</strong> جميع الدوال الأساسية موجودة', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 1:</strong> بعض الدوال الأساسية مفقودة', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 1:</strong> خطأ في فحص الدوال: ' + error.message, 'error');
            }
            
            // اختبار 2: الإعدادات الافتراضية
            totalTests++;
            try {
                const defaultSettings = getDefaultSettings();
                if (defaultSettings && defaultSettings.general && defaultSettings.general.currency === 'EGP') {
                    showResult('✅ <strong>اختبار 2:</strong> الإعدادات الافتراضية صحيحة (العملة: جنيه مصري)', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 2:</strong> الإعدادات الافتراضية غير صحيحة', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 2:</strong> خطأ في الإعدادات الافتراضية: ' + error.message, 'error');
            }
            
            // اختبار 3: تحميل الإعدادات
            totalTests++;
            try {
                loadCurrentSettings();
                if (currentSettings && typeof currentSettings === 'object') {
                    showResult('✅ <strong>اختبار 3:</strong> تم تحميل الإعدادات بنجاح', 'success');
                    passedTests++;
                } else {
                    showResult('❌ <strong>اختبار 3:</strong> فشل في تحميل الإعدادات', 'error');
                }
            } catch (error) {
                showResult('❌ <strong>اختبار 3:</strong> خطأ في تحميل الإعدادات: ' + error.message, 'error');
            }
            
            // اختبار 4: تحميل صفحة الإعدادات
            totalTests++;
            try {
                await loadSettings();
                
                // التحقق من وجود النماذج
                setTimeout(() => {
                    const generalForm = document.getElementById('generalSettingsForm');
                    if (generalForm) {
                        showResult('✅ <strong>اختبار 4:</strong> تم إنشاء صفحة الإعدادات بنجاح', 'success');
                        passedTests++;
                        
                        // اختبار 5: ملء النماذج
                        totalTests++;
                        setTimeout(() => {
                            const languageSelect = generalForm.querySelector('[name="language"]');
                            const currencySelect = generalForm.querySelector('[name="currency"]');
                            
                            if (languageSelect && languageSelect.value === 'ar' && 
                                currencySelect && currencySelect.value === 'EGP') {
                                showResult('✅ <strong>اختبار 5:</strong> تم ملء قسم الإعدادات العامة بشكل صحيح!', 'success');
                                passedTests++;
                            } else {
                                showResult('❌ <strong>اختبار 5:</strong> قسم الإعدادات العامة لم يتم ملؤه بشكل صحيح', 'error');
                            }
                            
                            // عرض النتيجة النهائية
                            setTimeout(() => {
                                const percentage = Math.round((passedTests / totalTests) * 100);
                                if (percentage >= 80) {
                                    showResult(`🎉 <strong>النتيجة النهائية:</strong> ${passedTests}/${totalTests} اختبارات نجحت (${percentage}%) - المشكلة محلولة!`, 'success');
                                } else {
                                    showResult(`⚠️ <strong>النتيجة النهائية:</strong> ${passedTests}/${totalTests} اختبارات نجحت (${percentage}%) - تحتاج إلى مراجعة`, 'error');
                                }
                            }, 500);
                        }, 300);
                    } else {
                        showResult('❌ <strong>اختبار 4:</strong> فشل في إنشاء صفحة الإعدادات', 'error');
                    }
                }, 200);
                
            } catch (error) {
                showResult('❌ <strong>اختبار 4:</strong> خطأ في تحميل صفحة الإعدادات: ' + error.message, 'error');
            }
        }
        
        function testIndividualFunctions() {
            clearResults();
            showResult('<i class="fas fa-wrench"></i> <strong>اختبار الدوال الفردية...</strong>', 'info');
            
            const functions = [
                'getDefaultSettings',
                'loadCurrentSettings', 
                'populateSettingsForms',
                'populateGeneralSettings',
                'populateCompanySettings',
                'populatePosSettings',
                'populateInvoiceSettings'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    showResult(`✅ ${funcName} - موجودة`, 'success');
                } else {
                    showResult(`❌ ${funcName} - مفقودة`, 'error');
                }
            });
        }
        
        function clearStorage() {
            localStorage.removeItem('technoflash_settings');
            localStorage.removeItem('technoflash_db');
            showResult('🗑️ تم مسح التخزين المحلي', 'info');
        }
    </script>
</body>
</html>