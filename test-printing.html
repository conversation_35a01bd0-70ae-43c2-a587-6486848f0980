<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة - تكنوفلاش POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 8px;
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-print"></i> اختبار الطباعة</h1>
            <p>نظام تكنوفلاش POS - اختبار الطباعة الحرارية</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cog"></i> اختبار الطابعة</h3>
            <button class="btn btn-primary test-btn" onclick="runTest()">
                <i class="fas fa-play"></i> تشغيل الاختبار
            </button>
            <div id="testResult" class="result-area">انقر على "تشغيل الاختبار" لبدء الاختبار</div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-receipt"></i> اختبار فاتورة تجريبية</h3>
            <button class="btn btn-success test-btn" onclick="testInvoice()">
                <i class="fas fa-file-invoice"></i> طباعة فاتورة تجريبية
            </button>
            <div id="invoiceResult" class="result-area">انقر لطباعة فاتورة تجريبية</div>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    
    <script>
        // تهيئة قاعدة البيانات
        const db = new Database();
        
        // محاكاة app object
        window.app = {
            showAlert: function(title, message) {
                alert(title + ': ' + message);
            },
            showNotification: function(message, type) {
                console.log(`${type}: ${message}`);
            }
        };

        async function runTest() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.textContent = 'جاري تشغيل الاختبار...';
            
            try {
                if (typeof testThermalPrinter === 'undefined') {
                    throw new Error('وظيفة testThermalPrinter غير متوفرة');
                }
                
                await testThermalPrinter();
                resultDiv.textContent = '✅ تم تشغيل الاختبار بنجاح!\nتحقق من الطابعة للتأكد من الطباعة.';
            } catch (error) {
                resultDiv.textContent = '❌ فشل الاختبار:\n' + error.message;
            }
        }

        function testInvoice() {
            const resultDiv = document.getElementById('invoiceResult');
            resultDiv.textContent = 'جاري إنشاء فاتورة تجريبية...';
            
            try {
                // إنشاء بيانات فاتورة تجريبية
                const testSale = {
                    id: 'TEST-' + Date.now(),
                    invoiceNumber: 'INV-TEST-001',
                    date: new Date().toISOString(),
                    customerId: 'guest',
                    items: [
                        {
                            productId: 'test1',
                            name: 'منتج تجريبي 1',
                            quantity: 2,
                            price: 25.50,
                            total: 51.00
                        },
                        {
                            productId: 'test2',
                            name: 'منتج تجريبي 2',
                            quantity: 1,
                            price: 15.00,
                            total: 15.00
                        }
                    ],
                    subtotal: 66.00,
                    tax: 6.60,
                    discount: 5.00,
                    total: 67.60,
                    paymentMethod: 'cash',
                    amountPaid: 70.00
                };
                
                // محاكاة قاعدة البيانات
                window.db = {
                    getSale: () => testSale,
                    getCustomer: () => null,
                    getSettings: () => ({
                        company: {
                            companyName: 'متجر تكنوفلاش التجريبي',
                            phone: '0*********0',
                            address: 'القاهرة، مصر',
                            taxNumber: '*********'
                        }
                    }),
                    getProduct: (id) => ({
                        name: id === 'test1' ? 'منتج تجريبي 1' : 'منتج تجريبي 2'
                    })
                };
                
                if (typeof printThermalInvoice === 'undefined') {
                    throw new Error('وظيفة printThermalInvoice غير متوفرة');
                }
                
                printThermalInvoice(testSale);
                resultDiv.textContent = '✅ تم إرسال الفاتورة التجريبية للطباعة!';
                
            } catch (error) {
                resultDiv.textContent = '❌ فشل في طباعة الفاتورة:\n' + error.message;
            }
        }

        // تحقق من تحميل الملفات
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة اختبار الطباعة');
            
            const checks = [
                { name: 'testThermalPrinter', available: typeof testThermalPrinter !== 'undefined' },
                { name: 'printThermalInvoice', available: typeof printThermalInvoice !== 'undefined' },
                { name: 'ESC_POS_COMMANDS', available: typeof ESC_POS_COMMANDS !== 'undefined' },
                { name: 'Database', available: typeof Database !== 'undefined' }
            ];
            
            console.log('📋 فحص الوظائف المطلوبة:');
            checks.forEach(check => {
                console.log(`${check.available ? '✅' : '❌'} ${check.name}: ${check.available ? 'متوفر' : 'غير متوفر'}`);
            });
        });
    </script>
</body>
</html>
