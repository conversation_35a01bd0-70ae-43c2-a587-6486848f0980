/**
 * لوحة المعلومات الرئيسية - تكنوفلاش
 */

// مستمع أحداث تحديث الإعدادات
window.addEventListener('settingsUpdated', function(event) {
    const settings = event.detail;
    console.log('تم استلام تحديث الإعدادات في لوحة المعلومات:', settings);

    // تحديث الوضع الليلي
    if (settings.general && typeof settings.general.darkMode !== 'undefined') {
        if (settings.general.darkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        console.log('تم تحديث الوضع الليلي:', settings.general.darkMode);
    }

    // تحديث العملة في الإحصائيات فقط إذا كنا في صفحة لوحة المعلومات
    if (settings.general && settings.general.currency && document.getElementById('dashboardContainer')) {
        updateDashboardCurrency(settings.general.currency);
    }

    // تحديث نوع الأرقام فقط إذا كنا في صفحة لوحة المعلومات
    if (settings.general && settings.general.numberType && document.getElementById('dashboardContainer')) {
        updateDashboardNumbers(settings.general.numberType);
    }

    // تحديث بيانات الشركة في الداشبورد
    if (settings.company && document.getElementById('dashboardContainer')) {
        updateDashboardCompanyInfo(settings.company);
    }

    // تحديث إعدادات نقطة البيع في الداشبورد
    if (settings.pos && document.getElementById('dashboardContainer')) {
        updateDashboardPOSSettings(settings.pos);
    }
});

/**
 * تحميل لوحة المعلومات
 */
async function loadDashboard() {
    const mainContent = document.getElementById('mainContent');
    
    // إنشاء محتوى لوحة المعلومات
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="stats-grid" id="statsGrid">
            <!-- سيتم تحميل الإحصائيات هنا -->
        </div>

        <!-- الرسوم البيانية والتقارير -->
        <div class="dashboard-content">
            <div class="row">
                <div class="col-md-8">
                    <!-- مبيعات اليوم -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                مبيعات اليوم
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="todaySalesChart" class="chart-container">
                                <!-- الرسم البياني سيتم إنشاؤه هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- أحدث المبيعات -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-shopping-cart"></i>
                                أحدث المبيعات
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table" id="recentSalesTable">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم تحميل البيانات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- تنبيهات المخزون -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                تنبيهات المخزون
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="stockAlerts">
                                <!-- التنبيهات ستظهر هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- أفضل المنتجات مبيعاً -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-star"></i>
                                أفضل المنتجات مبيعاً
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="topProducts">
                                <!-- أفضل المنتجات ستظهر هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- العملاء المدينون -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-money-bill-wave"></i>
                                العملاء المدينون
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="debtorCustomers">
                                <!-- العملاء المدينون سيظهرون هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تحميل البيانات
    await loadDashboardData();

    // تحميل وتطبيق الإعدادات
    try {
        const settings = JSON.parse(localStorage.getItem('technoflash_settings') || '{}');

        // تطبيق الإعدادات العامة
        if (settings.general) {
            if (settings.general.darkMode) {
                document.body.classList.add('dark-mode');
            }
            if (settings.general.numberType) {
                document.documentElement.setAttribute('data-number-type', settings.general.numberType);
            }
            if (settings.general.currency) {
                document.documentElement.setAttribute('data-currency', settings.general.currency);
            }
        }

        // تطبيق إعدادات نقطة البيع
        if (settings.pos) {
            updateDashboardPOSSettings(settings.pos);
        }

        // تطبيق بيانات الشركة
        if (settings.company) {
            updateDashboardCompanyInfo(settings.company);
        }
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات في لوحة المعلومات:', error);
    }
}

/**
 * تحميل بيانات لوحة المعلومات
 */
async function loadDashboardData() {
    try {
        // تحميل الإحصائيات
        await loadDashboardStats();
        
        // تحميل أحدث المبيعات
        await loadRecentSales();
        
        // تحميل تنبيهات المخزون
        await loadStockAlerts();
        
        // تحميل أفضل المنتجات
        await loadTopProducts();
        
        // تحميل العملاء المدينين
        await loadDebtorCustomers();
        
        // تحميل الرسم البياني
        await loadTodaySalesChart();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة المعلومات:', error);
        if (typeof app !== 'undefined' && app.showAlert) {
            app.showAlert('خطأ', 'حدث خطأ في تحميل بيانات لوحة المعلومات');
        } else {
            alert('حدث خطأ في تحميل بيانات لوحة المعلومات');
        }
    }
}

/**
 * تحميل الإحصائيات السريعة
 */
async function loadDashboardStats() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، استخدام بيانات افتراضية');
        // استخدام بيانات افتراضية
        displayDefaultStats();
        return;
    }

    const products = db.getProducts() || [];
    const customers = db.getCustomers() || [];
    const sales = db.getSales() || [];
    const suppliers = db.getSuppliers() || [];
    
    // حساب الإحصائيات
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    const todaySales = sales.filter(sale => 
        sale.date.split('T')[0] === todayStr
    );
    
    const totalSalesToday = todaySales.reduce((sum, sale) => sum + sale.total, 0);
    const totalProducts = products.length;
    const lowStockProducts = products.filter(product => 
        product.quantity <= product.minStock
    ).length;
    const totalCustomers = customers.length - 1; // استثناء العميل الافتراضي
    
    // إنشاء بطاقات الإحصائيات
    const statsGrid = document.getElementById('statsGrid');
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value number currency">${db.toArabicNumerals(db.formatCurrency(totalSalesToday))}</div>
            <div class="stat-label">مبيعات اليوم</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-value number">${db.toArabicNumerals(totalProducts)}</div>
            <div class="stat-label">إجمالي المنتجات</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-value number">${db.toArabicNumerals(lowStockProducts)}</div>
            <div class="stat-label">منتجات منخفضة المخزون</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon info">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value number">${db.toArabicNumerals(totalCustomers)}</div>
            <div class="stat-label">إجمالي العملاء</div>
        </div>
    `;
}

/**
 * عرض إحصائيات افتراضية عند عدم وجود قاعدة البيانات
 */
function displayDefaultStats() {
    const statsContainer = document.getElementById('statsContainer');
    if (statsContainer) {
        statsContainer.innerHTML = `
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value number">0</div>
                <div class="stat-label">مبيعات اليوم</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-value number">0</div>
                <div class="stat-label">إجمالي المنتجات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value number">0</div>
                <div class="stat-label">منتجات قليلة المخزون</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value number">0</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>
        `;
    }
    console.log('تم عرض الإحصائيات الافتراضية');
}

/**
 * تحميل أحدث المبيعات
 */
async function loadRecentSales() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، عرض رسالة افتراضية للمبيعات');
        displayDefaultRecentSales();
        return;
    }

    const sales = db.getSales() || [];
    const customers = db.getCustomers() || [];
    
    // ترتيب المبيعات حسب التاريخ (الأحدث أولاً)
    const recentSales = sales
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 5);
    
    const tbody = document.querySelector('#recentSalesTable tbody');
    
    if (recentSales.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">لا توجد مبيعات حتى الآن</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = recentSales.map(sale => {
        const customer = customers.find(c => c.id === sale.customerId);
        const customerName = customer ? customer.name : 'غير محدد';
        const statusClass = sale.paymentMethod === 'cash' ? 'success' : 'warning';
        const statusText = sale.paymentMethod === 'cash' ? 'مدفوع' : 'آجل';
        
        return `
            <tr>
                <td>${sale.invoiceNumber || sale.id.substr(-6)}</td>
                <td>${customerName}</td>
                <td>${db.formatCurrency(sale.total)}</td>
                <td>${db.formatDate(sale.date, true)}</td>
                <td><span class="badge badge-${statusClass}">${statusText}</span></td>
            </tr>
        `;
    }).join('');
}

/**
 * عرض رسالة افتراضية للمبيعات الحديثة
 */
function displayDefaultRecentSales() {
    const tbody = document.querySelector('#recentSalesTable tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">لا توجد مبيعات حتى الآن</td>
            </tr>
        `;
    }
    console.log('تم عرض رسالة افتراضية للمبيعات الحديثة');
}

/**
 * تحميل تنبيهات المخزون
 */
async function loadStockAlerts() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، عرض رسالة افتراضية لتنبيهات المخزون');
        displayDefaultStockAlerts();
        return;
    }

    const products = db.getProducts() || [];
    const lowStockProducts = products.filter(product => 
        product.quantity <= product.minStock
    );
    
    const stockAlertsContainer = document.getElementById('stockAlerts');
    
    if (lowStockProducts.length === 0) {
        stockAlertsContainer.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                جميع المنتجات متوفرة في المخزون
            </div>
        `;
        return;
    }
    
    stockAlertsContainer.innerHTML = lowStockProducts.map(product => `
        <div class="alert alert-warning">
            <div class="alert-content">
                <strong>${product.name}</strong>
                <br>
                <small>الكمية المتبقية: ${db.toArabicNumerals(product.quantity)}</small>
                <br>
                <small>الحد الأدنى: ${db.toArabicNumerals(product.minStock)}</small>
            </div>
        </div>
    `).join('');
}

/**
 * عرض رسالة افتراضية لتنبيهات المخزون
 */
function displayDefaultStockAlerts() {
    const tbody = document.querySelector('#stockAlertsTable tbody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center">لا توجد تنبيهات مخزون</td>
            </tr>
        `;
    }
    console.log('تم عرض رسالة افتراضية لتنبيهات المخزون');
}

/**
 * تحميل أفضل المنتجات مبيعاً
 */
async function loadTopProducts() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، عرض رسالة افتراضية لأفضل المنتجات');
        displayDefaultTopProducts();
        return;
    }

    const sales = db.getSales() || [];
    const products = db.getProducts() || [];
    
    // حساب كمية المبيعات لكل منتج
    const productSales = {};
    
    sales.forEach(sale => {
        sale.items.forEach(item => {
            if (productSales[item.productId]) {
                productSales[item.productId] += item.quantity;
            } else {
                productSales[item.productId] = item.quantity;
            }
        });
    });
    
    // ترتيب المنتجات حسب المبيعات
    const topProducts = Object.entries(productSales)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([productId, quantity]) => {
            const product = products.find(p => p.id === productId);
            return { product, quantity };
        })
        .filter(item => item.product);
    
    const topProductsContainer = document.getElementById('topProducts');
    
    if (topProducts.length === 0) {
        topProductsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                لا توجد مبيعات حتى الآن
            </div>
        `;
        return;
    }
    
    topProductsContainer.innerHTML = topProducts.map((item, index) => `
        <div class="top-product-item">
            <div class="product-rank">${db.toArabicNumerals(index + 1)}</div>
            <div class="product-info">
                <strong>${item.product.name}</strong>
                <br>
                <small>تم بيع ${db.toArabicNumerals(item.quantity)} قطعة</small>
            </div>
        </div>
    `).join('');
}

/**
 * عرض رسالة افتراضية لأفضل المنتجات
 */
function displayDefaultTopProducts() {
    const topProductsContainer = document.getElementById('topProductsContainer');
    if (topProductsContainer) {
        topProductsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                لا توجد مبيعات حتى الآن
            </div>
        `;
    }
    console.log('تم عرض رسالة افتراضية لأفضل المنتجات');
}

/**
 * تحميل العملاء المدينين
 */
async function loadDebtorCustomers() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، عرض رسالة افتراضية للعملاء المدينين');
        displayDefaultDebtorCustomers();
        return;
    }

    const customers = db.getCustomers() || [];
    const debtorCustomers = customers
        .filter(customer => customer.balance > 0 && customer.id !== 'guest')
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5);
    
    const debtorCustomersContainer = document.getElementById('debtorCustomers');
    
    if (debtorCustomers.length === 0) {
        debtorCustomersContainer.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                لا توجد ديون مستحقة
            </div>
        `;
        return;
    }
    
    debtorCustomersContainer.innerHTML = debtorCustomers.map(customer => `
        <div class="debtor-item">
            <div class="customer-info">
                <strong>${customer.name}</strong>
                <br>
                <small>${customer.phone || 'لا يوجد رقم هاتف'}</small>
            </div>
            <div class="debt-amount">
                ${db.formatCurrency(customer.balance)}
            </div>
        </div>
    `).join('');
}

/**
 * عرض رسالة افتراضية للعملاء المدينين
 */
function displayDefaultDebtorCustomers() {
    const debtorCustomersContainer = document.getElementById('debtorCustomersContainer');
    if (debtorCustomersContainer) {
        debtorCustomersContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                لا يوجد عملاء مدينون
            </div>
        `;
    }
    console.log('تم عرض رسالة افتراضية للعملاء المدينين');
}

/**
 * تحميل الرسم البياني لمبيعات اليوم
 */
async function loadTodaySalesChart() {
    // التحقق من وجود db
    if (typeof db === 'undefined') {
        console.warn('متغير db غير معرف، عرض رسالة افتراضية للرسم البياني');
        displayDefaultChart();
        return;
    }

    const sales = db.getSales() || [];
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    const todaySales = sales.filter(sale => 
        sale.date.split('T')[0] === todayStr
    );
    
    // تجميع المبيعات حسب الساعة
    const hourlyData = {};
    for (let i = 0; i < 24; i++) {
        hourlyData[i] = 0;
    }
    
    todaySales.forEach(sale => {
        const hour = new Date(sale.date).getHours();
        hourlyData[hour] += sale.total;
    });
    
    // إنشاء رسم بياني بسيط
    const chartContainer = document.getElementById('todaySalesChart');
    const maxValue = Math.max(...Object.values(hourlyData));
    
    if (maxValue === 0) {
        chartContainer.innerHTML = `
            <div class="no-data">
                <i class="fas fa-chart-line"></i>
                <p>لا توجد مبيعات اليوم</p>
            </div>
        `;
        return;
    }
    
    chartContainer.innerHTML = `
        <div class="simple-chart">
            ${Object.entries(hourlyData).map(([hour, value]) => {
                const height = (value / maxValue) * 100;
                return `
                    <div class="chart-bar" style="height: ${height}%" title="${db.toArabicNumerals(hour)}:00 - ${db.formatCurrency(value)}">
                        <div class="bar-value">${db.toArabicNumerals(Math.round(value))}</div>
                    </div>
                `;
            }).join('')}
        </div>
        <div class="chart-labels">
            ${Object.keys(hourlyData).map(hour => 
                `<span>${db.toArabicNumerals(hour)}</span>`
            ).join('')}
        </div>
    `;
}

/**
 * تحديث لوحة المعلومات
 */
async function refreshDashboard() {
    if (typeof app !== 'undefined' && app.showLoading) {
        app.showLoading();
    }

    try {
        await loadDashboardData();
        if (typeof app !== 'undefined' && app.showNotification) {
            app.showNotification('تم تحديث لوحة المعلومات بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في تحديث لوحة المعلومات:', error);
        if (typeof app !== 'undefined' && app.showAlert) {
            app.showAlert('خطأ', 'حدث خطأ في تحديث لوحة المعلومات');
        } else {
            alert('حدث خطأ في تحديث لوحة المعلومات');
        }
    }

    if (typeof app !== 'undefined' && app.hideLoading) {
        app.hideLoading();
    }
}

/**
 * تحديث العملة في لوحة المعلومات
 */
function updateDashboardCurrency(currency) {
    try {
        // تحديث العملة في العنصر الجذر
        document.documentElement.setAttribute('data-currency', currency);

        // تحديث جميع عناصر العملة في لوحة المعلومات
        const currencyElements = document.querySelectorAll('.currency, [data-currency]');
        currencyElements.forEach(element => {
            element.setAttribute('data-currency', currency);
        });

        // إعادة تحميل الإحصائيات لتطبيق العملة الجديدة
        if (typeof loadStats === 'function') {
            loadStats();
        }

        // إعادة تحميل بيانات لوحة المعلومات فقط إذا كنا في صفحة لوحة المعلومات
        if (typeof loadDashboardData === 'function' && document.getElementById('dashboardContainer')) {
            loadDashboardData();
        }

        console.log('تم تحديث العملة في لوحة المعلومات:', currency);
    } catch (error) {
        console.error('خطأ في تحديث العملة:', error);
    }
}

/**
 * تحديث نوع الأرقام في لوحة المعلومات
 */
function updateDashboardNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // تحديث جميع عناصر الأرقام
        const numberElements = document.querySelectorAll('.number, .stats-card h3, .amount');
        numberElements.forEach(element => {
            element.classList.add('number');
        });

        // إعادة تحميل الإحصائيات لتطبيق نوع الأرقام الجديد
        if (typeof loadStats === 'function') {
            loadStats();
        }

        // إعادة تحميل بيانات لوحة المعلومات فقط إذا كنا في صفحة لوحة المعلومات
        if (typeof loadDashboardData === 'function' && document.getElementById('dashboardContainer')) {
            loadDashboardData();
        }

        console.log('تم تحديث نوع الأرقام في لوحة المعلومات:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام:', error);
    }
}

/**
 * عرض رسالة افتراضية للرسم البياني
 */
function displayDefaultChart() {
    const chartContainer = document.getElementById('salesChart');
    if (chartContainer) {
        chartContainer.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-chart-bar"></i>
                <p>لا توجد بيانات لعرض الرسم البياني</p>
            </div>
        `;
    }
    console.log('تم عرض رسالة افتراضية للرسم البياني');
}

/**
 * تحديث بيانات الشركة في لوحة المعلومات
 */
function updateDashboardCompanyInfo(companySettings) {
    try {
        console.log('تحديث بيانات الشركة في لوحة المعلومات:', companySettings);

        // تحديث اسم الشركة في العناصر
        const companyNameElements = document.querySelectorAll('.company-name');
        companyNameElements.forEach(element => {
            element.textContent = companySettings.companyName || 'تكنوفلاش';
        });

        // تحديث أي عناصر أخرى تحتوي على بيانات الشركة في الداشبورد
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage && companySettings.companyName) {
            welcomeMessage.innerHTML = `مرحباً بك في نظام ${companySettings.companyName}`;
        }

        console.log('تم تحديث بيانات الشركة في لوحة المعلومات');
    } catch (error) {
        console.error('خطأ في تحديث بيانات الشركة في لوحة المعلومات:', error);
    }
}

/**
 * تحديث إعدادات نقطة البيع في لوحة المعلومات
 */
function updateDashboardPOSSettings(posSettings) {
    try {
        console.log('تحديث إعدادات نقطة البيع في لوحة المعلومات:', posSettings);

        // حفظ إعدادات نقطة البيع في متغير عام
        window.posSettings = posSettings;

        // تحديث أي عناصر في الداشبورد تعتمد على إعدادات نقطة البيع
        // مثل عرض تنبيهات المخزون المنخفض
        if (typeof posSettings.lowStockAlert !== 'undefined') {
            window.lowStockAlertEnabled = posSettings.lowStockAlert;

            // إعادة تحميل تنبيهات المخزون إذا كانت الدالة متاحة
            if (typeof loadLowStockAlerts === 'function') {
                loadLowStockAlerts();
            }
        }

        // تحديث حد المخزون المنخفض
        if (posSettings.lowStockThreshold) {
            window.lowStockThreshold = posSettings.lowStockThreshold;
        }

        // إعادة تحميل بيانات الداشبورد لتطبيق الإعدادات الجديدة
        if (typeof loadDashboardData === 'function') {
            setTimeout(() => {
                loadDashboardData();
            }, 100);
        }

        console.log('تم تحديث إعدادات نقطة البيع في لوحة المعلومات');
    } catch (error) {
        console.error('خطأ في تحديث إعدادات نقطة البيع في لوحة المعلومات:', error);
    }
}
