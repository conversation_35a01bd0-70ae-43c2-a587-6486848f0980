# دليل التثبيت - تكنوفلاش

## التشغيل كتطبيق ويب

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- دعم JavaScript و LocalStorage

### خطوات التشغيل
1. قم بتحميل جميع ملفات المشروع
2. افتح ملف `index.html` في المتصفح
3. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

---

## التحويل إلى تطبيق Electron

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التثبيت

#### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
https://nodejs.org/

#### 2. تثبيت التبعيات
افتح Terminal أو Command Prompt في مجلد المشروع وقم بتشغيل:

```bash
npm install
```

أو إذا كنت تستخدم yarn:

```bash
yarn install
```

#### 3. تشغيل التطبيق في وضع التطوير
```bash
npm start
```

أو:

```bash
yarn start
```

#### 4. بناء التطبيق للتوزيع

##### لنظام Windows:
```bash
npm run build-win
```

##### لنظام macOS:
```bash
npm run build-mac
```

##### لنظام Linux:
```bash
npm run build-linux
```

##### لجميع الأنظمة:
```bash
npm run build
```

### ملفات التوزيع
ستجد ملفات التوزيع في مجلد `dist/` بعد اكتمال عملية البناء.

---

## إعداد قاعدة البيانات

النظام يستخدم LocalStorage لتخزين البيانات محلياً. لا حاجة لإعداد قاعدة بيانات خارجية.

### البيانات الافتراضية
عند التشغيل الأول، سيتم إنشاء:
- حساب المدير الافتراضي
- بيانات تجريبية للمنتجات والعملاء
- إعدادات النظام الأساسية

---

## التخصيص

### تغيير بيانات الشركة
1. انتقل إلى الإعدادات > بيانات الشركة
2. قم بتعديل المعلومات المطلوبة
3. احفظ التغييرات

### تخصيص الألوان والثيم
يمكنك تعديل ملف `style.css` لتغيير:
- الألوان الأساسية
- الخطوط
- التخطيط العام

### إضافة لغات جديدة
لإضافة دعم لغات أخرى:
1. أنشئ ملف ترجمة جديد
2. قم بتعديل ملف `app.js` لدعم اللغة الجديدة
3. أضف خيار اللغة في الإعدادات

---

## النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية
1. انتقل إلى صفحة النسخ الاحتياطي
2. انقر على "إنشاء نسخة احتياطية"
3. احفظ الملف في مكان آمن

### استعادة البيانات
1. انتقل إلى صفحة النسخ الاحتياطي
2. انقر على "استعادة البيانات"
3. اختر ملف النسخة الاحتياطية
4. أكد عملية الاستعادة

---

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### التطبيق لا يحفظ البيانات
- تأكد من أن المتصفح يدعم LocalStorage
- تحقق من عدم تشغيل المتصفح في الوضع الخاص
- امسح cache المتصفح وأعد المحاولة

#### الخطوط العربية لا تظهر بشكل صحيح
- تأكد من اتصال الإنترنت لتحميل خط Cairo
- تحقق من دعم المتصفح للخطوط العربية

#### مشاكل في الطباعة
- تأكد من إعدادات الطابعة
- جرب طابعات مختلفة
- تحقق من إعدادات المتصفح للطباعة

### الحصول على المساعدة
إذا واجهت مشاكل أخرى:
1. تحقق من وحدة التحكم في المتصفح للأخطاء
2. راجع ملف README.md
3. تواصل مع الدعم الفني

---

## الأمان

### حماية البيانات
- استخدم كلمات مرور قوية
- قم بإنشاء نسخ احتياطية دورية
- لا تشارك بيانات الدخول

### تحديثات الأمان
- تأكد من تحديث المتصفح باستمرار
- راقب التحديثات الأمنية للنظام
- قم بفحص الملفات دورياً

---

## الدعم الفني

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://technoflash.com/support
- الهاتف: +966-XX-XXX-XXXX

---

**تكنوفلاش** - نظام نقطة البيع العربي الشامل
