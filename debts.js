/**
 * إدارة الديون والمدفوعات - تكنوفلاش
 */

let debtsData = [];
let paymentsData = [];

/**
 * تحميل صفحة الديون والمدفوعات
 */
async function loadDebts() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-money-bill-wave"></i> إدارة الديون والمدفوعات</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddPaymentModal()">
                    <i class="fas fa-plus"></i> إضافة دفعة
                </button>
                <button class="btn btn-info" onclick="exportDebts()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-button active" onclick="switchTab('customer-debts')">
                    <i class="fas fa-users"></i> ديون العملاء
                </button>
                <button class="tab-button" onclick="switchTab('supplier-debts')">
                    <i class="fas fa-truck"></i> ديون الموردين
                </button>
                <button class="tab-button" onclick="switchTab('payments-history')">
                    <i class="fas fa-history"></i> سجل المدفوعات
                </button>
            </div>
        </div>

        <!-- إحصائيات الديون -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value" id="totalCustomerDebts">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي ديون العملاء</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-value" id="totalSupplierDebts">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي ديون الموردين</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-value" id="todayPayments">٠.٠٠ ر.س</div>
                <div class="stat-label">مدفوعات اليوم</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="stat-value" id="monthPayments">٠.٠٠ ر.س</div>
                <div class="stat-label">مدفوعات الشهر</div>
            </div>
        </div>

        <!-- محتوى التبويبات -->
        <div class="tab-content">
            <!-- تبويب ديون العملاء -->
            <div id="customer-debts" class="tab-pane active">
                <div class="card">
                    <div class="card-header">
                        <h3>ديون العملاء</h3>
                        <div class="search-bar">
                            <input type="text" class="search-input" id="customerDebtSearch" 
                                   placeholder="البحث في العملاء...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table" id="customerDebtsTable">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الهاتف</th>
                                        <th>إجمالي المبيعات</th>
                                        <th>إجمالي المدفوع</th>
                                        <th>الرصيد المتبقي</th>
                                        <th>آخر دفعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تحميل البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب ديون الموردين -->
            <div id="supplier-debts" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>ديون الموردين</h3>
                        <div class="search-bar">
                            <input type="text" class="search-input" id="supplierDebtSearch" 
                                   placeholder="البحث في الموردين...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table" id="supplierDebtsTable">
                                <thead>
                                    <tr>
                                        <th>المورد</th>
                                        <th>الهاتف</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>إجمالي المدفوع</th>
                                        <th>الرصيد المتبقي</th>
                                        <th>آخر دفعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تحميل البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب سجل المدفوعات -->
            <div id="payments-history" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>سجل المدفوعات</h3>
                        <div class="filters-row">
                            <div class="search-bar">
                                <input type="text" class="search-input" id="paymentSearch" 
                                       placeholder="البحث في المدفوعات...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                            <select class="form-control" id="paymentTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="customer">دفعة من عميل</option>
                                <option value="supplier">دفعة لمورد</option>
                            </select>
                            <input type="date" class="form-control" id="paymentDateFilter">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table" id="paymentsTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>العميل/المورد</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تحميل البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة دفعة -->
        <div id="paymentModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="paymentModalTitle">إضافة دفعة جديدة</h3>
                    <button class="modal-close" onclick="app.hideModal('paymentModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <div class="form-group">
                            <label>نوع الدفعة *</label>
                            <select class="form-control" name="type" required onchange="updatePaymentOptions()">
                                <option value="">اختر نوع الدفعة</option>
                                <option value="customer">دفعة من عميل</option>
                                <option value="supplier">دفعة لمورد</option>
                            </select>
                        </div>

                        <div class="form-group" id="customerGroup" style="display: none;">
                            <label>العميل *</label>
                            <select class="form-control" name="customerId">
                                <option value="">اختر العميل</option>
                            </select>
                        </div>

                        <div class="form-group" id="supplierGroup" style="display: none;">
                            <label>المورد *</label>
                            <select class="form-control" name="supplierId">
                                <option value="">اختر المورد</option>
                            </select>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>المبلغ المدفوع *</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="amount"
                                           step="0.01" min="0.01" required
                                           placeholder="أدخل المبلغ المدفوع"
                                           oninput="formatAmountInput(this)">
                                    <div class="input-group-append">
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                </div>
                                <small class="form-text text-muted" id="remainingBalance" style="display: none;">
                                    الرصيد المتبقي: <span id="remainingAmount"></span>
                                </small>
                                <div class="quick-amount-buttons" id="quickAmountButtons" style="display: none; margin-top: 8px;">
                                    <small class="form-text text-muted">مبالغ سريعة:</small>
                                    <div class="btn-group btn-group-sm" style="margin-top: 4px;">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickAmount(100)">100</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickAmount(500)">500</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickAmount(1000)">1000</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setFullAmount()">الكامل</button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>طريقة الدفع *</label>
                                <select class="form-control" name="paymentMethod" required>
                                    <option value="cash">نقداً</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="installment">قسط</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>التاريخ *</label>
                            <input type="date" class="form-control" name="date" required>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الدفعة
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('paymentModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل الديون -->
        <div id="debtDetailsModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3 id="debtDetailsTitle">تفاصيل الديون</h3>
                    <button class="modal-close" onclick="app.hideModal('debtDetailsModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="debtDetailsContent">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeDebtsPage();
}

/**
 * تهيئة صفحة الديون
 */
async function initializeDebtsPage() {
    // تحميل البيانات
    loadDebtsData();
    
    // تحميل خيارات العملاء والموردين
    loadCustomersAndSuppliersOptions();
    
    // إعداد مستمعي الأحداث
    setupDebtsEventListeners();
    
    // عرض البيانات والإحصائيات
    displayCustomerDebts();
    updateDebtsStats();
    
    // تعيين التاريخ الحالي
    document.querySelector('[name="date"]').value = new Date().toISOString().split('T')[0];
}

/**
 * تحميل بيانات الديون
 */
function loadDebtsData() {
    debtsData = calculateDebts();
    paymentsData = db.getPayments();
}

/**
 * حساب الديون
 */
function calculateDebts() {
    const customers = db.getCustomers();
    const suppliers = db.getSuppliers();
    const sales = db.getSales();
    const purchases = db.getPurchases();
    const payments = db.getPayments();
    
    // حساب ديون العملاء
    const customerDebts = customers.map(customer => {
        const customerSales = sales.filter(sale => sale.customerId === customer.id);
        const customerPayments = payments.filter(payment =>
            payment.type === 'customer' && payment.customerId === customer.id
        );

        // حساب إجمالي المبيعات والمبلغ المدفوع من المبيعات
        const totalSales = customerSales.reduce((sum, sale) => sum + sale.total, 0);
        const paidFromSales = customerSales.reduce((sum, sale) => sum + (sale.paidAmount || 0), 0);

        // حساب إجمالي المدفوعات الإضافية
        const additionalPayments = customerPayments.reduce((sum, payment) => sum + payment.amount, 0);

        // إجمالي المدفوع = المدفوع من المبيعات + المدفوعات الإضافية
        const totalPaid = paidFromSales + additionalPayments;
        const balance = totalSales - totalPaid;

        const lastPayment = customerPayments.length > 0 ?
            customerPayments.sort((a, b) => new Date(b.date) - new Date(a.date))[0] : null;

        return {
            type: 'customer',
            id: customer.id,
            name: customer.name,
            phone: customer.phone,
            totalAmount: totalSales,
            paidFromSales: paidFromSales,
            additionalPayments: additionalPayments,
            totalPaid: totalPaid,
            balance: Math.max(0, balance), // تأكد من عدم وجود رصيد سالب
            lastPayment: lastPayment
        };
    }).filter(debt => debt.balance > 0);
    
    // حساب ديون الموردين
    const supplierDebts = suppliers.map(supplier => {
        const supplierPurchases = purchases.filter(purchase => purchase.supplierId === supplier.id);
        const supplierPayments = payments.filter(payment =>
            payment.type === 'supplier' && payment.supplierId === supplier.id
        );

        // حساب إجمالي المشتريات والمبلغ المدفوع من المشتريات
        const totalPurchases = supplierPurchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const paidFromPurchases = supplierPurchases.reduce((sum, purchase) => sum + (purchase.paidAmount || 0), 0);

        // حساب إجمالي المدفوعات الإضافية
        const additionalPayments = supplierPayments.reduce((sum, payment) => sum + payment.amount, 0);

        // إجمالي المدفوع = المدفوع من المشتريات + المدفوعات الإضافية
        const totalPaid = paidFromPurchases + additionalPayments;
        const balance = totalPurchases - totalPaid;

        const lastPayment = supplierPayments.length > 0 ?
            supplierPayments.sort((a, b) => new Date(b.date) - new Date(a.date))[0] : null;

        return {
            type: 'supplier',
            id: supplier.id,
            name: supplier.name,
            phone: supplier.phone,
            totalAmount: totalPurchases,
            paidFromPurchases: paidFromPurchases,
            additionalPayments: additionalPayments,
            totalPaid: totalPaid,
            balance: Math.max(0, balance), // تأكد من عدم وجود رصيد سالب
            lastPayment: lastPayment
        };
    }).filter(debt => debt.balance > 0);
    
    return [...customerDebts, ...supplierDebts];
}

/**
 * تحميل خيارات العملاء والموردين
 */
function loadCustomersAndSuppliersOptions() {
    const customers = db.getCustomers().filter(c => c.isActive);
    const suppliers = db.getSuppliers().filter(s => s.isActive);

    const customerSelect = document.querySelector('[name="customerId"]');
    const supplierSelect = document.querySelector('[name="supplierId"]');

    // مسح الخيارات الحالية
    customerSelect.innerHTML = '<option value="">اختر العميل</option>';
    supplierSelect.innerHTML = '<option value="">اختر المورد</option>';

    // إضافة العملاء
    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        customerSelect.appendChild(option);
    });

    // إضافة الموردين
    suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.id;
        option.textContent = supplier.name;
        supplierSelect.appendChild(option);
    });
}

/**
 * إعداد مستمعي الأحداث
 */
function setupDebtsEventListeners() {
    // البحث
    document.getElementById('customerDebtSearch').addEventListener('input', () => filterDebts('customer'));
    document.getElementById('supplierDebtSearch').addEventListener('input', () => filterDebts('supplier'));
    document.getElementById('paymentSearch').addEventListener('input', filterPayments);

    // فلاتر المدفوعات
    document.getElementById('paymentTypeFilter').addEventListener('change', filterPayments);
    document.getElementById('paymentDateFilter').addEventListener('change', filterPayments);

    // نموذج الدفعة
    document.getElementById('paymentForm').addEventListener('submit', handlePaymentSubmit);
}

/**
 * تبديل التبويبات
 */
function switchTab(tabId) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // إزالة الحالة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabId).classList.add('active');

    // تفعيل الزر المناسب
    document.querySelector(`[onclick="switchTab('${tabId}')"]`).classList.add('active');

    // تحميل البيانات حسب التبويب
    switch (tabId) {
        case 'customer-debts':
            displayCustomerDebts();
            break;
        case 'supplier-debts':
            displaySupplierDebts();
            break;
        case 'payments-history':
            displayPayments();
            break;
    }
}

/**
 * عرض ديون العملاء
 */
function displayCustomerDebts(debts = null) {
    const customerDebts = debts || debtsData.filter(debt => debt.type === 'customer');
    const tbody = document.querySelector('#customerDebtsTable tbody');

    if (customerDebts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد ديون للعملاء</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = customerDebts.map(debt => `
        <tr>
            <td><strong>${debt.name}</strong></td>
            <td>${debt.phone || '-'}</td>
            <td>${db.formatCurrency(debt.totalAmount)}</td>
            <td>
                <div class="payment-breakdown">
                    <div>من المبيعات: ${db.formatCurrency(debt.paidFromSales || 0)}</div>
                    <div>دفعات إضافية: ${db.formatCurrency(debt.additionalPayments || 0)}</div>
                    <strong>الإجمالي: ${db.formatCurrency(debt.totalPaid)}</strong>
                </div>
            </td>
            <td>
                <span class="balance-amount debt">
                    ${db.formatCurrency(debt.balance)}
                </span>
            </td>
            <td>${debt.lastPayment ? db.formatDate(debt.lastPayment.date) : '-'}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="showPaymentModal('customer', '${debt.id}')"
                            title="إضافة دفعة">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewDebtDetails('customer', '${debt.id}')"
                            title="التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * عرض ديون الموردين
 */
function displaySupplierDebts(debts = null) {
    const supplierDebts = debts || debtsData.filter(debt => debt.type === 'supplier');
    const tbody = document.querySelector('#supplierDebtsTable tbody');

    if (supplierDebts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد ديون للموردين</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = supplierDebts.map(debt => `
        <tr>
            <td><strong>${debt.name}</strong></td>
            <td>${debt.phone || '-'}</td>
            <td>${db.formatCurrency(debt.totalAmount)}</td>
            <td>${db.formatCurrency(debt.totalPaid)}</td>
            <td>
                <span class="balance-amount debt">
                    ${db.formatCurrency(debt.balance)}
                </span>
            </td>
            <td>${debt.lastPayment ? db.formatDate(debt.lastPayment.date) : '-'}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="showPaymentModal('supplier', '${debt.id}')"
                            title="إضافة دفعة">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewDebtDetails('supplier', '${debt.id}')"
                            title="التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * عرض سجل المدفوعات
 */
function displayPayments(payments = paymentsData) {
    const tbody = document.querySelector('#paymentsTable tbody');

    if (payments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد مدفوعات</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = payments.map(payment => {
        const entityName = payment.type === 'customer' ?
            (db.getCustomer(payment.customerId)?.name || 'غير محدد') :
            (db.getSupplier(payment.supplierId)?.name || 'غير محدد');

        return `
            <tr>
                <td>${db.formatDate(payment.date)}</td>
                <td>
                    <span class="badge badge-${payment.type === 'customer' ? 'success' : 'warning'}">
                        ${payment.type === 'customer' ? 'من عميل' : 'لمورد'}
                    </span>
                </td>
                <td>${entityName}</td>
                <td>${db.formatCurrency(payment.amount)}</td>
                <td>${getPaymentMethodName(payment.paymentMethod)}</td>
                <td>${payment.notes || '-'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-danger" onclick="deletePayment('${payment.id}')"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * تحديث إحصائيات الديون
 */
function updateDebtsStats() {
    const customerDebts = debtsData.filter(debt => debt.type === 'customer');
    const supplierDebts = debtsData.filter(debt => debt.type === 'supplier');

    const totalCustomerDebts = customerDebts.reduce((sum, debt) => sum + debt.balance, 0);
    const totalSupplierDebts = supplierDebts.reduce((sum, debt) => sum + debt.balance, 0);

    // مدفوعات اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayPayments = paymentsData.filter(payment => payment.date === today);
    const todayAmount = todayPayments.reduce((sum, payment) => sum + payment.amount, 0);

    // مدفوعات الشهر
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const monthPayments = paymentsData.filter(payment =>
        new Date(payment.date) >= thisMonth
    );
    const monthAmount = monthPayments.reduce((sum, payment) => sum + payment.amount, 0);

    document.getElementById('totalCustomerDebts').textContent = db.formatCurrency(totalCustomerDebts);
    document.getElementById('totalSupplierDebts').textContent = db.formatCurrency(totalSupplierDebts);
    document.getElementById('todayPayments').textContent = db.formatCurrency(todayAmount);
    document.getElementById('monthPayments').textContent = db.formatCurrency(monthAmount);
}

/**
 * فلترة الديون
 */
function filterDebts(type) {
    const searchInput = document.getElementById(`${type}DebtSearch`);
    const searchTerm = searchInput.value.toLowerCase();

    const filteredDebts = debtsData.filter(debt => {
        if (debt.type !== type) return false;

        return debt.name.toLowerCase().includes(searchTerm) ||
               (debt.phone && debt.phone.includes(searchTerm));
    });

    if (type === 'customer') {
        displayCustomerDebts(filteredDebts);
    } else {
        displaySupplierDebts(filteredDebts);
    }
}

/**
 * فلترة المدفوعات
 */
function filterPayments() {
    const searchTerm = document.getElementById('paymentSearch').value.toLowerCase();
    const typeFilter = document.getElementById('paymentTypeFilter').value;
    const dateFilter = document.getElementById('paymentDateFilter').value;

    let filteredPayments = paymentsData;

    // فلترة بالبحث
    if (searchTerm) {
        filteredPayments = filteredPayments.filter(payment => {
            const entityName = payment.type === 'customer' ?
                (db.getCustomer(payment.customerId)?.name || '') :
                (db.getSupplier(payment.supplierId)?.name || '');

            return entityName.toLowerCase().includes(searchTerm) ||
                   (payment.notes && payment.notes.toLowerCase().includes(searchTerm));
        });
    }

    // فلترة بالنوع
    if (typeFilter) {
        filteredPayments = filteredPayments.filter(payment => payment.type === typeFilter);
    }

    // فلترة بالتاريخ
    if (dateFilter) {
        filteredPayments = filteredPayments.filter(payment => payment.date === dateFilter);
    }

    displayPayments(filteredPayments);
}

/**
 * إظهار نافذة إضافة دفعة
 */
function showAddPaymentModal() {
    document.getElementById('paymentModalTitle').textContent = 'إضافة دفعة جديدة';
    document.getElementById('paymentForm').reset();
    document.querySelector('[name="date"]').value = new Date().toISOString().split('T')[0];

    // إخفاء مجموعات العملاء والموردين
    document.getElementById('customerGroup').style.display = 'none';
    document.getElementById('supplierGroup').style.display = 'none';

    app.showModal('paymentModal');
}

/**
 * إظهار نافذة دفعة لعميل/مورد محدد
 */
function showPaymentModal(type, entityId) {
    document.getElementById('paymentModalTitle').textContent =
        type === 'customer' ? 'إضافة دفعة من عميل' : 'إضافة دفعة لمورد';

    document.getElementById('paymentForm').reset();
    document.querySelector('[name="date"]').value = new Date().toISOString().split('T')[0];

    // تعيين النوع والكيان
    document.querySelector('[name="type"]').value = type;

    if (type === 'customer') {
        document.querySelector('[name="customerId"]').value = entityId;
        document.getElementById('customerGroup').style.display = 'block';
        document.getElementById('supplierGroup').style.display = 'none';
    } else {
        document.querySelector('[name="supplierId"]').value = entityId;
        document.getElementById('customerGroup').style.display = 'none';
        document.getElementById('supplierGroup').style.display = 'block';
    }

    updatePaymentOptions();

    // عرض الرصيد المتبقي للعميل/المورد المحدد
    showRemainingBalance(type, entityId);

    app.showModal('paymentModal');
}

/**
 * تحديث خيارات الدفعة
 */
function updatePaymentOptions() {
    const type = document.querySelector('[name="type"]').value;
    const customerGroup = document.getElementById('customerGroup');
    const supplierGroup = document.getElementById('supplierGroup');

    if (type === 'customer') {
        customerGroup.style.display = 'block';
        supplierGroup.style.display = 'none';
        document.querySelector('[name="customerId"]').required = true;
        document.querySelector('[name="supplierId"]').required = false;
    } else if (type === 'supplier') {
        customerGroup.style.display = 'none';
        supplierGroup.style.display = 'block';
        document.querySelector('[name="customerId"]').required = false;
        document.querySelector('[name="supplierId"]').required = true;
    } else {
        customerGroup.style.display = 'none';
        supplierGroup.style.display = 'none';
        document.querySelector('[name="customerId"]').required = false;
        document.querySelector('[name="supplierId"]').required = false;
    }
}

/**
 * تنسيق إدخال المبلغ
 */
function formatAmountInput(input) {
    // إزالة الأصفار الزائدة والتأكد من صحة التنسيق
    let value = parseFloat(input.value);
    if (!isNaN(value) && value > 0) {
        // تحديث الرصيد المتبقي إذا كان متاحاً
        updateRemainingBalance();
    }
}

/**
 * عرض الرصيد المتبقي للعميل/المورد
 */
function showRemainingBalance(type, entityId) {
    const debt = debtsData.find(d => d.type === type && d.id === entityId);
    if (debt && debt.balance > 0) {
        document.getElementById('remainingBalance').style.display = 'block';
        document.getElementById('remainingAmount').textContent = db.formatCurrency(debt.balance);
        document.getElementById('quickAmountButtons').style.display = 'block';

        // حفظ الرصيد المتبقي للاستخدام في التحديث
        document.querySelector('[name="amount"]').setAttribute('data-max-balance', debt.balance);
    } else {
        document.getElementById('remainingBalance').style.display = 'none';
        document.getElementById('quickAmountButtons').style.display = 'none';
    }
}

/**
 * تحديث الرصيد المتبقي عند تغيير المبلغ
 */
function updateRemainingBalance() {
    const amountInput = document.querySelector('[name="amount"]');
    const maxBalance = parseFloat(amountInput.getAttribute('data-max-balance'));
    const enteredAmount = parseFloat(amountInput.value);

    if (!isNaN(maxBalance) && !isNaN(enteredAmount)) {
        const remaining = Math.max(0, maxBalance - enteredAmount);
        document.getElementById('remainingAmount').textContent = db.formatCurrency(remaining);

        // تغيير لون النص حسب الحالة
        const remainingElement = document.getElementById('remainingAmount');
        if (remaining === 0) {
            remainingElement.style.color = '#28a745'; // أخضر - مدفوع بالكامل
        } else if (enteredAmount > maxBalance) {
            remainingElement.style.color = '#dc3545'; // أحمر - مبلغ زائد
            remainingElement.textContent = db.formatCurrency(remaining) + ' (مبلغ زائد)';
        } else {
            remainingElement.style.color = '#ffc107'; // أصفر - متبقي جزئي
        }
    }
}

/**
 * تعيين مبلغ سريع
 */
function setQuickAmount(amount) {
    const amountInput = document.querySelector('[name="amount"]');
    amountInput.value = amount;
    formatAmountInput(amountInput);
}

/**
 * تعيين المبلغ الكامل
 */
function setFullAmount() {
    const amountInput = document.querySelector('[name="amount"]');
    const maxBalance = parseFloat(amountInput.getAttribute('data-max-balance'));

    if (!isNaN(maxBalance)) {
        amountInput.value = maxBalance;
        formatAmountInput(amountInput);
    }
}

/**
 * معالجة إرسال نموذج الدفعة
 */
async function handlePaymentSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const paymentData = {
        type: formData.get('type'),
        amount: parseFloat(formData.get('amount')),
        paymentMethod: formData.get('paymentMethod'),
        date: formData.get('date'),
        notes: formData.get('notes').trim()
    };

    // إضافة معرف العميل أو المورد
    if (paymentData.type === 'customer') {
        paymentData.customerId = formData.get('customerId');
    } else {
        paymentData.supplierId = formData.get('supplierId');
    }

    // التحقق من صحة البيانات
    const validation = Validator.validateForm(paymentData, {
        type: ['required'],
        amount: ['required', 'min:0.01'],
        paymentMethod: ['required'],
        date: ['required']
    });

    if (!validation.isValid) {
        Validator.displayFormErrors(validation.errors);
        return;
    }

    // التحقق من المبلغ مقابل الرصيد المتبقي
    const amountInput = document.querySelector('[name="amount"]');
    const maxBalance = parseFloat(amountInput.getAttribute('data-max-balance'));

    if (!isNaN(maxBalance) && paymentData.amount > maxBalance) {
        const confirmOverpayment = await app.showConfirm(
            'تحذير - مبلغ زائد',
            `المبلغ المدخل (${db.formatCurrency(paymentData.amount)}) أكبر من الرصيد المتبقي (${db.formatCurrency(maxBalance)}). هل تريد المتابعة؟`,
            'نعم، متابعة',
            'لا، تعديل المبلغ'
        );

        if (!confirmOverpayment) {
            return;
        }
    }

    // التحقق من وجود العميل أو المورد
    if (paymentData.type === 'customer' && !paymentData.customerId) {
        app.showAlert('خطأ', 'يرجى اختيار العميل');
        return;
    }

    if (paymentData.type === 'supplier' && !paymentData.supplierId) {
        app.showAlert('خطأ', 'يرجى اختيار المورد');
        return;
    }

    app.showLoading();

    try {
        // إضافة الدفعة
        db.addPayment(paymentData);

        // تحديث رصيد العميل أو المورد
        if (paymentData.type === 'customer') {
            const customer = db.getCustomer(paymentData.customerId);
            if (customer) {
                db.updateCustomer(customer.id, {
                    balance: Math.max(0, customer.balance - paymentData.amount)
                });
            }
        } else {
            const supplier = db.getSupplier(paymentData.supplierId);
            if (supplier) {
                db.updateSupplier(supplier.id, {
                    balance: Math.max(0, supplier.balance - paymentData.amount)
                });
            }
        }

        app.showNotification('تم إضافة الدفعة بنجاح', 'success');

        // تحديث العرض
        loadDebtsData();
        displayCustomerDebts();
        displaySupplierDebts();
        displayPayments();
        updateDebtsStats();

        // إغلاق النافذة
        app.hideModal('paymentModal');

    } catch (error) {
        console.error('خطأ في إضافة الدفعة:', error);
        app.showAlert('خطأ', 'حدث خطأ في إضافة الدفعة');
    }

    app.hideLoading();
}

/**
 * عرض تفاصيل الديون
 */
function viewDebtDetails(type, entityId) {
    const debt = debtsData.find(d => d.type === type && d.id === entityId);
    if (!debt) {
        app.showAlert('خطأ', 'البيانات غير موجودة');
        return;
    }

    // الحصول على المعاملات
    const sales = type === 'customer' ? db.getSales().filter(s => s.customerId === entityId) : [];
    const purchases = type === 'supplier' ? db.getPurchases().filter(p => p.supplierId === entityId) : [];
    const payments = paymentsData.filter(p =>
        (p.type === 'customer' && p.customerId === entityId) ||
        (p.type === 'supplier' && p.supplierId === entityId)
    );

    const title = type === 'customer' ? `تفاصيل ديون العميل: ${debt.name}` : `تفاصيل ديون المورد: ${debt.name}`;
    document.getElementById('debtDetailsTitle').textContent = title;

    const content = `
        <div class="debt-details">
            <div class="details-summary">
                <div class="summary-card">
                    <h4>ملخص الحساب</h4>
                    <div class="summary-row">
                        <span>إجمالي ${type === 'customer' ? 'المبيعات' : 'المشتريات'}:</span>
                        <span>${db.formatCurrency(debt.totalAmount)}</span>
                    </div>
                    <div class="summary-row">
                        <span>إجمالي المدفوع:</span>
                        <span>${db.formatCurrency(debt.totalPaid)}</span>
                    </div>
                    <div class="summary-row total">
                        <span>الرصيد المتبقي:</span>
                        <span class="balance-amount debt">${db.formatCurrency(debt.balance)}</span>
                    </div>
                </div>
            </div>

            ${type === 'customer' && sales.length > 0 ? `
                <div class="transactions-section">
                    <h4>المبيعات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sales.map(sale => {
                                const remaining = sale.total - sale.paidAmount;
                                return `
                                    <tr>
                                        <td>${sale.invoiceNumber}</td>
                                        <td>${db.formatDate(sale.date)}</td>
                                        <td>${db.formatCurrency(sale.total)}</td>
                                        <td>${db.formatCurrency(sale.paidAmount)}</td>
                                        <td class="${remaining > 0 ? 'debt' : 'clear'}">
                                            ${db.formatCurrency(remaining)}
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            ` : ''}

            ${type === 'supplier' && purchases.length > 0 ? `
                <div class="transactions-section">
                    <h4>المشتريات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchases.map(purchase => {
                                const remaining = purchase.total - purchase.paidAmount;
                                return `
                                    <tr>
                                        <td>${purchase.invoiceNumber}</td>
                                        <td>${db.formatDate(purchase.date)}</td>
                                        <td>${db.formatCurrency(purchase.total)}</td>
                                        <td>${db.formatCurrency(purchase.paidAmount)}</td>
                                        <td class="${remaining > 0 ? 'debt' : 'clear'}">
                                            ${db.formatCurrency(remaining)}
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            ` : ''}

            ${payments.length > 0 ? `
                <div class="transactions-section">
                    <h4>سجل المدفوعات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${payments.map(payment => `
                                <tr>
                                    <td>${db.formatDate(payment.date)}</td>
                                    <td>${db.formatCurrency(payment.amount)}</td>
                                    <td>${getPaymentMethodName(payment.paymentMethod)}</td>
                                    <td>${payment.notes || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            ` : ''}
        </div>
    `;

    document.getElementById('debtDetailsContent').innerHTML = content;
    app.showModal('debtDetailsModal');
}

/**
 * حذف دفعة
 */
function deletePayment(paymentId) {
    const payment = db.getPayment(paymentId);
    if (!payment) {
        app.showAlert('خطأ', 'الدفعة غير موجودة');
        return;
    }

    app.showConfirm(
        'حذف الدفعة',
        'هل أنت متأكد من حذف هذه الدفعة؟',
        () => {
            // إرجاع المبلغ للرصيد
            if (payment.type === 'customer') {
                const customer = db.getCustomer(payment.customerId);
                if (customer) {
                    db.updateCustomer(customer.id, {
                        balance: customer.balance + payment.amount
                    });
                }
            } else {
                const supplier = db.getSupplier(payment.supplierId);
                if (supplier) {
                    db.updateSupplier(supplier.id, {
                        balance: supplier.balance + payment.amount
                    });
                }
            }

            db.deletePayment(paymentId);
            app.showNotification('تم حذف الدفعة بنجاح', 'success');

            // تحديث العرض
            loadDebtsData();
            displayCustomerDebts();
            displaySupplierDebts();
            displayPayments();
            updateDebtsStats();
        }
    );
}

/**
 * تصدير الديون
 */
function exportDebts() {
    const exportData = {
        customerDebts: debtsData.filter(debt => debt.type === 'customer'),
        supplierDebts: debtsData.filter(debt => debt.type === 'supplier'),
        payments: paymentsData,
        exportDate: new Date().toISOString()
    };

    Utils.downloadJSON(exportData, `debts_${new Date().toISOString().split('T')[0]}.json`);
    app.showNotification('تم تصدير الديون بنجاح', 'success');
}

/**
 * الحصول على اسم طريقة الدفع
 */
function getPaymentMethodName(method) {
    const methods = {
        cash: 'نقداً',
        card: 'بطاقة',
        transfer: 'تحويل',
        check: 'شيك'
    };
    return methods[method] || method;
}
