const { app, BrowserWindow, Menu, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// متغيرات عامة
let mainWindow;
let isDev = process.env.NODE_ENV === 'development';

/**
 * إنشاء النافذة الرئيسية
 */
function createMainWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: 'تكنوفلاش - نظام نقطة البيع',
        show: false,
        titleBarStyle: 'default'
    });

    // تحميل الصفحة الرئيسية
    mainWindow.loadFile('index.html');

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // فتح أدوات المطور في وضع التطوير
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // إغلاق التطبيق عند إغلاق النافذة الرئيسية
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // منع التنقل إلى مواقع خارجية
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });

    // إنشاء القائمة
    createMenu();
}

/**
 * إنشاء قائمة التطبيق
 */
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'نسخة احتياطية جديدة',
                    accelerator: 'CmdOrCtrl+B',
                    click: () => {
                        mainWindow.webContents.executeJavaScript('createBackup()');
                    }
                },
                {
                    label: 'استعادة البيانات',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        showRestoreDialog();
                    }
                },
                { type: 'separator' },
                {
                    label: 'تصدير البيانات',
                    click: () => {
                        exportData();
                    }
                },
                {
                    label: 'استيراد البيانات',
                    click: () => {
                        importData();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { role: 'undo', label: 'تراجع' },
                { role: 'redo', label: 'إعادة' },
                { type: 'separator' },
                { role: 'cut', label: 'قص' },
                { role: 'copy', label: 'نسخ' },
                { role: 'paste', label: 'لصق' },
                { role: 'selectall', label: 'تحديد الكل' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { role: 'reload', label: 'إعادة تحميل' },
                { role: 'forceReload', label: 'إعادة تحميل قسري' },
                { role: 'toggleDevTools', label: 'أدوات المطور' },
                { type: 'separator' },
                { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
                { role: 'zoomIn', label: 'تكبير' },
                { role: 'zoomOut', label: 'تصغير' },
                { type: 'separator' },
                { role: 'togglefullscreen', label: 'ملء الشاشة' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { role: 'minimize', label: 'تصغير' },
                { role: 'close', label: 'إغلاق' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول تكنوفلاش',
                    click: () => {
                        showAboutDialog();
                    }
                },
                {
                    label: 'دليل المستخدم',
                    click: () => {
                        shell.openExternal('https://technoflash.com/help');
                    }
                },
                {
                    label: 'الدعم الفني',
                    click: () => {
                        shell.openExternal('https://technoflash.com/support');
                    }
                }
            ]
        }
    ];

    // تعديل القائمة لنظام macOS
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { role: 'about', label: 'حول تكنوفلاش' },
                { type: 'separator' },
                { role: 'services', label: 'الخدمات' },
                { type: 'separator' },
                { role: 'hide', label: 'إخفاء تكنوفلاش' },
                { role: 'hideothers', label: 'إخفاء الآخرين' },
                { role: 'unhide', label: 'إظهار الكل' },
                { type: 'separator' },
                { role: 'quit', label: 'إنهاء تكنوفلاش' }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

/**
 * عرض نافذة حول التطبيق
 */
function showAboutDialog() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'حول تكنوفلاش',
        message: 'تكنوفلاش - نظام نقطة البيع',
        detail: 'الإصدار 1.0.0\n\nنظام نقطة بيع شامل مصمم خصيصاً للأسواق العربية\n\n© 2024 TechnoFlash. جميع الحقوق محفوظة.',
        buttons: ['موافق']
    });
}

/**
 * عرض نافذة استعادة البيانات
 */
async function showRestoreDialog() {
    const result = await dialog.showOpenDialog(mainWindow, {
        title: 'اختر ملف النسخة الاحتياطية',
        filters: [
            { name: 'ملفات النسخ الاحتياطي', extensions: ['json'] },
            { name: 'جميع الملفات', extensions: ['*'] }
        ],
        properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        mainWindow.webContents.executeJavaScript(`restoreFromFile('${filePath}')`);
    }
}

/**
 * تصدير البيانات
 */
async function exportData() {
    const result = await dialog.showSaveDialog(mainWindow, {
        title: 'تصدير البيانات',
        defaultPath: `technoflash-export-${new Date().toISOString().split('T')[0]}.json`,
        filters: [
            { name: 'ملفات JSON', extensions: ['json'] },
            { name: 'جميع الملفات', extensions: ['*'] }
        ]
    });

    if (!result.canceled) {
        mainWindow.webContents.executeJavaScript(`exportAllData('${result.filePath}')`);
    }
}

/**
 * استيراد البيانات
 */
async function importData() {
    const result = await dialog.showOpenDialog(mainWindow, {
        title: 'استيراد البيانات',
        filters: [
            { name: 'ملفات JSON', extensions: ['json'] },
            { name: 'جميع الملفات', extensions: ['*'] }
        ],
        properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        mainWindow.webContents.executeJavaScript(`importAllData('${filePath}')`);
    }
}

// أحداث التطبيق
app.whenReady().then(createMainWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

// منع إنشاء نوافذ متعددة
app.on('second-instance', () => {
    if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.focus();
    }
});

// جعل التطبيق يعمل كنسخة واحدة فقط
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
}
