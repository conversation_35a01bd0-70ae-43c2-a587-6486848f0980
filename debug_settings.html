<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الإعدادات العامة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص مشكلة الإعدادات العامة</h1>
        
        <div class="debug-section">
            <h3>أدوات التشخيص</h3>
            <button class="btn btn-primary" onclick="loadSettings()">تحميل صفحة الإعدادات</button>
            <button class="btn btn-success" onclick="testGeneralSettings()">اختبار الإعدادات العامة</button>
            <button class="btn btn-warning" onclick="forcePopulateGeneral()">إجبار ملء الإعدادات العامة</button>
            <button class="btn btn-warning" onclick="showCurrentSettings()">عرض الإعدادات الحالية</button>
            <button class="btn btn-danger" onclick="clearConsole()">مسح وحدة التحكم</button>
        </div>
        
        <div class="debug-section">
            <h3>وحدة التحكم</h3>
            <div id="console-output"></div>
        </div>
        
        <div id="mainContent">
            <!-- سيتم تحميل محتوى الإعدادات هنا -->
        </div>
    </div>

    <script src="settings.js"></script>
    <script>
        // إعادة توجيه console.log إلى العنصر المرئي
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'warn' ? '#ff0' : '#0f0';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        function showCurrentSettings() {
            console.log('📊 الإعدادات الحالية:', currentSettings);
            if (currentSettings && currentSettings.general) {
                console.log('⚙️ الإعدادات العامة:', currentSettings.general);
            } else {
                console.error('❌ الإعدادات العامة غير موجودة');
            }
        }
        
        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة التشخيص');
        console.log('📖 الأوامر المتاحة:');
        console.log('  - loadSettings() // تحميل صفحة الإعدادات');
        console.log('  - testGeneralSettings() // اختبار الإعدادات العامة');
        console.log('  - forcePopulateGeneral() // إجبار ملء الإعدادات العامة');
        console.log('  - showCurrentSettings() // عرض الإعدادات الحالية');
    </script>
</body>
</html>