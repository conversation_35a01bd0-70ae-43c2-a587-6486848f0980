<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الباركود - تكنوفلاش</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-result {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .barcode-display {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-barcode"></i> اختبار نظام الباركود</h1>
        
        <!-- اختبار توليد Code 128 -->
        <div class="test-section">
            <h2>اختبار توليد Code 128</h2>
            <div class="form-group">
                <label>النص المراد تحويله لباركود:</label>
                <input type="text" id="code128Input" class="form-control" value="TF123456" placeholder="أدخل النص">
            </div>
            <button class="btn btn-primary" onclick="testCode128()">
                <i class="fas fa-barcode"></i> توليد Code 128
            </button>
            <div id="code128Result" class="test-result"></div>
            <div id="code128Display" class="barcode-display"></div>
        </div>

        <!-- اختبار توليد EAN-13 -->
        <div class="test-section">
            <h2>اختبار توليد EAN-13</h2>
            <div class="form-group">
                <label>الأرقام (12 رقم):</label>
                <input type="text" id="ean13Input" class="form-control" value="123456789012" placeholder="أدخل 12 رقم">
            </div>
            <button class="btn btn-primary" onclick="testEAN13()">
                <i class="fas fa-barcode"></i> توليد EAN-13
            </button>
            <div id="ean13Result" class="test-result"></div>
            <div id="ean13Display" class="barcode-display"></div>
        </div>

        <!-- اختبار توليد باركود للمنتج -->
        <div class="test-section">
            <h2>اختبار توليد باركود للمنتج</h2>
            <div class="form-group">
                <label>معرف المنتج:</label>
                <input type="text" id="productIdInput" class="form-control" value="PROD001" placeholder="أدخل معرف المنتج">
            </div>
            <div class="form-group">
                <label>نوع الباركود:</label>
                <select id="productBarcodeType" class="form-control">
                    <option value="CODE128">Code 128</option>
                    <option value="EAN13">EAN-13</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="testProductBarcode()">
                <i class="fas fa-barcode"></i> توليد باركود المنتج
            </button>
            <div id="productBarcodeResult" class="test-result"></div>
            <div id="productBarcodeDisplay" class="barcode-display"></div>
        </div>

        <!-- اختبار تحويل إلى صورة -->
        <div class="test-section">
            <h2>اختبار تحويل إلى صورة</h2>
            <button class="btn btn-success" onclick="testImageExport()">
                <i class="fas fa-download"></i> تحويل آخر باركود إلى PNG
            </button>
            <div id="imageExportResult" class="test-result"></div>
        </div>

        <!-- اختبار التحقق من صحة الباركود -->
        <div class="test-section">
            <h2>اختبار التحقق من صحة الباركود</h2>
            <div class="form-group">
                <label>الباركود للتحقق:</label>
                <input type="text" id="validateInput" class="form-control" value="TF123456" placeholder="أدخل الباركود">
            </div>
            <div class="form-group">
                <label>نوع الباركود:</label>
                <select id="validateType" class="form-control">
                    <option value="CODE128">Code 128</option>
                    <option value="EAN13">EAN-13</option>
                </select>
            </div>
            <button class="btn btn-info" onclick="testValidation()">
                <i class="fas fa-check"></i> التحقق من الصحة
            </button>
            <div id="validationResult" class="test-result"></div>
        </div>

        <!-- معلومات النظام -->
        <div class="test-section">
            <h2>معلومات النظام</h2>
            <div id="systemInfo">
                <p><strong>حالة مولد الباركود:</strong> <span id="generatorStatus">غير محمل</span></p>
                <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
                <p><strong>دعم Canvas:</strong> <span id="canvasSupport"></span></p>
            </div>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="barcode-generator.js"></script>
    
    <script>
        let generator;
        let lastCanvas;

        // تهيئة النظام
        window.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
        });

        function initializeSystem() {
            try {
                generator = new BarcodeGenerator();
                document.getElementById('generatorStatus').textContent = 'محمل بنجاح';
                document.getElementById('generatorStatus').style.color = 'green';
            } catch (error) {
                document.getElementById('generatorStatus').textContent = 'خطأ في التحميل: ' + error.message;
                document.getElementById('generatorStatus').style.color = 'red';
            }

            // معلومات المتصفح
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ')[0];
            
            // دعم Canvas
            const canvas = document.createElement('canvas');
            const canvasSupported = !!(canvas.getContext && canvas.getContext('2d'));
            document.getElementById('canvasSupport').textContent = canvasSupported ? 'مدعوم' : 'غير مدعوم';
            document.getElementById('canvasSupport').style.color = canvasSupported ? 'green' : 'red';
        }

        function testCode128() {
            const input = document.getElementById('code128Input').value;
            const resultDiv = document.getElementById('code128Result');
            const displayDiv = document.getElementById('code128Display');

            if (!input) {
                showResult(resultDiv, 'يرجى إدخال نص', 'error');
                return;
            }

            try {
                const pattern = generator.generateCode128(input);
                const canvas = generator.renderToCanvas(pattern, {
                    width: 300,
                    height: 100,
                    showText: true,
                    text: input,
                    fontSize: 12
                });

                lastCanvas = canvas;
                displayDiv.innerHTML = '';
                displayDiv.appendChild(canvas);

                showResult(resultDiv, `تم توليد باركود Code 128 بنجاح للنص: ${input}`, 'success');
            } catch (error) {
                showResult(resultDiv, `خطأ في توليد الباركود: ${error.message}`, 'error');
            }
        }

        function testEAN13() {
            const input = document.getElementById('ean13Input').value;
            const resultDiv = document.getElementById('ean13Result');
            const displayDiv = document.getElementById('ean13Display');

            if (!input) {
                showResult(resultDiv, 'يرجى إدخال الأرقام', 'error');
                return;
            }

            try {
                const pattern = generator.generateEAN13(input);
                const canvas = generator.renderToCanvas(pattern, {
                    width: 300,
                    height: 100,
                    showText: true,
                    text: input,
                    fontSize: 12
                });

                lastCanvas = canvas;
                displayDiv.innerHTML = '';
                displayDiv.appendChild(canvas);

                showResult(resultDiv, `تم توليد باركود EAN-13 بنجاح للرقم: ${input}`, 'success');
            } catch (error) {
                showResult(resultDiv, `خطأ في توليد الباركود: ${error.message}`, 'error');
            }
        }

        function testProductBarcode() {
            const productId = document.getElementById('productIdInput').value;
            const barcodeType = document.getElementById('productBarcodeType').value;
            const resultDiv = document.getElementById('productBarcodeResult');
            const displayDiv = document.getElementById('productBarcodeDisplay');

            if (!productId) {
                showResult(resultDiv, 'يرجى إدخال معرف المنتج', 'error');
                return;
            }

            try {
                const barcodeValue = generator.generateProductBarcode(productId, barcodeType);
                
                const pattern = barcodeType === 'EAN13' ? 
                    generator.generateEAN13(barcodeValue) : 
                    generator.generateCode128(barcodeValue);

                const canvas = generator.renderToCanvas(pattern, {
                    width: 300,
                    height: 100,
                    showText: true,
                    text: barcodeValue,
                    fontSize: 12
                });

                lastCanvas = canvas;
                displayDiv.innerHTML = '';
                displayDiv.appendChild(canvas);

                showResult(resultDiv, `تم توليد باركود ${barcodeType} للمنتج: ${productId}<br>قيمة الباركود: ${barcodeValue}`, 'success');
            } catch (error) {
                showResult(resultDiv, `خطأ في توليد باركود المنتج: ${error.message}`, 'error');
            }
        }

        function testImageExport() {
            const resultDiv = document.getElementById('imageExportResult');

            if (!lastCanvas) {
                showResult(resultDiv, 'لا يوجد باركود لتصديره. يرجى توليد باركود أولاً', 'error');
                return;
            }

            try {
                const imageData = generator.canvasToImage(lastCanvas, 'png');
                
                // إنشاء رابط تحميل
                const link = document.createElement('a');
                link.download = 'barcode.png';
                link.href = imageData;
                link.click();

                showResult(resultDiv, 'تم تصدير الباركود كصورة PNG بنجاح', 'success');
            } catch (error) {
                showResult(resultDiv, `خطأ في تصدير الصورة: ${error.message}`, 'error');
            }
        }

        function testValidation() {
            const barcode = document.getElementById('validateInput').value;
            const type = document.getElementById('validateType').value;
            const resultDiv = document.getElementById('validationResult');

            if (!barcode) {
                showResult(resultDiv, 'يرجى إدخال الباركود', 'error');
                return;
            }

            try {
                const isValid = generator.validateBarcode(barcode, type);
                const message = isValid ? 
                    `الباركود صحيح لنوع ${type}` : 
                    `الباركود غير صحيح لنوع ${type}`;
                
                showResult(resultDiv, message, isValid ? 'success' : 'error');
            } catch (error) {
                showResult(resultDiv, `خطأ في التحقق: ${error.message}`, 'error');
            }
        }

        function showResult(element, message, type) {
            element.innerHTML = message;
            element.className = `test-result ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
