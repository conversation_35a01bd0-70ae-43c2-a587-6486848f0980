/**
 * إدارة العملاء - تكنوفلاش
 */

// مستمع أحداث تحديث الإعدادات
window.addEventListener('settingsUpdated', function(event) {
    const settings = event.detail;
    console.log('تم استلام تحديث الإعدادات في العملاء:', settings);

    // تحديث الوضع الليلي
    if (settings.general && typeof settings.general.darkMode !== 'undefined') {
        if (settings.general.darkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        console.log('تم تحديث الوضع الليلي في العملاء:', settings.general.darkMode);
    }

    // تحديث العملة في واجهة العملاء
    if (settings.general && settings.general.currency) {
        updateCustomersCurrency(settings.general.currency);
    }

    // تحديث نوع الأرقام
    if (settings.general && settings.general.numberType) {
        updateCustomersNumbers(settings.general.numberType);
    }
});

let currentCustomer = null;
let customersData = [];

/**
 * تحميل صفحة العملاء
 */
async function loadCustomers() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddCustomerModal()">
                    <i class="fas fa-plus"></i> إضافة عميل
                </button>
                <button class="btn btn-info" onclick="exportCustomers()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="btn btn-warning" onclick="showImportModal()">
                    <i class="fas fa-upload"></i> استيراد
                </button>
            </div>
        </div>

        <!-- شريط البحث والفلاتر -->
        <div class="filters-section">
            <div class="card">
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <div class="search-bar">
                                <input type="text" class="search-input" id="customerSearchInput" 
                                       placeholder="البحث بالاسم أو الهاتف...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="balanceFilter">
                                <option value="">جميع العملاء</option>
                                <option value="with-balance">لديهم رصيد</option>
                                <option value="no-balance">بدون رصيد</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات العملاء -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value" id="totalCustomersCount">٠</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-value" id="activeCustomersCount">٠</div>
                <div class="stat-label">العملاء النشطون</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-value" id="totalDebtAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي الديون</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-value" id="newCustomersThisMonth">٠</div>
                <div class="stat-label">عملاء جدد هذا الشهر</div>
            </div>
        </div>

        <!-- جدول العملاء -->
        <div class="card">
            <div class="card-body">
                <div class="table-container">
                    <table class="table" id="customersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم تحميل البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل عميل -->
        <div id="customerModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="customerModalTitle">إضافة عميل جديد</h3>
                    <button class="modal-close" onclick="app.hideModal('customerModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>الاسم الكامل *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="form-group">
                                <label>رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                            <div class="form-group">
                                <label>تاريخ الميلاد</label>
                                <input type="date" class="form-control" name="birthDate">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>العنوان</label>
                            <textarea class="form-control" name="address" rows="3" 
                                      placeholder="العنوان الكامل..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الرصيد الحالي</label>
                                <input type="number" class="form-control" name="balance" 
                                       step="0.01" value="0" readonly>
                            </div>
                            <div class="form-group">
                                <label>الحد الائتماني</label>
                                <input type="number" class="form-control" name="creditLimit" 
                                       step="0.01" min="0" value="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="isActive" 
                                       id="isActiveCheck" checked>
                                <label class="form-check-label" for="isActiveCheck">
                                    عميل نشط
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('customerModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة تعديل الرصيد -->
        <div id="balanceModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تعديل رصيد العميل</h3>
                    <button class="modal-close" onclick="app.hideModal('balanceModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="balanceForm">
                        <div class="customer-info">
                            <h4 id="balanceCustomerName"></h4>
                            <p>الرصيد الحالي: <span id="currentBalance"></span></p>
                        </div>

                        <div class="form-group">
                            <label>نوع العملية</label>
                            <select class="form-control" name="operationType" required>
                                <option value="add">إضافة مبلغ (دين)</option>
                                <option value="subtract">خصم مبلغ (دفع)</option>
                                <option value="set">تعيين رصيد جديد</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>المبلغ *</label>
                            <input type="number" class="form-control" name="amount" 
                                   step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="سبب التعديل..."></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> تطبيق
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="app.hideModal('balanceModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نافذة الاستيراد -->
        <div id="importModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>استيراد العملاء</h3>
                    <button class="modal-close" onclick="app.hideModal('importModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="import-section">
                        <p>يمكنك استيراد العملاء من ملف JSON:</p>
                        <input type="file" id="importFile" accept=".json" class="form-control">
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="importCustomers()">
                                <i class="fas fa-upload"></i> استيراد
                            </button>
                            <button class="btn btn-info" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i> تحميل نموذج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeCustomersPage();
}

/**
 * تهيئة صفحة العملاء
 */
async function initializeCustomersPage() {
    // تحميل البيانات
    loadCustomersData();
    
    // إعداد مستمعي الأحداث
    setupCustomersEventListeners();
    
    // عرض العملاء والإحصائيات
    displayCustomers();
    updateCustomersStats();
}

/**
 * تحميل بيانات العملاء
 */
function loadCustomersData() {
    customersData = db.getCustomers().filter(customer => customer.id !== 'guest');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupCustomersEventListeners() {
    // البحث
    document.getElementById('customerSearchInput').addEventListener('input', filterCustomers);
    
    // الفلاتر
    document.getElementById('balanceFilter').addEventListener('change', filterCustomers);
    document.getElementById('statusFilter').addEventListener('change', filterCustomers);
    
    // نماذج
    document.getElementById('customerForm').addEventListener('submit', handleCustomerSubmit);
    document.getElementById('balanceForm').addEventListener('submit', handleBalanceSubmit);
}

/**
 * عرض العملاء
 */
function displayCustomers(customers = customersData) {
    const tbody = document.querySelector('#customersTable tbody');
    
    if (customers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد عملاء</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = customers.map(customer => `
        <tr>
            <td>
                <strong>${customer.name}</strong>
                ${customer.email ? `<br><small>${customer.email}</small>` : ''}
            </td>
            <td>${customer.phone || '-'}</td>
            <td>${customer.email || '-'}</td>
            <td>
                <span class="balance-amount ${customer.balance > 0 ? 'debt' : 'clear'}">
                    ${db.formatCurrency(customer.balance)}
                </span>
            </td>
            <td>${db.formatDate(customer.createdAt)}</td>
            <td>
                <span class="badge badge-${customer.isActive ? 'success' : 'secondary'}">
                    ${customer.isActive ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="editCustomer('${customer.id}')" 
                            title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adjustBalance('${customer.id}')" 
                            title="تعديل الرصيد">
                        <i class="fas fa-money-bill-wave"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="viewCustomerHistory('${customer.id}')" 
                            title="السجل">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer('${customer.id}')" 
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * تحديث إحصائيات العملاء
 */
function updateCustomersStats() {
    const totalCustomers = customersData.length;
    const activeCustomers = customersData.filter(c => c.isActive).length;
    const totalDebt = customersData.reduce((sum, c) => sum + (c.balance > 0 ? c.balance : 0), 0);
    
    // العملاء الجدد هذا الشهر
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const newCustomersThisMonth = customersData.filter(c => 
        new Date(c.createdAt) >= thisMonth
    ).length;
    
    document.getElementById('totalCustomersCount').textContent = db.toArabicNumerals(totalCustomers);
    document.getElementById('activeCustomersCount').textContent = db.toArabicNumerals(activeCustomers);
    document.getElementById('totalDebtAmount').textContent = db.formatCurrency(totalDebt);
    document.getElementById('newCustomersThisMonth').textContent = db.toArabicNumerals(newCustomersThisMonth);
}

/**
 * فلترة العملاء
 */
function filterCustomers() {
    const searchTerm = document.getElementById('customerSearchInput').value.toLowerCase();
    const balanceFilter = document.getElementById('balanceFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    let filteredCustomers = customersData;
    
    // فلترة بالبحث
    if (searchTerm) {
        filteredCustomers = filteredCustomers.filter(customer => 
            customer.name.toLowerCase().includes(searchTerm) ||
            (customer.phone && customer.phone.includes(searchTerm)) ||
            (customer.email && customer.email.toLowerCase().includes(searchTerm))
        );
    }
    
    // فلترة بالرصيد
    if (balanceFilter) {
        filteredCustomers = filteredCustomers.filter(customer => {
            switch (balanceFilter) {
                case 'with-balance':
                    return customer.balance > 0;
                case 'no-balance':
                    return customer.balance <= 0;
                default:
                    return true;
            }
        });
    }
    
    // فلترة بالحالة
    if (statusFilter) {
        filteredCustomers = filteredCustomers.filter(customer => {
            switch (statusFilter) {
                case 'active':
                    return customer.isActive;
                case 'inactive':
                    return !customer.isActive;
                default:
                    return true;
            }
        });
    }
    
    displayCustomers(filteredCustomers);
}

/**
 * إظهار نافذة إضافة عميل
 */
function showAddCustomerModal() {
    currentCustomer = null;
    document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('isActiveCheck').checked = true;
    app.showModal('customerModal');
}

/**
 * تعديل عميل
 */
function editCustomer(customerId) {
    currentCustomer = db.getCustomer(customerId);
    if (!currentCustomer) {
        app.showAlert('خطأ', 'العميل غير موجود');
        return;
    }
    
    document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
    
    // ملء النموذج
    const form = document.getElementById('customerForm');
    form.name.value = currentCustomer.name;
    form.phone.value = currentCustomer.phone || '';
    form.email.value = currentCustomer.email || '';
    form.birthDate.value = currentCustomer.birthDate || '';
    form.address.value = currentCustomer.address || '';
    form.balance.value = currentCustomer.balance || 0;
    form.creditLimit.value = currentCustomer.creditLimit || 0;
    form.notes.value = currentCustomer.notes || '';
    form.isActive.checked = currentCustomer.isActive !== false;
    
    app.showModal('customerModal');
}

/**
 * معالجة إرسال نموذج العميل
 */
async function handleCustomerSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const customerData = {
        name: formData.get('name').trim(),
        phone: formData.get('phone').trim(),
        email: formData.get('email').trim(),
        birthDate: formData.get('birthDate'),
        address: formData.get('address').trim(),
        creditLimit: parseFloat(formData.get('creditLimit')) || 0,
        notes: formData.get('notes').trim(),
        isActive: formData.has('isActive')
    };
    
    // التحقق من صحة البيانات
    const validation = Validator.validateForm(customerData, {
        name: ['required', 'minLength:2'],
        phone: customerData.phone ? ['phone'] : [],
        email: customerData.email ? ['email'] : []
    });
    
    if (!validation.isValid) {
        Validator.displayFormErrors(validation.errors);
        return;
    }
    
    app.showLoading();
    
    try {
        if (currentCustomer) {
            // تحديث عميل موجود (مع الحفاظ على الرصيد)
            customerData.balance = currentCustomer.balance;
            db.updateCustomer(currentCustomer.id, customerData);
            app.showNotification('تم تحديث العميل بنجاح', 'success');
        } else {
            // إضافة عميل جديد
            customerData.balance = 0;
            db.addCustomer(customerData);
            app.showNotification('تم إضافة العميل بنجاح', 'success');
        }
        
        // تحديث العرض
        loadCustomersData();
        displayCustomers();
        updateCustomersStats();
        
        // إغلاق النافذة
        app.hideModal('customerModal');
        
    } catch (error) {
        console.error('خطأ في حفظ العميل:', error);
        app.showAlert('خطأ', 'حدث خطأ في حفظ العميل');
    }
    
    app.hideLoading();
}

/**
 * تعديل رصيد العميل
 */
function adjustBalance(customerId) {
    const customer = db.getCustomer(customerId);
    if (!customer) {
        app.showAlert('خطأ', 'العميل غير موجود');
        return;
    }
    
    currentCustomer = customer;
    document.getElementById('balanceCustomerName').textContent = customer.name;
    document.getElementById('currentBalance').textContent = db.formatCurrency(customer.balance);
    document.getElementById('balanceForm').reset();
    
    app.showModal('balanceModal');
}

/**
 * معالجة تعديل الرصيد
 */
async function handleBalanceSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const operationType = formData.get('operationType');
    const amount = parseFloat(formData.get('amount'));
    const notes = formData.get('notes').trim();
    
    if (!amount || amount <= 0) {
        app.showAlert('خطأ', 'يرجى إدخال مبلغ صحيح');
        return;
    }
    
    let newBalance = currentCustomer.balance;
    
    switch (operationType) {
        case 'add':
            newBalance += amount;
            break;
        case 'subtract':
            newBalance -= amount;
            break;
        case 'set':
            newBalance = amount;
            break;
    }
    
    // التحقق من الحد الائتماني
    if (newBalance > currentCustomer.creditLimit && currentCustomer.creditLimit > 0) {
        const confirm = await new Promise(resolve => {
            app.showConfirm(
                'تجاوز الحد الائتماني',
                `الرصيد الجديد ${db.formatCurrency(newBalance)} يتجاوز الحد الائتماني ${db.formatCurrency(currentCustomer.creditLimit)}. هل تريد المتابعة؟`,
                () => resolve(true)
            );
        });
        
        if (!confirm) return;
    }
    
    app.showLoading();
    
    try {
        // تحديث الرصيد
        db.updateCustomer(currentCustomer.id, { balance: newBalance });
        
        // إضافة سجل للعملية
        db.addBalanceTransaction({
            customerId: currentCustomer.id,
            type: operationType,
            amount: amount,
            oldBalance: currentCustomer.balance,
            newBalance: newBalance,
            notes: notes,
            date: new Date().toISOString()
        });
        
        app.showNotification('تم تحديث الرصيد بنجاح', 'success');
        
        // تحديث العرض
        loadCustomersData();
        displayCustomers();
        updateCustomersStats();
        
        // إغلاق النافذة
        app.hideModal('balanceModal');
        
    } catch (error) {
        console.error('خطأ في تحديث الرصيد:', error);
        app.showAlert('خطأ', 'حدث خطأ في تحديث الرصيد');
    }
    
    app.hideLoading();
}

/**
 * عرض سجل العميل
 */
function viewCustomerHistory(customerId) {
    const customer = db.getCustomer(customerId);
    if (!customer) {
        app.showAlert('خطأ', 'العميل غير موجود');
        return;
    }
    
    // سيتم تنفيذ هذه الوظيفة لاحقاً
    app.showAlert('قريباً', 'هذه الميزة ستكون متاحة قريباً');
}

/**
 * حذف عميل
 */
function deleteCustomer(customerId) {
    const customer = db.getCustomer(customerId);
    if (!customer) {
        app.showAlert('خطأ', 'العميل غير موجود');
        return;
    }
    
    // التحقق من وجود مبيعات للعميل
    const sales = db.getSales().filter(sale => sale.customerId === customerId);
    if (sales.length > 0) {
        app.showAlert('تحذير', 'لا يمكن حذف العميل لأن له مبيعات مسجلة');
        return;
    }
    
    app.showConfirm(
        'حذف العميل',
        `هل أنت متأكد من حذف العميل "${customer.name}"؟`,
        () => {
            db.deleteCustomer(customerId);
            app.showNotification('تم حذف العميل بنجاح', 'success');
            
            loadCustomersData();
            displayCustomers();
            updateCustomersStats();
        }
    );
}

/**
 * تصدير العملاء
 */
function exportCustomers() {
    const customers = db.getCustomers().filter(c => c.id !== 'guest');
    const exportData = customers.map(customer => ({
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        birthDate: customer.birthDate,
        address: customer.address,
        balance: customer.balance,
        creditLimit: customer.creditLimit,
        notes: customer.notes,
        isActive: customer.isActive
    }));
    
    Utils.downloadJSON(exportData, `customers_${new Date().toISOString().split('T')[0]}.json`);
    app.showNotification('تم تصدير العملاء بنجاح', 'success');
}

/**
 * إظهار نافذة الاستيراد
 */
function showImportModal() {
    app.showModal('importModal');
}

/**
 * استيراد العملاء
 */
async function importCustomers() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        app.showAlert('خطأ', 'يرجى اختيار ملف');
        return;
    }
    
    app.showLoading();
    
    try {
        const data = await Utils.readJSONFile(file);
        
        if (!Array.isArray(data)) {
            throw new Error('تنسيق الملف غير صحيح');
        }
        
        let importedCount = 0;
        let errorCount = 0;
        
        for (const customerData of data) {
            try {
                // التحقق من البيانات الأساسية
                if (!customerData.name) {
                    errorCount++;
                    continue;
                }
                
                db.addCustomer(customerData);
                importedCount++;
                
            } catch (error) {
                errorCount++;
            }
        }
        
        app.showNotification(
            `تم استيراد ${importedCount} عميل بنجاح${errorCount > 0 ? ` (${errorCount} خطأ)` : ''}`,
            'success'
        );
        
        // تحديث العرض
        loadCustomersData();
        displayCustomers();
        updateCustomersStats();
        
        // إغلاق النافذة
        app.hideModal('importModal');
        
    } catch (error) {
        console.error('خطأ في الاستيراد:', error);
        app.showAlert('خطأ', 'حدث خطأ في استيراد الملف');
    }
    
    app.hideLoading();
}

/**
 * تحميل نموذج الاستيراد
 */
function downloadTemplate() {
    const template = [
        {
            name: "عميل تجريبي",
            phone: "0501234567",
            email: "<EMAIL>",
            birthDate: "1990-01-01",
            address: "الرياض، المملكة العربية السعودية",
            balance: 0,
            creditLimit: 1000,
            notes: "ملاحظات العميل",
            isActive: true
        }
    ];
    
    Utils.downloadJSON(template, 'customers_template.json');
    app.showNotification('تم تحميل النموذج بنجاح', 'info');
}

/**
 * تحديث العملة في واجهة العملاء
 */
function updateCustomersCurrency(currency) {
    try {
        // تحديث العملة في العنصر الجذر
        document.documentElement.setAttribute('data-currency', currency);

        // تحديث جميع عناصر العملة في واجهة العملاء
        const currencyElements = document.querySelectorAll('.currency, [data-currency], .amount, .balance, .debt');
        currencyElements.forEach(element => {
            element.setAttribute('data-currency', currency);
            element.classList.add('currency');
        });

        // إعادة تحميل قائمة العملاء لتطبيق العملة الجديدة
        if (typeof loadCustomersList === 'function') {
            loadCustomersList();
        }

        console.log('تم تحديث العملة في العملاء:', currency);
    } catch (error) {
        console.error('خطأ في تحديث العملة في العملاء:', error);
    }
}

/**
 * تحديث نوع الأرقام في واجهة العملاء
 */
function updateCustomersNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // تحديث جميع عناصر الأرقام
        const numberElements = document.querySelectorAll('.number, .amount, .balance, .debt, .phone');
        numberElements.forEach(element => {
            element.classList.add('number');
        });

        // إعادة تحميل قائمة العملاء لتطبيق نوع الأرقام الجديد
        if (typeof loadCustomersList === 'function') {
            loadCustomersList();
        }

        console.log('تم تحديث نوع الأرقام في العملاء:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام في العملاء:', error);
    }
}
