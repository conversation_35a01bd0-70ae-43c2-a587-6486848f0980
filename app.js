/**
 * وظائف مساعدة وأدوات عامة لتطبيق تكنوفلاش
 */

/**
 * فئة الأدوات المساعدة
 */
class Utils {
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * التحقق من صحة رقم الهاتف
     */
    static isValidPhone(phone) {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        return phoneRegex.test(phone) && phone.length >= 10;
    }

    /**
     * تنظيف النص من المسافات الزائدة
     */
    static cleanText(text) {
        return text.trim().replace(/\s+/g, ' ');
    }

    /**
     * تحويل النص إلى عنوان URL صالح
     */
    static slugify(text) {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }

    /**
     * تنسيق الرقم بفواصل الآلاف
     */
    static formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    /**
     * حساب النسبة المئوية
     */
    static calculatePercentage(value, total) {
        if (total === 0) return 0;
        return Math.round((value / total) * 100);
    }

    /**
     * تحويل الحجم بالبايت إلى وحدة مقروءة
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * إنشاء عنصر HTML
     */
    static createElement(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }

    /**
     * تأخير التنفيذ
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * نسخ النص إلى الحافظة
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // طريقة بديلة للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        }
    }

    /**
     * تحميل ملف JSON
     */
    static downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * قراءة ملف JSON
     */
    static readJSONFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    }

    /**
     * طباعة عنصر HTML
     */
    static printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return false;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                ${element.innerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
        return true;
    }
}

/**
 * فئة التحقق من صحة البيانات
 */
class Validator {
    /**
     * قواعد التحقق
     */
    static rules = {
        required: (value) => value !== null && value !== undefined && value.toString().trim() !== '',
        minLength: (value, min) => value.toString().length >= min,
        maxLength: (value, max) => value.toString().length <= max,
        min: (value, min) => parseFloat(value) >= min,
        max: (value, max) => parseFloat(value) <= max,
        email: (value) => Utils.isValidEmail(value),
        phone: (value) => Utils.isValidPhone(value),
        number: (value) => !isNaN(parseFloat(value)) && isFinite(value),
        integer: (value) => Number.isInteger(parseFloat(value)),
        positive: (value) => parseFloat(value) > 0,
        nonNegative: (value) => parseFloat(value) >= 0
    };

    /**
     * رسائل الخطأ
     */
    static messages = {
        required: 'هذا الحقل مطلوب',
        minLength: 'يجب أن يكون الطول أكبر من {min} أحرف',
        maxLength: 'يجب أن يكون الطول أقل من {max} أحرف',
        min: 'يجب أن تكون القيمة أكبر من {min}',
        max: 'يجب أن تكون القيمة أقل من {max}',
        email: 'البريد الإلكتروني غير صحيح',
        phone: 'رقم الهاتف غير صحيح',
        number: 'يجب أن تكون القيمة رقماً',
        integer: 'يجب أن تكون القيمة رقماً صحيحاً',
        positive: 'يجب أن تكون القيمة موجبة',
        nonNegative: 'يجب أن تكون القيمة غير سالبة'
    };

    /**
     * التحقق من حقل واحد
     */
    static validateField(value, rules) {
        const errors = [];

        for (const rule of rules) {
            const [ruleName, ...params] = rule.split(':');
            const ruleFunction = this.rules[ruleName];

            if (ruleFunction) {
                const isValid = params.length > 0 
                    ? ruleFunction(value, ...params.map(p => isNaN(p) ? p : parseFloat(p)))
                    : ruleFunction(value);

                if (!isValid) {
                    let message = this.messages[ruleName] || 'قيمة غير صحيحة';
                    
                    // استبدال المتغيرات في الرسالة
                    params.forEach((param, index) => {
                        const placeholder = `{${Object.keys(this.messages)[Object.values(this.messages).indexOf(message)].split(':')[index] || 'param'}}`;
                        message = message.replace(placeholder, param);
                    });

                    errors.push(message);
                }
            }
        }

        return errors;
    }

    /**
     * التحقق من نموذج كامل
     */
    static validateForm(data, schema) {
        const errors = {};
        let isValid = true;

        for (const [field, rules] of Object.entries(schema)) {
            const fieldErrors = this.validateField(data[field], rules);
            if (fieldErrors.length > 0) {
                errors[field] = fieldErrors;
                isValid = false;
            }
        }

        return { isValid, errors };
    }

    /**
     * عرض أخطاء النموذج
     */
    static displayFormErrors(errors) {
        // إزالة الأخطاء السابقة
        document.querySelectorAll('.field-error').forEach(el => el.remove());

        for (const [field, fieldErrors] of Object.entries(errors)) {
            const fieldElement = document.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                const errorElement = Utils.createElement('div', 'field-error', fieldErrors.join('<br>'));
                errorElement.style.color = 'var(--error-color)';
                errorElement.style.fontSize = 'var(--font-size-sm)';
                errorElement.style.marginTop = 'var(--spacing-xs)';
                
                fieldElement.parentNode.appendChild(errorElement);
                fieldElement.style.borderColor = 'var(--error-color)';
            }
        }
    }

    /**
     * مسح أخطاء النموذج
     */
    static clearFormErrors() {
        document.querySelectorAll('.field-error').forEach(el => el.remove());
        document.querySelectorAll('.form-control').forEach(el => {
            el.style.borderColor = '';
        });
    }
}

/**
 * فئة إدارة التخزين المحلي
 */
class Storage {
    /**
     * حفظ قيمة
     */
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    /**
     * استرجاع قيمة
     */
    static get(key, defaultValue = null) {
        try {
            const value = localStorage.getItem(key);
            return value ? JSON.parse(value) : defaultValue;
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return defaultValue;
        }
    }

    /**
     * حذف قيمة
     */
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    static clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }

    /**
     * التحقق من وجود مفتاح
     */
    static has(key) {
        return localStorage.getItem(key) !== null;
    }

    /**
     * الحصول على جميع المفاتيح
     */
    static keys() {
        return Object.keys(localStorage);
    }

    /**
     * الحصول على حجم التخزين المستخدم
     */
    static getSize() {
        let total = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                total += localStorage[key].length + key.length;
            }
        }
        return total;
    }
}

/**
 * فئة إدارة الأحداث
 */
class EventManager {
    constructor() {
        this.events = {};
    }

    /**
     * إضافة مستمع حدث
     */
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    /**
     * إزالة مستمع حدث
     */
    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }

    /**
     * إطلاق حدث
     */
    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }

    /**
     * إضافة مستمع حدث لمرة واحدة
     */
    once(event, callback) {
        const onceCallback = (data) => {
            callback(data);
            this.off(event, onceCallback);
        };
        this.on(event, onceCallback);
    }
}

// إنشاء مثيل عام لإدارة الأحداث
const eventManager = new EventManager();

/**
 * وظائف مساعدة عامة
 */

/**
 * تحديد عنصر DOM
 */
function $(selector) {
    return document.querySelector(selector);
}

/**
 * تحديد عدة عناصر DOM
 */
function $$(selector) {
    return document.querySelectorAll(selector);
}

/**
 * إضافة مستمع حدث مع دعم التفويض
 */
function on(element, event, selector, callback) {
    if (typeof selector === 'function') {
        callback = selector;
        element.addEventListener(event, callback);
    } else {
        element.addEventListener(event, function(e) {
            if (e.target.matches(selector)) {
                callback.call(e.target, e);
            }
        });
    }
}

/**
 * إزالة مستمع حدث
 */
function off(element, event, callback) {
    element.removeEventListener(event, callback);
}

/**
 * تبديل فئة CSS
 */
function toggleClass(element, className) {
    element.classList.toggle(className);
}

/**
 * إضافة فئة CSS
 */
function addClass(element, className) {
    element.classList.add(className);
}

/**
 * إزالة فئة CSS
 */
function removeClass(element, className) {
    element.classList.remove(className);
}

/**
 * التحقق من وجود فئة CSS
 */
function hasClass(element, className) {
    return element.classList.contains(className);
}
