<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الإعدادات - تكنوفلاش</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
        }
        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-right: 4px solid #007bff;
        }
        .status-card.error {
            border-right-color: #dc3545;
            background: #fff5f5;
        }
        .status-card.success {
            border-right-color: #28a745;
            background: #f0fff4;
        }
        .status-card.warning {
            border-right-color: #ffc107;
            background: #fffbf0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .actions {
            text-align: center;
            margin: 30px 0;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .icon {
            font-size: 1.2em;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح إعدادات تكنوفلاش</h1>
            <p>أداة لتشخيص وإصلاح مشاكل الإعدادات</p>
        </div>

        <div id="statusCard" class="status-card">
            <h3>📊 حالة النظام</h3>
            <p>جاري فحص النظام...</p>
        </div>

        <div class="actions">
            <button class="btn" onclick="diagnoseSystem()">
                <span class="icon">🔍</span> تشخيص النظام
            </button>
            <button class="btn btn-success" onclick="fixSettings()">
                <span class="icon">🔧</span> إصلاح الإعدادات
            </button>
            <button class="btn btn-warning" onclick="resetSettings()">
                <span class="icon">🔄</span> إعادة تعيين
            </button>
            <button class="btn btn-danger" onclick="emergencyReset()">
                <span class="icon">⚠️</span> إعادة تعيين طارئة
            </button>
        </div>

        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>

        <div id="logContainer" class="log" style="display: none;"></div>

        <div class="actions">
            <button class="btn" onclick="goToMainSystem()">
                <span class="icon">🏠</span> العودة للنظام الرئيسي
            </button>
            <button class="btn" onclick="clearLog()">
                <span class="icon">🗑️</span> مسح السجل
            </button>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="database.js"></script>
    <script src="app.js"></script>
    <script src="backup.js"></script>
    <script src="settings.js"></script>

    <script>
        let logMessages = [];

        // إعادة توجيه console للسجل
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('INFO', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN', args.join(' '));
        };

        function addToLog(type, message) {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = `[${timestamp}] ${type}: ${message}`;
            logMessages.push(logEntry);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logContainer = document.getElementById('logContainer');
            logContainer.textContent = logMessages.slice(-50).join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            logContainer.style.display = 'block';
        }

        function clearLog() {
            logMessages = [];
            document.getElementById('logContainer').style.display = 'none';
        }

        function updateStatus(message, type = 'info') {
            const statusCard = document.getElementById('statusCard');
            statusCard.className = `status-card ${type}`;
            statusCard.innerHTML = `
                <h3>${type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '📊'} ${
                    type === 'error' ? 'خطأ' : type === 'success' ? 'نجح' : type === 'warning' ? 'تحذير' : 'حالة النظام'
                }</h3>
                <p>${message}</p>
            `;
        }

        function showProgress(percent, message = '') {
            const container = document.getElementById('progressContainer');
            const bar = document.getElementById('progressBar');
            container.style.display = 'block';
            bar.style.width = percent + '%';
            bar.textContent = message || percent + '%';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        async function diagnoseSystem() {
            updateStatus('جاري تشخيص النظام...', 'info');
            showProgress(0, 'بدء التشخيص...');

            try {
                // فحص localStorage
                showProgress(25, 'فحص التخزين المحلي...');
                const settingsData = localStorage.getItem('technoflash_settings');
                
                if (!settingsData) {
                    updateStatus('لا توجد إعدادات محفوظة. يحتاج النظام لإنشاء إعدادات افتراضية.', 'warning');
                    return;
                }

                // فحص صحة البيانات
                showProgress(50, 'فحص صحة البيانات...');
                let settings;
                try {
                    settings = JSON.parse(settingsData);
                } catch (e) {
                    updateStatus('الإعدادات المحفوظة تالفة ولا يمكن قراءتها.', 'error');
                    return;
                }

                // فحص الأقسام المطلوبة
                showProgress(75, 'فحص الأقسام المطلوبة...');
                const requiredSections = ['general', 'company', 'pos'];
                const missingSections = requiredSections.filter(section => !settings[section]);

                if (missingSections.length > 0) {
                    updateStatus(`أقسام مفقودة في الإعدادات: ${missingSections.join(', ')}`, 'warning');
                    return;
                }

                // فحص قاعدة البيانات
                showProgress(90, 'فحص قاعدة البيانات...');
                if (typeof db === 'undefined' || !db) {
                    updateStatus('قاعدة البيانات غير متاحة، لكن الإعدادات سليمة.', 'warning');
                    return;
                }

                showProgress(100, 'اكتمل التشخيص');
                updateStatus('النظام يعمل بشكل طبيعي. جميع الإعدادات سليمة.', 'success');

            } catch (error) {
                updateStatus(`خطأ في التشخيص: ${error.message}`, 'error');
            } finally {
                setTimeout(hideProgress, 2000);
            }
        }

        async function fixSettings() {
            updateStatus('جاري إصلاح الإعدادات...', 'info');
            showProgress(0, 'بدء الإصلاح...');

            try {
                if (typeof fixCorruptedSettings === 'function') {
                    showProgress(50, 'تشغيل دالة الإصلاح...');
                    const result = fixCorruptedSettings();
                    
                    if (result) {
                        showProgress(100, 'تم الإصلاح بنجاح');
                        updateStatus('تم إصلاح الإعدادات بنجاح!', 'success');
                    } else {
                        updateStatus('فشل في إصلاح الإعدادات', 'error');
                    }
                } else {
                    updateStatus('دالة الإصلاح غير متاحة', 'error');
                }
            } catch (error) {
                updateStatus(`خطأ في الإصلاح: ${error.message}`, 'error');
            } finally {
                setTimeout(hideProgress, 2000);
            }
        }

        async function resetSettings() {
            if (!confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) return;

            updateStatus('جاري إعادة تعيين الإعدادات...', 'info');
            showProgress(0, 'بدء إعادة التعيين...');

            try {
                showProgress(25, 'حذف الإعدادات القديمة...');
                localStorage.removeItem('technoflash_settings');
                
                showProgress(50, 'إنشاء إعدادات افتراضية...');
                if (typeof emergencySettingsReset === 'function') {
                    const result = emergencySettingsReset();
                    if (result) {
                        showProgress(100, 'تم إعادة التعيين بنجاح');
                        updateStatus('تم إعادة تعيين الإعدادات بنجاح!', 'success');
                    } else {
                        updateStatus('فشل في إعادة التعيين', 'error');
                    }
                } else {
                    updateStatus('دالة إعادة التعيين غير متاحة', 'error');
                }
            } catch (error) {
                updateStatus(`خطأ في إعادة التعيين: ${error.message}`, 'error');
            } finally {
                setTimeout(hideProgress, 2000);
            }
        }

        async function emergencyReset() {
            const confirmText = prompt('للمتابعة، اكتب "إعادة تعيين طارئة" في الحقل أدناه:');
            if (confirmText !== 'إعادة تعيين طارئة') {
                alert('تم إلغاء العملية');
                return;
            }

            updateStatus('جاري إعادة التعيين الطارئة...', 'warning');
            showProgress(0, 'بدء إعادة التعيين الطارئة...');

            try {
                showProgress(20, 'حذف جميع البيانات...');
                localStorage.clear();
                
                showProgress(40, 'إنشاء إعدادات جديدة...');
                if (typeof fixCorruptedSettings === 'function') {
                    fixCorruptedSettings();
                }
                
                showProgress(60, 'إعادة تهيئة النظام...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showProgress(80, 'التحقق من النظام...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                showProgress(100, 'تم إعادة التعيين الطارئة');
                updateStatus('تم إعادة التعيين الطارئة بنجاح! يُنصح بإعادة تحميل الصفحة.', 'success');
                
                setTimeout(() => {
                    if (confirm('هل تريد إعادة تحميل الصفحة الآن؟')) {
                        location.reload();
                    }
                }, 2000);
                
            } catch (error) {
                updateStatus(`خطأ في إعادة التعيين الطارئة: ${error.message}`, 'error');
            } finally {
                setTimeout(hideProgress, 2000);
            }
        }

        function goToMainSystem() {
            window.location.href = 'index.html';
        }

        // تشغيل تشخيص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(diagnoseSystem, 1000);
        });
    </script>
</body>
</html>
