/**
 * نظام إدارة قاعدة البيانات المحلية - تكنوفلاش
 * يستخدم localStorage لحفظ البيانات محلياً
 */

class TechnoFlashDB {
    constructor() {
        this.dbName = 'technoflash_pos';
        this.version = '1.0.0';
        this.initializeDatabase();
    }

    /**
     * تهيئة قاعدة البيانات مع البيانات الافتراضية
     */
    initializeDatabase() {
        // التحقق من وجود قاعدة البيانات
        if (!localStorage.getItem(this.dbName)) {
            this.createDefaultData();
        }
        
        // التحقق من إصدار قاعدة البيانات
        this.checkVersion();
    }

    /**
     * إنشاء البيانات الافتراضية
     */
    createDefaultData() {
        const defaultData = {
            version: this.version,
            settings: {
                companyName: 'شركتي',
                companyAddress: 'العنوان',
                companyPhone: '٠١٢٣٤٥٦٧٨٩',
                companyEmail: '<EMAIL>',
                taxRate: 15, // نسبة الضريبة
                currency: 'EGP',
                theme: 'light',
                language: 'ar',
                password: this.hashPassword('123'), // كلمة المرور الافتراضية
                autoBackup: true,
                printSettings: {
                    paperSize: 'A4',
                    showLogo: true,
                    showCompanyInfo: true
                }
            },
            products: [
                {
                    id: this.generateId(),
                    name: 'منتج تجريبي',
                    description: 'وصف المنتج التجريبي',
                    price: 100,
                    cost: 80,
                    quantity: 50,
                    category: 'عام',
                    barcode: '1234567890',
                    minStock: 10,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            categories: ['عام', 'إلكترونيات', 'ملابس', 'طعام', 'مشروبات'],
            customers: [
                {
                    id: 'guest',
                    name: 'ضيف',
                    phone: '',
                    email: '',
                    address: '',
                    balance: 0,
                    isDefault: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            suppliers: [],
            sales: [],
            purchases: [],
            payments: [],
            debts: [],
            reports: {
                lastGenerated: null,
                dailySales: {},
                monthlySales: {},
                yearlySales: {}
            }
        };

        this.saveData(defaultData);
    }

    /**
     * التحقق من إصدار قاعدة البيانات
     */
    checkVersion() {
        const data = this.getData();
        if (!data.version || data.version !== this.version) {
            this.upgradeDatabase(data);
        }
    }

    /**
     * ترقية قاعدة البيانات
     */
    upgradeDatabase(data) {
        // إضافة الحقول المفقودة
        data.version = this.version;
        
        // حفظ البيانات المحدثة
        this.saveData(data);
    }

    /**
     * حفظ البيانات في localStorage
     */
    saveData(data) {
        try {
            const jsonData = JSON.stringify(data);
            localStorage.setItem(this.dbName, jsonData);

            // التحقق من نجاح الحفظ
            const savedData = localStorage.getItem(this.dbName);
            if (savedData === jsonData) {
                return true;
            } else {
                console.error('فشل في التحقق من حفظ البيانات');
                return false;
            }
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);

            // معالجة أخطاء محددة
            if (error.name === 'QuotaExceededError') {
                console.error('مساحة التخزين ممتلئة');
                // يمكن إضافة تنبيه للمستخدم هنا
            }

            return false;
        }
    }

    /**
     * استرجاع البيانات من localStorage
     */
    getData() {
        try {
            const data = localStorage.getItem(this.dbName);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return null;
        }
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * تشفير كلمة المرور
     */
    hashPassword(password) {
        // تشفير بسيط - يمكن تحسينه لاحقاً
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return hash.toString();
    }

    /**
     * التحقق من كلمة المرور
     */
    verifyPassword(password) {
        const data = this.getData();
        const hashedPassword = this.hashPassword(password);
        return data.settings.password === hashedPassword;
    }

    /**
     * تحديث كلمة المرور
     */
    updatePassword(newPassword) {
        const data = this.getData();
        data.settings.password = this.hashPassword(newPassword);
        return this.saveData(data);
    }

    /**
     * الحصول على الإعدادات
     */
    getSettings() {
        const data = this.getData();
        return data ? data.settings : null;
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        const data = this.getData();
        data.settings = { ...data.settings, ...newSettings };
        return this.saveData(data);
    }

    /**
     * إضافة منتج جديد
     */
    addProduct(product) {
        const data = this.getData();
        product.id = this.generateId();
        product.createdAt = new Date().toISOString();
        product.updatedAt = new Date().toISOString();

        // إضافة الحقول الافتراضية للمنتجات المبنية على الكرتون
        if (product.isCartonBased) {
            product.unitsPerCarton = product.unitsPerCarton || 1;
            product.cartonCostPrice = product.cartonCostPrice || 0;
            product.unitSalePrice = product.unitSalePrice || 0;
            product.cartonQuantity = product.cartonQuantity || 0;
            product.unitQuantity = product.unitQuantity || 0;

            // حساب إجمالي القطع المتاحة
            product.totalUnitsAvailable = (product.cartonQuantity * product.unitsPerCarton) + product.unitQuantity;

            // حساب متوسط سعر التكلفة للقطعة الواحدة
            product.unitCostPrice = product.cartonCostPrice / product.unitsPerCarton;
        } else {
            product.isCartonBased = false;
        }

        data.products.push(product);
        return this.saveData(data) ? product : null;
    }

    /**
     * الحصول على جميع المنتجات
     */
    getProducts() {
        const data = this.getData();
        return data ? data.products : [];
    }

    /**
     * الحصول على منتج بالمعرف
     */
    getProduct(id) {
        const products = this.getProducts();
        return products.find(product => product.id === id);
    }

    /**
     * الحصول على منتج بالباركود
     */
    getProductByBarcode(barcode) {
        const products = this.getProducts();
        return products.find(product => product.barcode === barcode);
    }

    /**
     * تحديث منتج
     */
    updateProduct(id, updates) {
        const data = this.getData();
        const productIndex = data.products.findIndex(product => product.id === id);
        if (productIndex !== -1) {
            const currentProduct = data.products[productIndex];
            const updatedProduct = {
                ...currentProduct,
                ...updates,
                updatedAt: new Date().toISOString()
            };

            // إعادة حساب البيانات للمنتجات المبنية على الكرتون
            if (updatedProduct.isCartonBased) {
                updatedProduct.unitsPerCarton = updatedProduct.unitsPerCarton || 1;
                updatedProduct.cartonCostPrice = updatedProduct.cartonCostPrice || 0;
                updatedProduct.unitSalePrice = updatedProduct.unitSalePrice || 0;
                updatedProduct.cartonQuantity = updatedProduct.cartonQuantity || 0;
                updatedProduct.unitQuantity = updatedProduct.unitQuantity || 0;

                // حساب إجمالي القطع المتاحة
                updatedProduct.totalUnitsAvailable = (updatedProduct.cartonQuantity * updatedProduct.unitsPerCarton) + updatedProduct.unitQuantity;

                // حساب متوسط سعر التكلفة للقطعة الواحدة
                updatedProduct.unitCostPrice = updatedProduct.cartonCostPrice / updatedProduct.unitsPerCarton;
            }

            data.products[productIndex] = updatedProduct;
            return this.saveData(data) ? updatedProduct : null;
        }
        return null;
    }

    /**
     * حذف منتج
     */
    deleteProduct(id) {
        const data = this.getData();
        const productIndex = data.products.findIndex(product => product.id === id);
        if (productIndex !== -1) {
            data.products.splice(productIndex, 1);
            return this.saveData(data);
        }
        return false;
    }

    /**
     * تحديث مخزون المنتج المبني على الكرتون
     */
    updateCartonBasedStock(productId, cartonChange = 0, unitChange = 0) {
        const product = this.getProduct(productId);
        if (!product || !product.isCartonBased) {
            return false;
        }

        const newCartonQuantity = Math.max(0, product.cartonQuantity + cartonChange);
        const newUnitQuantity = Math.max(0, product.unitQuantity + unitChange);

        return this.updateProduct(productId, {
            cartonQuantity: newCartonQuantity,
            unitQuantity: newUnitQuantity
        });
    }

    /**
     * بيع قطع من منتج مبني على الكرتون
     */
    sellCartonBasedUnits(productId, unitsToSell) {
        const product = this.getProduct(productId);
        if (!product || !product.isCartonBased) {
            return false;
        }

        let remainingUnitsToSell = unitsToSell;
        let newUnitQuantity = product.unitQuantity;
        let newCartonQuantity = product.cartonQuantity;

        // أولاً: بيع من القطع المفردة المتاحة
        if (remainingUnitsToSell > 0 && newUnitQuantity > 0) {
            const unitsFromLoose = Math.min(remainingUnitsToSell, newUnitQuantity);
            newUnitQuantity -= unitsFromLoose;
            remainingUnitsToSell -= unitsFromLoose;
        }

        // ثانياً: فتح كراتين جديدة إذا لزم الأمر
        while (remainingUnitsToSell > 0 && newCartonQuantity > 0) {
            newCartonQuantity -= 1;
            newUnitQuantity += product.unitsPerCarton;

            const unitsFromCarton = Math.min(remainingUnitsToSell, product.unitsPerCarton);
            newUnitQuantity -= unitsFromCarton;
            remainingUnitsToSell -= unitsFromCarton;
        }

        // التحقق من توفر الكمية المطلوبة
        if (remainingUnitsToSell > 0) {
            return false; // لا توجد كمية كافية
        }

        return this.updateProduct(productId, {
            cartonQuantity: newCartonQuantity,
            unitQuantity: newUnitQuantity
        });
    }

    /**
     * بيع كرتونة كاملة
     */
    sellWholeCarton(productId, cartonsToSell = 1) {
        const product = this.getProduct(productId);
        if (!product || !product.isCartonBased || product.cartonQuantity < cartonsToSell) {
            return false;
        }

        return this.updateProduct(productId, {
            cartonQuantity: product.cartonQuantity - cartonsToSell
        });
    }

    /**
     * إضافة عميل جديد
     */
    addCustomer(customer) {
        const data = this.getData();
        customer.id = this.generateId();
        customer.balance = customer.balance || 0;
        customer.createdAt = new Date().toISOString();
        customer.updatedAt = new Date().toISOString();
        data.customers.push(customer);
        return this.saveData(data) ? customer : null;
    }

    /**
     * الحصول على جميع العملاء
     */
    getCustomers() {
        const data = this.getData();
        return data ? data.customers : [];
    }

    /**
     * الحصول على عميل بالمعرف
     */
    getCustomer(id) {
        const customers = this.getCustomers();
        return customers.find(customer => customer.id === id);
    }

    /**
     * تحديث عميل
     */
    updateCustomer(id, updates) {
        const data = this.getData();
        const customerIndex = data.customers.findIndex(customer => customer.id === id);
        if (customerIndex !== -1) {
            data.customers[customerIndex] = {
                ...data.customers[customerIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            return this.saveData(data) ? data.customers[customerIndex] : null;
        }
        return null;
    }

    /**
     * حذف عميل
     */
    deleteCustomer(id) {
        // منع حذف العميل الافتراضي
        if (id === 'guest') return false;
        
        const data = this.getData();
        const customerIndex = data.customers.findIndex(customer => customer.id === id);
        if (customerIndex !== -1) {
            data.customers.splice(customerIndex, 1);
            return this.saveData(data);
        }
        return false;
    }

    /**
     * إضافة مورد جديد
     */
    addSupplier(supplier) {
        const data = this.getData();
        supplier.id = this.generateId();
        supplier.balance = supplier.balance || 0;
        supplier.createdAt = new Date().toISOString();
        supplier.updatedAt = new Date().toISOString();
        data.suppliers.push(supplier);
        return this.saveData(data) ? supplier : null;
    }

    /**
     * الحصول على جميع الموردين
     */
    getSuppliers() {
        const data = this.getData();
        return data ? data.suppliers : [];
    }

    /**
     * الحصول على مورد بالمعرف
     */
    getSupplier(id) {
        const suppliers = this.getSuppliers();
        return suppliers.find(supplier => supplier.id === id);
    }

    /**
     * البحث عن المنتجات
     */
    searchProducts(query) {
        const products = this.getProducts();
        if (!query) {
            return [];
        }
        const lowerCaseQuery = query.toLowerCase();
        return products.filter(product => {
            const productName = product.name ? product.name.toLowerCase() : '';
            const productBarcode = product.barcode ? product.barcode.toLowerCase() : '';
            return productName.includes(lowerCaseQuery) || productBarcode.includes(lowerCaseQuery);
        });
    }

    /**
     * إضافة عملية بيع
     */
    addSale(sale) {
        const data = this.getData();
        sale.id = this.generateId();
        sale.date = new Date().toISOString();

        // تحديث كمية المنتجات
        sale.items.forEach(item => {
            const productIndex = data.products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                const product = data.products[productIndex];

                if (product.isCartonBased) {
                    // للمنتجات المبنية على الكرتون
                    if (item.saleType === 'carton') {
                        // بيع كرتونة كاملة
                        product.cartonQuantity -= item.quantity;
                    } else {
                        // بيع قطع مفردة
                        this.sellCartonBasedUnits(product.id, item.quantity);
                        // إعادة تحميل المنتج بعد التحديث
                        const updatedProduct = this.getProduct(product.id);
                        data.products[productIndex] = updatedProduct;
                    }
                } else {
                    // للمنتجات العادية
                    product.quantity -= item.quantity;
                }
            }
        });

        // تحديث رصيد العميل
        if (sale.paymentMethod === 'credit') {
            const customerIndex = data.customers.findIndex(c => c.id === sale.customerId);
            if (customerIndex !== -1) {
                data.customers[customerIndex].balance += sale.total;
            }
        }

        data.sales.push(sale);
        return this.saveData(data) ? sale : null;
    }

    /**
     * الحصول على جميع المبيعات
     */
    getSales() {
        const data = this.getData();
        return data ? data.sales : [];
    }

    /**
     * الحصول على بيع واحد بالمعرف
     */
    getSale(id) {
        const sales = this.getSales();
        return sales.find(sale => sale.id === id);
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        const data = this.getData();
        if (data) {
            // إزالة كلمة المرور من التصدير لأسباب أمنية
            const exportData = { ...data };
            delete exportData.settings.password;
            return JSON.stringify(exportData, null, 2);
        }
        return null;
    }

    /**
     * استيراد البيانات
     */
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            
            // التحقق من صحة البيانات
            if (!importedData.version || !importedData.settings) {
                throw new Error('بيانات غير صحيحة');
            }

            // الاحتفاظ بكلمة المرور الحالية
            const currentData = this.getData();
            importedData.settings.password = currentData.settings.password;

            return this.saveData(importedData);
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        localStorage.removeItem(this.dbName);
        this.createDefaultData();
        return true;
    }

    /**
     * تحويل الأرقام إلى الأرقام العربية الهندية
     */
    toArabicNumerals(num) {
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return num.toString().replace(/[0-9]/g, (digit) => arabicNumerals[digit]);
    }

    /**
     * تحويل الأرقام العربية الهندية إلى أرقام إنجليزية
     */
    fromArabicNumerals(str) {
        const englishNumerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        
        let result = str;
        arabicNumerals.forEach((arabic, index) => {
            result = result.replace(new RegExp(arabic, 'g'), englishNumerals[index]);
        });
        
        return result;
    }

    /**
     * تنسيق التاريخ بالعربية
     */
    formatDate(date, includeTime = false) {
        const d = new Date(date);
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        
        const day = this.toArabicNumerals(d.getDate());
        const month = months[d.getMonth()];
        const year = this.toArabicNumerals(d.getFullYear());
        
        let formatted = `${day} ${month} ${year}`;
        
        if (includeTime) {
            const hours = this.toArabicNumerals(d.getHours().toString().padStart(2, '0'));
            const minutes = this.toArabicNumerals(d.getMinutes().toString().padStart(2, '0'));
            formatted += ` - ${hours}:${minutes}`;
        }
        
        return formatted;
    }

    /**
     * تنسيق المبلغ بالعملة (الجنيه المصري فقط)
     */
    formatCurrency(amount) {
        // العملة الوحيدة المدعومة: الجنيه المصري
        const currencySymbol = 'ج.م';
        const formattedAmount = this.toArabicNumerals(amount.toFixed(2));
        return `${formattedAmount} ${currencySymbol}`;
    }

    /**
     * تحويل التاريخ الميلادي إلى هجري (تقريبي)
     */
    toHijriDate(gregorianDate) {
        try {
            const gDate = new Date(gregorianDate);
            // تحويل تقريبي للتاريخ الهجري
            const hijriYear = Math.floor((gDate.getFullYear() - 622) * 1.030684);
            const hijriMonths = [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ];
            const hijriMonth = hijriMonths[gDate.getMonth()];
            const hijriDay = this.toArabicNumerals(gDate.getDate());

            return `${hijriDay} ${hijriMonth} ${this.toArabicNumerals(hijriYear)} هـ`;
        } catch (error) {
            return '';
        }
    }

    /**
     * تنسيق التاريخ والوقت
     */
    formatDateTime(dateTime) {
        return this.formatDate(dateTime, true);
    }

    /**
     * الحصول على جميع المشتريات
     */
    getPurchases() {
        const data = this.getData();
        return data ? data.purchases : [];
    }

    /**
     * إضافة مشترى جديد
     */
    addPurchase(purchase) {
        const data = this.getData();
        purchase.id = this.generateId();
        purchase.date = new Date().toISOString();
        data.purchases.push(purchase);
        return this.saveData(data) ? purchase : null;
    }

    /**
     * الحصول على مشترى بالمعرف
     */
    getPurchase(id) {
        const purchases = this.getPurchases();
        return purchases.find(purchase => purchase.id === id);
    }

    /**
     * تحديث مشترى
     */
    updatePurchase(id, updates) {
        const data = this.getData();
        const purchaseIndex = data.purchases.findIndex(purchase => purchase.id === id);
        if (purchaseIndex !== -1) {
            data.purchases[purchaseIndex] = {
                ...data.purchases[purchaseIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            return this.saveData(data) ? data.purchases[purchaseIndex] : null;
        }
        return null;
    }

    /**
     * حذف مشترى
     */
    deletePurchase(id) {
        const data = this.getData();
        const purchaseIndex = data.purchases.findIndex(purchase => purchase.id === id);
        if (purchaseIndex !== -1) {
            data.purchases.splice(purchaseIndex, 1);
            return this.saveData(data);
        }
        return false;
    }

    /**
     * الحصول على جميع المدفوعات
     */
    getPayments() {
        const data = this.getData();
        return data ? data.payments : [];
    }

    /**
     * الحصول على دفعة بالمعرف
     */
    getPayment(id) {
        const payments = this.getPayments();
        return payments.find(payment => payment.id === id);
    }

    /**
     * إضافة دفعة جديدة
     */
    addPayment(payment) {
        const data = this.getData();
        payment.id = this.generateId();
        payment.date = new Date().toISOString();
        data.payments.push(payment);
        return this.saveData(data) ? payment : null;
    }

    /**
     * تحديث دفعة
     */
    updatePayment(id, updates) {
        const data = this.getData();
        const paymentIndex = data.payments.findIndex(payment => payment.id === id);
        if (paymentIndex !== -1) {
            data.payments[paymentIndex] = {
                ...data.payments[paymentIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            return this.saveData(data) ? data.payments[paymentIndex] : null;
        }
        return null;
    }

    /**
     * حذف دفعة
     */
    deletePayment(id) {
        const data = this.getData();
        const paymentIndex = data.payments.findIndex(payment => payment.id === id);
        if (paymentIndex !== -1) {
            data.payments.splice(paymentIndex, 1);
            return this.saveData(data);
        }
        return false;
    }

    /**
     * إضافة معاملة رصيد عميل
     */
    addCustomerBalanceTransaction(customerId, amount, type, description) {
        const data = this.getData();
        const customerIndex = data.customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            if (type === 'debit') {
                data.customers[customerIndex].balance += amount;
            } else {
                data.customers[customerIndex].balance -= amount;
            }

            // إضافة سجل المعاملة
            if (!data.customerTransactions) {
                data.customerTransactions = [];
            }

            data.customerTransactions.push({
                id: this.generateId(),
                customerId: customerId,
                amount: amount,
                type: type,
                description: description,
                date: new Date().toISOString()
            });

            return this.saveData(data);
        }
        return false;
    }

    /**
     * إضافة معاملة رصيد مورد
     */
    addSupplierBalanceTransaction(supplierId, amount, type, description) {
        const data = this.getData();
        const supplierIndex = data.suppliers.findIndex(s => s.id === supplierId);
        if (supplierIndex !== -1) {
            if (type === 'debit') {
                data.suppliers[supplierIndex].balance += amount;
            } else {
                data.suppliers[supplierIndex].balance -= amount;
            }

            // إضافة سجل المعاملة
            if (!data.supplierTransactions) {
                data.supplierTransactions = [];
            }

            data.supplierTransactions.push({
                id: this.generateId(),
                supplierId: supplierId,
                amount: amount,
                type: type,
                description: description,
                date: new Date().toISOString()
            });

            return this.saveData(data);
        }
        return false;
    }

    /**
     * تحديث مورد
     */
    updateSupplier(id, updates) {
        const data = this.getData();
        const supplierIndex = data.suppliers.findIndex(supplier => supplier.id === id);
        if (supplierIndex !== -1) {
            data.suppliers[supplierIndex] = {
                ...data.suppliers[supplierIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            return this.saveData(data) ? data.suppliers[supplierIndex] : null;
        }
        return null;
    }

    /**
     * حذف مورد
     */
    deleteSupplier(id) {
        const data = this.getData();
        const supplierIndex = data.suppliers.findIndex(supplier => supplier.id === id);
        if (supplierIndex !== -1) {
            data.suppliers.splice(supplierIndex, 1);
            return this.saveData(data);
        }
        return false;
    }

    /**
     * الحصول على سجل النسخ الاحتياطي
     */
    getBackupHistory() {
        try {
            const history = localStorage.getItem('backupHistory');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('خطأ في استرجاع سجل النسخ الاحتياطي:', error);
            return [];
        }
    }

    /**
     * حفظ سجل النسخ الاحتياطي
     */
    saveBackupHistory(history) {
        try {
            localStorage.setItem('backupHistory', JSON.stringify(history));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ سجل النسخ الاحتياطي:', error);
            return false;
        }
    }

    /**
     * حفظ الإعدادات المحدثة
     */
    saveSettings(settings) {
        try {
            localStorage.setItem('technoflash_settings', JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    /**
     * الحصول على الإعدادات من localStorage منفصلة
     */
    getStoredSettings() {
        try {
            // البحث في المفتاح الصحيح
            const settings = localStorage.getItem('technoflash_settings');
            return settings ? JSON.parse(settings) : null;
        } catch (error) {
            console.error('خطأ في استرجاع الإعدادات:', error);
            return null;
        }
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const db = new TechnoFlashDB();
