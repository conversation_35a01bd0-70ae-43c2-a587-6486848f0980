<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - تكنوفلاش POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .test-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-check-circle"></i> اختبار نهائي شامل</h1>
            <p>فحص جميع الوظائف والتنسيقات في نظام تكنوفلاش POS</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار المكتبات والوظائف</h3>
            <button class="btn btn-primary test-btn" onclick="testLibraries()">
                <i class="fas fa-book"></i> فحص المكتبات
            </button>
            <button class="btn btn-info test-btn" onclick="testPrintFunctions()">
                <i class="fas fa-print"></i> فحص وظائف الطباعة
            </button>
            <div id="libraryResults"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-table"></i> اختبار تنسيق الجداول</h3>
            <button class="btn btn-success test-btn" onclick="testTableLayout()">
                <i class="fas fa-table"></i> فحص الجداول
            </button>
            <button class="btn btn-warning test-btn" onclick="fixAllTables()">
                <i class="fas fa-wrench"></i> إصلاح الجداول
            </button>
            <div id="tableResults"></div>
            
            <!-- جدول تجريبي -->
            <div class="sales-container mt-4">
                <div class="card">
                    <div class="card-header">
                        <h4>جدول تجريبي</h4>
                    </div>
                    <div class="card-body">
                        <table class="table" id="testTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>منتج تجريبي 1</td>
                                    <td>25.00 ج.م</td>
                                    <td>2</td>
                                    <td>50.00 ج.م</td>
                                </tr>
                                <tr>
                                    <td>منتج تجريبي 2</td>
                                    <td>15.00 ج.م</td>
                                    <td>1</td>
                                    <td>15.00 ج.م</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-print"></i> اختبار الطباعة</h3>
            <button class="btn btn-primary test-btn" onclick="testThermalPrint()">
                <i class="fas fa-receipt"></i> اختبار طباعة حرارية
            </button>
            <button class="btn btn-success test-btn" onclick="testBarcodeLabel()">
                <i class="fas fa-barcode"></i> اختبار ملصق باركود
            </button>
            <div id="printResults"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-shopping-cart"></i> اختبار المبيعات</h3>
            <button class="btn btn-success test-btn" onclick="testSalesFlow()">
                <i class="fas fa-cash-register"></i> اختبار تدفق المبيعات
            </button>
            <div id="salesResults"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-chart-line"></i> النتائج النهائية</h3>
            <button class="btn btn-danger test-btn" onclick="runAllTests()">
                <i class="fas fa-rocket"></i> تشغيل جميع الاختبارات
            </button>
            <div id="finalResults"></div>
        </div>
    </div>

    <!-- تضمين الملفات -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="thermal-printer.js"></script>
    <script src="fix-sales-css.js"></script>
    
    <script>
        // تهيئة النظام
        let db;
        let testResults = {
            libraries: false,
            printFunctions: false,
            tableLayout: false,
            thermalPrint: false,
            barcodeLabel: false,
            salesFlow: false
        };

        // محاكاة app object
        window.app = {
            showAlert: function(title, message) {
                console.log(`ALERT: ${title} - ${message}`);
            },
            showNotification: function(message, type) {
                console.log(`NOTIFICATION (${type}): ${message}`);
            },
            showLoading: function() {
                console.log('LOADING: عرض شاشة التحميل');
            },
            hideLoading: function() {
                console.log('LOADING: إخفاء شاشة التحميل');
            }
        };

        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function testLibraries() {
            const results = document.getElementById('libraryResults');
            results.innerHTML = '';
            
            const libraries = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'Database', check: () => typeof Database !== 'undefined' },
                { name: 'BarcodeGenerator', check: () => typeof BarcodeGenerator !== 'undefined' },
                { name: 'fixSalesCSS', check: () => typeof fixSalesCSS !== 'undefined' },
                { name: 'fixTablesSpecifically', check: () => typeof fixTablesSpecifically !== 'undefined' }
            ];
            
            let allPassed = true;
            libraries.forEach(lib => {
                const available = lib.check();
                if (!available) allPassed = false;
                showResult('libraryResults', 
                    `${available ? '✅' : '❌'} ${lib.name}: ${available ? 'متوفر' : 'غير متوفر'}`,
                    available ? 'success' : 'error'
                );
            });
            
            testResults.libraries = allPassed;
            showResult('libraryResults', 
                allPassed ? '🎉 جميع المكتبات متوفرة!' : '⚠️ بعض المكتبات مفقودة',
                allPassed ? 'success' : 'warning'
            );
        }

        function testPrintFunctions() {
            const results = document.getElementById('libraryResults');
            
            const functions = [
                'printThermalInvoice',
                'printStandardInvoice', 
                'printSaleItemsLabels',
                'testThermalPrinter',
                'sendToThermalPrinter',
                'ESC_POS_COMMANDS'
            ];
            
            let allPassed = true;
            functions.forEach(funcName => {
                const available = typeof window[funcName] !== 'undefined';
                if (!available) allPassed = false;
                showResult('libraryResults',
                    `${available ? '✅' : '❌'} ${funcName}: ${available ? 'متوفر' : 'غير متوفر'}`,
                    available ? 'success' : 'error'
                );
            });
            
            testResults.printFunctions = allPassed;
        }

        function testTableLayout() {
            const results = document.getElementById('tableResults');
            results.innerHTML = '';
            
            const table = document.getElementById('testTable');
            if (!table) {
                showResult('tableResults', '❌ لم يتم العثور على الجدول التجريبي', 'error');
                return;
            }
            
            const computedStyle = window.getComputedStyle(table);
            const parentWidth = table.parentElement.offsetWidth;
            const tableWidth = table.offsetWidth;
            const widthPercentage = (tableWidth / parentWidth * 100).toFixed(2);
            
            showResult('tableResults', 
                `📏 عرض الجدول: ${widthPercentage}% (${tableWidth}px من ${parentWidth}px)`,
                'success'
            );
            
            showResult('tableResults',
                `🎨 CSS width: ${computedStyle.width}`,
                'success'
            );
            
            const isCorrect = computedStyle.width === '100%' && widthPercentage >= 95;
            testResults.tableLayout = isCorrect;
            
            showResult('tableResults',
                isCorrect ? '✅ الجدول يعمل بشكل صحيح!' : '❌ الجدول يحتاج إصلاح',
                isCorrect ? 'success' : 'error'
            );
        }

        function fixAllTables() {
            const results = document.getElementById('tableResults');
            showResult('tableResults', '🔧 جاري إصلاح الجداول...', 'warning');
            
            if (typeof fixTablesSpecifically === 'function') {
                fixTablesSpecifically();
                setTimeout(() => {
                    showResult('tableResults', '✅ تم تطبيق إصلاحات الجداول!', 'success');
                    setTimeout(testTableLayout, 500);
                }, 1000);
            } else {
                showResult('tableResults', '❌ وظيفة إصلاح الجداول غير متوفرة!', 'error');
            }
        }

        function testThermalPrint() {
            const results = document.getElementById('printResults');
            results.innerHTML = '';
            
            if (typeof testThermalPrinter === 'function') {
                showResult('printResults', '🖨️ جاري اختبار الطباعة الحرارية...', 'warning');
                try {
                    testThermalPrinter();
                    testResults.thermalPrint = true;
                    showResult('printResults', '✅ تم إرسال اختبار الطباعة الحرارية!', 'success');
                } catch (error) {
                    testResults.thermalPrint = false;
                    showResult('printResults', `❌ فشل اختبار الطباعة: ${error.message}`, 'error');
                }
            } else {
                testResults.thermalPrint = false;
                showResult('printResults', '❌ وظيفة اختبار الطباعة غير متوفرة', 'error');
            }
        }

        function testBarcodeLabel() {
            const results = document.getElementById('printResults');
            
            if (typeof printSmallBarcodeLabel === 'function') {
                showResult('printResults', '🏷️ جاري اختبار ملصق الباركود...', 'warning');
                try {
                    const testProduct = {
                        name: 'منتج تجريبي للاختبار',
                        barcode: 'TEST123456789',
                        price: 99.99
                    };
                    printSmallBarcodeLabel(testProduct);
                    testResults.barcodeLabel = true;
                    showResult('printResults', '✅ تم إرسال ملصق الباركود للطباعة!', 'success');
                } catch (error) {
                    testResults.barcodeLabel = false;
                    showResult('printResults', `❌ فشل في طباعة الملصق: ${error.message}`, 'error');
                }
            } else {
                testResults.barcodeLabel = false;
                showResult('printResults', '❌ وظيفة طباعة الملصقات غير متوفرة', 'error');
            }
        }

        function testSalesFlow() {
            const results = document.getElementById('salesResults');
            results.innerHTML = '';
            
            try {
                // تهيئة قاعدة البيانات
                if (!db) {
                    db = new Database();
                }
                
                showResult('salesResults', '✅ تم تهيئة قاعدة البيانات', 'success');
                
                // اختبار إنشاء بيع
                const testSale = {
                    customerId: 'guest',
                    items: [{
                        productId: 'test1',
                        name: 'منتج تجريبي',
                        quantity: 1,
                        price: 50.00,
                        total: 50.00
                    }],
                    subtotal: 50.00,
                    total: 50.00,
                    paymentMethod: 'cash'
                };
                
                const savedSale = db.addSale(testSale);
                if (savedSale) {
                    testResults.salesFlow = true;
                    showResult('salesResults', '✅ تم إنشاء بيع تجريبي بنجاح', 'success');
                    showResult('salesResults', `📄 معرف البيع: ${savedSale.id}`, 'success');
                } else {
                    testResults.salesFlow = false;
                    showResult('salesResults', '❌ فشل في إنشاء البيع', 'error');
                }
                
            } catch (error) {
                testResults.salesFlow = false;
                showResult('salesResults', `❌ خطأ في اختبار المبيعات: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            const results = document.getElementById('finalResults');
            results.innerHTML = '';
            
            showResult('finalResults', '🚀 بدء الاختبار الشامل...', 'warning');
            
            // تشغيل جميع الاختبارات
            testLibraries();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testPrintFunctions();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testTableLayout();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testThermalPrint();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            testBarcodeLabel();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            testSalesFlow();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // عرض النتائج النهائية
            const passedTests = Object.values(testResults).filter(result => result).length;
            const totalTests = Object.keys(testResults).length;
            const successRate = (passedTests / totalTests * 100).toFixed(1);
            
            showResult('finalResults', 
                `📊 النتائج النهائية: ${passedTests}/${totalTests} اختبارات نجحت (${successRate}%)`,
                passedTests === totalTests ? 'success' : 'warning'
            );
            
            Object.entries(testResults).forEach(([test, result]) => {
                const testNames = {
                    libraries: 'المكتبات',
                    printFunctions: 'وظائف الطباعة',
                    tableLayout: 'تخطيط الجداول',
                    thermalPrint: 'الطباعة الحرارية',
                    barcodeLabel: 'ملصقات الباركود',
                    salesFlow: 'تدفق المبيعات'
                };
                
                showResult('finalResults',
                    `${result ? '✅' : '❌'} ${testNames[test]}: ${result ? 'نجح' : 'فشل'}`,
                    result ? 'success' : 'error'
                );
            });
            
            if (passedTests === totalTests) {
                showResult('finalResults', '🎉 تهانينا! جميع الاختبارات نجحت. النظام جاهز للاستخدام!', 'success');
            } else {
                showResult('finalResults', '⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء وإصلاحها.', 'warning');
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة الاختبار النهائي');
            setTimeout(() => {
                testLibraries();
                testTableLayout();
            }, 1000);
        });
    </script>
</body>
</html>
