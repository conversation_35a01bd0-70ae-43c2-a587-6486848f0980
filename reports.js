/**
 * إدارة التقارير - تكنوفلاش
 */

// مستمع أحداث تحديث الإعدادات
window.addEventListener('settingsUpdated', function(event) {
    const settings = event.detail;
    console.log('تم استلام تحديث الإعدادات في التقارير:', settings);

    // تحديث الوضع الليلي
    if (settings.general && typeof settings.general.darkMode !== 'undefined') {
        if (settings.general.darkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        console.log('تم تحديث الوضع الليلي في التقارير:', settings.general.darkMode);
    }

    // تحديث العملة في واجهة التقارير
    if (settings.general && settings.general.currency) {
        updateReportsCurrency(settings.general.currency);
    }

    // تحديث نوع الأرقام
    if (settings.general && settings.general.numberType) {
        updateReportsNumbers(settings.general.numberType);
    }
});

let reportsData = {};
let currentChart = null;

/**
 * تحميل صفحة التقارير
 */
async function loadReports() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-sync"></i> تحديث التقارير
                </button>
                <button class="btn btn-info" onclick="exportReport()">
                    <i class="fas fa-download"></i> تصدير التقرير
                </button>
            </div>
        </div>

        <!-- فلاتر التقارير -->
        <div class="filters-section">
            <div class="card">
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label>نوع التقرير</label>
                            <select class="form-control" id="reportType" onchange="updateReportFilters()">
                                <option value="sales">تقرير المبيعات</option>
                                <option value="purchases">تقرير المشتريات</option>
                                <option value="products">تقرير المنتجات</option>
                                <option value="customers">تقرير العملاء</option>
                                <option value="suppliers">تقرير الموردين</option>
                                <option value="financial">التقرير المالي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الفترة الزمنية</label>
                            <select class="form-control" id="timePeriod" onchange="updateDateRange()">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                        <div class="form-group" id="customDateRange" style="display: none;">
                            <label>من تاريخ</label>
                            <input type="date" class="form-control" id="dateFrom">
                        </div>
                        <div class="form-group" id="customDateRange2" style="display: none;">
                            <label>إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateTo">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-value" id="totalSalesAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي المبيعات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-value" id="totalPurchasesAmount">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي المشتريات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value" id="totalProfit">٠.٠٠ ر.س</div>
                <div class="stat-label">إجمالي الربح</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-value" id="profitMargin">٠%</div>
                <div class="stat-label">هامش الربح</div>
            </div>
        </div>

        <!-- محتوى التقرير -->
        <div class="report-content">
            <!-- الرسوم البيانية -->
            <div class="charts-section">
                <div class="card">
                    <div class="card-header">
                        <h3 id="chartTitle">الرسم البياني</h3>
                        <div class="chart-controls">
                            <select class="form-control" id="chartType" onchange="updateChart()">
                                <option value="line">خط</option>
                                <option value="bar">أعمدة</option>
                                <option value="pie">دائري</option>
                                <option value="doughnut">حلقي</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="reportChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- جدول التقرير -->
            <div class="table-section">
                <div class="card">
                    <div class="card-header">
                        <h3 id="tableTitle">بيانات التقرير</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table" id="reportTable">
                                <thead id="reportTableHead">
                                    <!-- سيتم تحميل العناوين هنا -->
                                </thead>
                                <tbody id="reportTableBody">
                                    <!-- سيتم تحميل البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل التقرير -->
        <div id="reportDetailsModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>تفاصيل التقرير</h3>
                    <button class="modal-close" onclick="app.hideModal('reportDetailsModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="reportDetailsContent">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeReportsPage();
}

/**
 * تهيئة صفحة التقارير
 */
async function initializeReportsPage() {
    // تحديث نطاق التاريخ الافتراضي
    updateDateRange();
    
    // توليد التقرير الافتراضي
    generateReport();
}

/**
 * تحديث فلاتر التقرير
 */
function updateReportFilters() {
    const reportType = document.getElementById('reportType').value;
    
    // يمكن إضافة فلاتر خاصة لكل نوع تقرير هنا
    generateReport();
}

/**
 * تحديث نطاق التاريخ
 */
function updateDateRange() {
    const timePeriod = document.getElementById('timePeriod').value;
    const customDateRange = document.getElementById('customDateRange');
    const customDateRange2 = document.getElementById('customDateRange2');
    const dateFrom = document.getElementById('dateFrom');
    const dateTo = document.getElementById('dateTo');
    
    if (timePeriod === 'custom') {
        customDateRange.style.display = 'block';
        customDateRange2.style.display = 'block';
    } else {
        customDateRange.style.display = 'none';
        customDateRange2.style.display = 'none';
        
        const today = new Date();
        let startDate, endDate = today;
        
        switch (timePeriod) {
            case 'today':
                startDate = new Date(today);
                break;
            case 'week':
                startDate = new Date(today);
                startDate.setDate(today.getDate() - 7);
                break;
            case 'month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                break;
            case 'quarter':
                const quarter = Math.floor(today.getMonth() / 3);
                startDate = new Date(today.getFullYear(), quarter * 3, 1);
                break;
            case 'year':
                startDate = new Date(today.getFullYear(), 0, 1);
                break;
        }
        
        dateFrom.value = startDate.toISOString().split('T')[0];
        dateTo.value = endDate.toISOString().split('T')[0];
    }
    
    generateReport();
}

/**
 * توليد التقرير
 */
function generateReport() {
    console.log('بدء توليد التقرير');

    const reportType = document.getElementById('reportType').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;

    if (!dateFrom || !dateTo) {
        app.showAlert('خطأ', 'يرجى تحديد الفترة الزمنية');
        return;
    }

    console.log('نوع التقرير:', reportType, 'من:', dateFrom, 'إلى:', dateTo);

    // التحقق من وجود app
    if (typeof app === 'undefined') {
        console.error('متغير app غير معرف');
        alert('حدث خطأ في النظام');
        return;
    }

    app.showLoading();

    try {
        // للتأكد من تضمين اليوم الأخير بالكامل في الفلتر، نضيف الوقت
        const inclusiveDateTo = dateTo + 'T23:59:59.999Z';

        console.log('التاريخ المعدل:', inclusiveDateTo);

        // جمع البيانات حسب نوع التقرير
        switch (reportType) {
            case 'sales':
                console.log('توليد تقرير المبيعات');
                generateSalesReport(dateFrom, inclusiveDateTo);
                break;
            case 'purchases':
                console.log('توليد تقرير المشتريات');
                generatePurchasesReport(dateFrom, inclusiveDateTo);
                break;
            case 'products':
                console.log('توليد تقرير المنتجات');
                generateProductsReport(dateFrom, inclusiveDateTo);
                break;
            case 'customers':
                console.log('توليد تقرير العملاء');
                generateCustomersReport(dateFrom, inclusiveDateTo);
                break;
            case 'suppliers':
                console.log('توليد تقرير الموردين');
                generateSuppliersReport(dateFrom, inclusiveDateTo);
                break;
            case 'financial':
                console.log('توليد التقرير المالي');
                generateFinancialReport(dateFrom, inclusiveDateTo);
                break;
            default:
                throw new Error('نوع تقرير غير صحيح: ' + reportType);
        }

        // تحديث الإحصائيات السريعة
        console.log('تحديث الإحصائيات السريعة');
        updateQuickStats(dateFrom, inclusiveDateTo);

        console.log('تم توليد التقرير بنجاح');

    } catch (error) {
        console.error('خطأ في توليد التقرير:', error);

        let errorMessage = 'حدث خطأ في توليد التقرير';
        if (error.message) {
            errorMessage += ': ' + error.message;
        }

        app.showAlert('خطأ', errorMessage);
    } finally {
        app.hideLoading();
    }
}

/**
 * تحديث الإحصائيات السريعة
 */
function updateQuickStats(dateFrom, dateTo) {
    try {
        console.log('تحديث الإحصائيات السريعة للفترة:', dateFrom, 'إلى', dateTo);

        // التحقق من وجود قاعدة البيانات
        if (typeof db === 'undefined') {
            throw new Error('قاعدة البيانات غير متاحة');
        }

        const allSales = db.getSales() || [];
        const allPurchases = db.getPurchases() || [];

        console.log('إجمالي المبيعات:', allSales.length, 'إجمالي المشتريات:', allPurchases.length);

        const sales = allSales.filter(sale =>
            sale.date && sale.date >= dateFrom && sale.date <= dateTo
        );

        const purchases = allPurchases.filter(purchase =>
            purchase.date && purchase.date >= dateFrom && purchase.date <= dateTo
        );

        console.log('المبيعات المفلترة:', sales.length, 'المشتريات المفلترة:', purchases.length);

        const totalSales = sales.reduce((sum, sale) => sum + (sale.total || 0), 0);
        const totalPurchases = purchases.reduce((sum, purchase) => sum + (purchase.total || 0), 0);

        // حساب الربح الصحيح بناءً على المنتجات المباعة
        const totalProfit = calculateActualProfit(sales);
        const profitMargin = totalSales > 0 ? ((totalProfit / totalSales) * 100) : 0;

        console.log('الإحصائيات:', {
            totalSales,
            totalPurchases,
            totalProfit,
            profitMargin
        });

        // تحديث العناصر في الواجهة
        const totalSalesElement = document.getElementById('totalSalesAmount');
        const totalPurchasesElement = document.getElementById('totalPurchasesAmount');
        const totalProfitElement = document.getElementById('totalProfit');
        const profitMarginElement = document.getElementById('profitMargin');

        if (totalSalesElement) {
            totalSalesElement.textContent = db.formatCurrency(totalSales);
        }
        if (totalPurchasesElement) {
            totalPurchasesElement.textContent = db.formatCurrency(totalPurchases);
        }
        if (totalProfitElement) {
            totalProfitElement.textContent = db.formatCurrency(totalProfit);
        }
        if (profitMarginElement) {
            profitMarginElement.textContent = db.toArabicNumerals(profitMargin.toFixed(1)) + '%';
        }

        console.log('تم تحديث الإحصائيات السريعة بنجاح');

    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات السريعة:', error);

        // عرض قيم افتراضية في حالة الخطأ
        const elements = [
            'totalSalesAmount',
            'totalPurchasesAmount',
            'totalProfit',
            'profitMargin'
        ];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = id === 'profitMargin' ? '0%' : db.formatCurrency(0);
            }
        });
    }
}

/**
 * توليد تقرير المبيعات
 */
function generateSalesReport(dateFrom, dateTo) {
    try {
        console.log('توليد تقرير المبيعات للفترة:', dateFrom, 'إلى', dateTo);

        const allSales = db.getSales() || [];
        console.log('إجمالي المبيعات في قاعدة البيانات:', allSales.length);

        const sales = allSales.filter(sale =>
            sale.date && sale.date >= dateFrom && sale.date <= dateTo
        );

        console.log('المبيعات المفلترة:', sales.length);

        // إعداد الجدول
        const tableTitleElement = document.getElementById('tableTitle');
        const chartTitleElement = document.getElementById('chartTitle');

        if (tableTitleElement) tableTitleElement.textContent = 'تقرير المبيعات';
        if (chartTitleElement) chartTitleElement.textContent = 'مخطط المبيعات';

        const tableHead = document.getElementById('reportTableHead');
        const tableBody = document.getElementById('reportTableBody');

        if (!tableHead || !tableBody) {
            throw new Error('عناصر الجدول غير موجودة');
        }

        tableHead.innerHTML = `
            <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>عدد الأصناف</th>
                <th>الإجمالي</th>
                <th>المدفوع</th>
                <th>المتبقي</th>
            </tr>
        `;

        if (sales.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">لا توجد مبيعات في الفترة المحددة</td>
                </tr>
            `;

            // بيانات فارغة للرسم البياني
            reportsData = {
                type: 'sales',
                chartData: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        label: 'المبيعات اليومية',
                        data: [0],
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2
                    }]
                }
            };
        } else {
            tableBody.innerHTML = sales.map(sale => {
                const customer = db.getCustomer(sale.customerId);
                const paidAmount = sale.paidAmount || 0;
                const remaining = (sale.total || 0) - paidAmount;

                return `
                    <tr>
                        <td>${sale.invoiceNumber || sale.id?.substr(-6) || 'غير محدد'}</td>
                        <td>${customer ? customer.name : 'عميل نقدي'}</td>
                        <td>${db.formatDate(sale.date)}</td>
                        <td>${db.toArabicNumerals((sale.items || []).length)}</td>
                        <td>${db.formatCurrency(sale.total || 0)}</td>
                        <td>${db.formatCurrency(paidAmount)}</td>
                        <td class="${remaining > 0 ? 'debt' : 'clear'}">
                            ${db.formatCurrency(remaining)}
                        </td>
                    </tr>
                `;
            }).join('');

            // إعداد البيانات للرسم البياني
            const dailySales = groupSalesByDate(sales);
            reportsData = {
                type: 'sales',
                chartData: {
                    labels: Object.keys(dailySales).map(date => db.formatDate(date)),
                    datasets: [{
                        label: 'المبيعات اليومية',
                        data: Object.values(dailySales),
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2
                    }]
                }
            };
        }

        console.log('تم إعداد بيانات تقرير المبيعات');
        updateChart();

    } catch (error) {
        console.error('خطأ في توليد تقرير المبيعات:', error);
        throw error;
    }
}

/**
 * توليد تقرير المشتريات
 */
function generatePurchasesReport(dateFrom, dateTo) {
    const purchases = db.getPurchases().filter(purchase =>
        purchase.date >= dateFrom && purchase.date <= dateTo
    );

    // إعداد الجدول
    document.getElementById('tableTitle').textContent = 'تقرير المشتريات';
    document.getElementById('chartTitle').textContent = 'مخطط المشتريات';

    const tableHead = document.getElementById('reportTableHead');
    const tableBody = document.getElementById('reportTableBody');

    tableHead.innerHTML = `
        <tr>
            <th>رقم الفاتورة</th>
            <th>المورد</th>
            <th>التاريخ</th>
            <th>عدد الأصناف</th>
            <th>الإجمالي</th>
            <th>المدفوع</th>
            <th>المتبقي</th>
        </tr>
    `;

    tableBody.innerHTML = purchases.map(purchase => {
        const supplier = db.getSupplier(purchase.supplierId);
        const paidAmount = purchase.paidAmount || 0;
        const remaining = purchase.total - paidAmount;

        return `
            <tr>
                <td>${purchase.invoiceNumber}</td>
                <td>${supplier ? supplier.name : 'غير محدد'}</td>
                <td>${db.formatDate(purchase.date)}</td>
                <td>${db.toArabicNumerals(purchase.items.length)}</td>
                <td>${db.formatCurrency(purchase.total)}</td>
                <td>${db.formatCurrency(paidAmount)}</td>
                <td class="${remaining > 0 ? 'debt' : 'clear'}">
                    ${db.formatCurrency(remaining)}
                </td>
            </tr>
        `;
    }).join('');

    // إعداد البيانات للرسم البياني
    const dailyPurchases = groupPurchasesByDate(purchases);
    reportsData = {
        type: 'purchases',
        chartData: {
            labels: Object.keys(dailyPurchases).map(date => db.formatDate(date)),
            datasets: [{
                label: 'المشتريات اليومية',
                data: Object.values(dailyPurchases),
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2
            }]
        }
    };

    updateChart();
}

/**
 * توليد تقرير المنتجات
 */
function generateProductsReport(dateFrom, dateTo) {
    const products = db.getProducts();
    const sales = db.getSales().filter(sale =>
        sale.date >= dateFrom && sale.date <= dateTo
    );

    // حساب مبيعات كل منتج
    const productSales = {};
    sales.forEach(sale => {
        sale.items.forEach(item => {
            if (!productSales[item.productId]) {
                productSales[item.productId] = {
                    quantity: 0,
                    total: 0
                };
            }
            productSales[item.productId].quantity += item.quantity;
            productSales[item.productId].total += item.total;
        });
    });

    // إعداد الجدول
    document.getElementById('tableTitle').textContent = 'تقرير المنتجات';
    document.getElementById('chartTitle').textContent = 'أكثر المنتجات مبيعاً';

    const tableHead = document.getElementById('reportTableHead');
    const tableBody = document.getElementById('reportTableBody');

    tableHead.innerHTML = `
        <tr>
            <th>المنتج</th>
            <th>الفئة</th>
            <th>المخزون الحالي</th>
            <th>الكمية المباعة</th>
            <th>إجمالي المبيعات</th>
            <th>متوسط السعر</th>
        </tr>
    `;

    const productReportData = products.map(product => {
        const sales = productSales[product.id] || { quantity: 0, total: 0 };
        const avgPrice = sales.quantity > 0 ? sales.total / sales.quantity : 0;

        return {
            product,
            sales,
            avgPrice
        };
    }).sort((a, b) => b.sales.total - a.sales.total);

    tableBody.innerHTML = productReportData.map(data => `
        <tr>
            <td><strong>${data.product.name}</strong></td>
            <td>${data.product.category || '-'}</td>
            <td>${db.toArabicNumerals(data.product.quantity)}</td>
            <td>${db.toArabicNumerals(data.sales.quantity)}</td>
            <td>${db.formatCurrency(data.sales.total)}</td>
            <td>${db.formatCurrency(data.avgPrice)}</td>
        </tr>
    `).join('');

    // إعداد البيانات للرسم البياني (أفضل 10 منتجات)
    const topProducts = productReportData.slice(0, 10);
    reportsData = {
        type: 'products',
        chartData: {
            labels: topProducts.map(data => data.product.name),
            datasets: [{
                label: 'إجمالي المبيعات',
                data: topProducts.map(data => data.sales.total),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)',
                    'rgba(199, 199, 199, 0.8)',
                    'rgba(83, 102, 255, 0.8)',
                    'rgba(255, 99, 255, 0.8)',
                    'rgba(99, 255, 132, 0.8)'
                ]
            }]
        }
    };

    updateChart();
}

/**
 * توليد تقرير العملاء
 */
function generateCustomersReport(dateFrom, dateTo) {
    const customers = db.getCustomers();
    const sales = db.getSales().filter(sale =>
        sale.date >= dateFrom && sale.date <= dateTo
    );

    // حساب مبيعات كل عميل
    const customerSales = {};
    sales.forEach(sale => {
        const customerId = sale.customerId || 'cash';
        if (!customerSales[customerId]) {
            customerSales[customerId] = {
                count: 0,
                total: 0,
                paid: 0
            };
        }
        customerSales[customerId].count++;
        customerSales[customerId].total += sale.total;
        customerSales[customerId].paid += sale.paidAmount;
    });

    // إعداد الجدول
    document.getElementById('tableTitle').textContent = 'تقرير العملاء';
    document.getElementById('chartTitle').textContent = 'أفضل العملاء';

    const tableHead = document.getElementById('reportTableHead');
    const tableBody = document.getElementById('reportTableBody');

    tableHead.innerHTML = `
        <tr>
            <th>العميل</th>
            <th>الهاتف</th>
            <th>عدد الفواتير</th>
            <th>إجمالي المبيعات</th>
            <th>إجمالي المدفوع</th>
            <th>الرصيد المتبقي</th>
        </tr>
    `;

    const customerReportData = customers.map(customer => {
        const sales = customerSales[customer.id] || { count: 0, total: 0, paid: 0 };
        const paidAmount = sales.paid || 0;
        const remaining = sales.total - paidAmount;

        return {
            customer,
            sales,
            remaining
        };
    }).filter(data => data.sales.count > 0)
      .sort((a, b) => b.sales.total - a.sales.total);

    // إضافة العملاء النقديين
    if (customerSales['cash']) {
        customerReportData.push({
            customer: { name: 'عملاء نقديون', phone: '-' },
            sales: customerSales['cash'],
            remaining: 0
        });
    }

    tableBody.innerHTML = customerReportData.map(data => `
        <tr>
            <td><strong>${data.customer.name}</strong></td>
            <td>${data.customer.phone || '-'}</td>
            <td>${db.toArabicNumerals(data.sales.count)}</td>
            <td>${db.formatCurrency(data.sales.total)}</td>
            <td>${db.formatCurrency(data.sales.paid)}</td>
            <td class="${data.remaining > 0 ? 'debt' : 'clear'}">
                ${db.formatCurrency(data.remaining)}
            </td>
        </tr>
    `).join('');

    // إعداد البيانات للرسم البياني (أفضل 10 عملاء)
    const topCustomers = customerReportData.slice(0, 10);
    reportsData = {
        type: 'customers',
        chartData: {
            labels: topCustomers.map(data => data.customer.name),
            datasets: [{
                label: 'إجمالي المبيعات',
                data: topCustomers.map(data => data.sales.total),
                backgroundColor: 'rgba(75, 192, 192, 0.8)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        }
    };

    updateChart();
}

/**
 * توليد تقرير الموردين
 */
function generateSuppliersReport(dateFrom, dateTo) {
    const suppliers = db.getSuppliers();
    const purchases = db.getPurchases().filter(purchase =>
        purchase.date >= dateFrom && purchase.date <= dateTo
    );

    // حساب مشتريات كل مورد
    const supplierPurchases = {};
    purchases.forEach(purchase => {
        if (!supplierPurchases[purchase.supplierId]) {
            supplierPurchases[purchase.supplierId] = {
                count: 0,
                total: 0,
                paid: 0
            };
        }
        supplierPurchases[purchase.supplierId].count++;
        supplierPurchases[purchase.supplierId].total += purchase.total;
        supplierPurchases[purchase.supplierId].paid += purchase.paidAmount;
    });

    // إعداد الجدول
    document.getElementById('tableTitle').textContent = 'تقرير الموردين';
    document.getElementById('chartTitle').textContent = 'أكبر الموردين';

    const tableHead = document.getElementById('reportTableHead');
    const tableBody = document.getElementById('reportTableBody');

    tableHead.innerHTML = `
        <tr>
            <th>المورد</th>
            <th>الهاتف</th>
            <th>عدد الفواتير</th>
            <th>إجمالي المشتريات</th>
            <th>إجمالي المدفوع</th>
            <th>الرصيد المتبقي</th>
        </tr>
    `;

    const supplierReportData = suppliers.map(supplier => {
        const purchases = supplierPurchases[supplier.id] || { count: 0, total: 0, paid: 0 };
        const paidAmount = purchases.paid || 0;
        const remaining = purchases.total - paidAmount;

        return {
            supplier,
            purchases,
            remaining
        };
    }).filter(data => data.purchases.count > 0)
      .sort((a, b) => b.purchases.total - a.purchases.total);

    tableBody.innerHTML = supplierReportData.map(data => `
        <tr>
            <td><strong>${data.supplier.name}</strong></td>
            <td>${data.supplier.phone || '-'}</td>
            <td>${db.toArabicNumerals(data.purchases.count)}</td>
            <td>${db.formatCurrency(data.purchases.total)}</td>
            <td>${db.formatCurrency(data.purchases.paid)}</td>
            <td class="${data.remaining > 0 ? 'debt' : 'clear'}">
                ${db.formatCurrency(data.remaining)}
            </td>
        </tr>
    `).join('');

    // إعداد البيانات للرسم البياني
    const topSuppliers = supplierReportData.slice(0, 10);
    reportsData = {
        type: 'suppliers',
        chartData: {
            labels: topSuppliers.map(data => data.supplier.name),
            datasets: [{
                label: 'إجمالي المشتريات',
                data: topSuppliers.map(data => data.purchases.total),
                backgroundColor: 'rgba(255, 159, 64, 0.8)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        }
    };

    updateChart();
}

/**
 * توليد التقرير المالي
 */
function generateFinancialReport(dateFrom, dateTo) {
    const sales = db.getSales().filter(sale =>
        sale.date >= dateFrom && sale.date <= dateTo
    );
    const purchases = db.getPurchases().filter(purchase =>
        purchase.date >= dateFrom && purchase.date <= dateTo
    );
    const payments = db.getPayments().filter(payment =>
        payment.date >= dateFrom && payment.date <= dateTo
    );

    // حساب الإحصائيات المالية
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const grossProfit = totalSales - totalPurchases;
    const profitMargin = totalSales > 0 ? (grossProfit / totalSales) * 100 : 0;

    // إعداد الجدول
    document.getElementById('tableTitle').textContent = 'التقرير المالي';
    document.getElementById('chartTitle').textContent = 'الأداء المالي';

    const tableHead = document.getElementById('reportTableHead');
    const tableBody = document.getElementById('reportTableBody');

    tableHead.innerHTML = `
        <tr>
            <th>البيان</th>
            <th>المبلغ</th>
            <th>النسبة</th>
        </tr>
    `;

    const financialData = [
        { label: 'إجمالي المبيعات', amount: totalSales, percentage: 100 },
        { label: 'إجمالي المشتريات', amount: totalPurchases, percentage: totalSales > 0 ? (totalPurchases / totalSales) * 100 : 0 },
        { label: 'إجمالي الربح', amount: grossProfit, percentage: profitMargin },
        { label: 'إجمالي المدفوعات', amount: totalPayments, percentage: totalSales > 0 ? (totalPayments / totalSales) * 100 : 0 }
    ];

    tableBody.innerHTML = financialData.map(data => `
        <tr>
            <td><strong>${data.label}</strong></td>
            <td class="${data.amount < 0 ? 'debt' : ''}">${db.formatCurrency(data.amount)}</td>
            <td>${db.toArabicNumerals(data.percentage.toFixed(1))}%</td>
        </tr>
    `).join('');

    // إعداد البيانات للرسم البياني
    reportsData = {
        type: 'financial',
        chartData: {
            labels: ['المبيعات', 'المشتريات', 'الربح', 'المدفوعات'],
            datasets: [{
                label: 'المبالغ المالية',
                data: [totalSales, totalPurchases, grossProfit, totalPayments],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 205, 86, 0.8)'
                ]
            }]
        }
    };

    updateChart();
}

/**
 * حساب الربح الفعلي بناءً على المنتجات المباعة
 */
function calculateActualProfit(sales) {
    let totalProfit = 0;

    try {
        console.log('حساب الربح الفعلي للمبيعات:', sales.length);

        sales.forEach(sale => {
            if (!sale.items || !Array.isArray(sale.items)) {
                console.warn('بيانات المبيعات غير صحيحة:', sale.id);
                return;
            }

            sale.items.forEach(item => {
                const product = db.getProduct(item.productId);
                if (!product) {
                    console.warn('منتج غير موجود:', item.productId);
                    return;
                }

                // الحصول على تكلفة المنتج
                const costPrice = product.cost || product.costPrice || 0;
                const sellPrice = item.price || product.price || 0;
                const quantity = item.quantity || 0;

                // حساب الربح لهذا المنتج = (سعر البيع - التكلفة) × الكمية
                const itemProfit = (sellPrice - costPrice) * quantity;
                totalProfit += itemProfit;

                console.log(`منتج: ${product.name}, الكمية: ${quantity}, سعر البيع: ${sellPrice}, التكلفة: ${costPrice}, الربح: ${itemProfit}`);
            });
        });

        console.log('إجمالي الربح الفعلي:', totalProfit);

    } catch (error) {
        console.error('خطأ في حساب الربح الفعلي:', error);
        totalProfit = 0;
    }

    return totalProfit;
}

/**
 * تجميع المبيعات حسب التاريخ
 */
function groupSalesByDate(sales) {
    const grouped = {};
    sales.forEach(sale => {
        const date = sale.date.split('T')[0]; // الحصول على التاريخ فقط
        if (!grouped[date]) {
            grouped[date] = 0;
        }
        grouped[date] += sale.total;
    });
    return grouped;
}

/**
 * تجميع المشتريات حسب التاريخ
 */
function groupPurchasesByDate(purchases) {
    const grouped = {};
    purchases.forEach(purchase => {
        const date = purchase.date.split('T')[0]; // الحصول على التاريخ فقط
        if (!grouped[date]) {
            grouped[date] = 0;
        }
        grouped[date] += purchase.total;
    });
    return grouped;
}

/**
 * تحديث الرسم البياني
 */
function updateChart() {
    try {
        console.log('بدء تحديث الرسم البياني');

        if (!reportsData || !reportsData.chartData) {
            console.log('لا توجد بيانات للرسم البياني');
            return;
        }

        const chartElement = document.getElementById('reportChart');
        if (!chartElement) {
            console.error('عنصر الرسم البياني غير موجود');
            return;
        }

        const ctx = chartElement.getContext('2d');
        const chartTypeElement = document.getElementById('chartType');
        const chartType = chartTypeElement ? chartTypeElement.value : 'bar';

        console.log('نوع الرسم البياني:', chartType);
        console.log('بيانات الرسم البياني:', reportsData.chartData);

        // تدمير الرسم البياني السابق
        if (currentChart) {
            console.log('تدمير الرسم البياني السابق');
            currentChart.destroy();
            currentChart = null;
        }

        // التحقق من وجود مكتبة Chart.js
        if (typeof Chart === 'undefined') {
            console.error('مكتبة Chart.js غير محملة');

            // عرض رسالة بديلة
            const chartContainer = document.querySelector('.chart-container');
            if (chartContainer) {
                chartContainer.innerHTML = `
                    <div class="chart-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>مكتبة الرسوم البيانية غير محملة</p>
                        <p>يرجى التأكد من تحميل مكتبة Chart.js</p>
                    </div>
                `;
            }
            return;
        }

        // إنشاء رسم بياني جديد
        console.log('إنشاء رسم بياني جديد');
        currentChart = new Chart(ctx, {
            type: chartType,
            data: reportsData.chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true,
                        labels: {
                            font: {
                                family: 'Cairo, sans-serif'
                            }
                        }
                    },
                    tooltip: {
                        rtl: true,
                        titleFont: {
                            family: 'Cairo, sans-serif'
                        },
                        bodyFont: {
                            family: 'Cairo, sans-serif'
                        },
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y || context.parsed;
                                return context.dataset.label + ': ' + db.formatCurrency(value);
                            }
                        }
                    }
                },
                scales: chartType !== 'pie' && chartType !== 'doughnut' ? {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif'
                            },
                            callback: function(value) {
                                return db.formatCurrency(value);
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif'
                            }
                        }
                    }
                } : {}
            }
        });

        console.log('تم إنشاء الرسم البياني بنجاح');

    } catch (error) {
        console.error('خطأ في تحديث الرسم البياني:', error);

        // عرض رسالة خطأ بديلة
        const chartContainer = document.querySelector('.chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="chart-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>حدث خطأ في عرض الرسم البياني</p>
                    <p>${error.message || 'خطأ غير معروف'}</p>
                </div>
            `;
        }
    }
}

/**
 * تصدير التقرير
 */
function exportReport() {
    if (!reportsData) {
        app.showAlert('خطأ', 'لا يوجد تقرير لتصديره');
        return;
    }

    const reportType = document.getElementById('reportType').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;

    // جمع بيانات التقرير
    const exportData = {
        reportType: reportType,
        dateRange: {
            from: dateFrom,
            to: dateTo
        },
        generatedAt: new Date().toISOString(),
        data: reportsData,
        summary: {
            totalSales: document.getElementById('totalSalesAmount').textContent,
            totalPurchases: document.getElementById('totalPurchasesAmount').textContent,
            totalProfit: document.getElementById('totalProfit').textContent,
            profitMargin: document.getElementById('profitMargin').textContent
        }
    };

    // تصدير كملف JSON
    const filename = `report_${reportType}_${dateFrom}_${dateTo}.json`;
    Utils.downloadJSON(exportData, filename);

    app.showNotification('تم تصدير التقرير بنجاح', 'success');
}

/**
 * طباعة التقرير
 */
function printReport() {
    const reportContent = document.querySelector('.report-content');
    if (!reportContent) {
        app.showAlert('خطأ', 'لا يوجد تقرير للطباعة');
        return;
    }

    const printContent = `
        <div class="print-header">
            <h1>تكنوفلاش - نظام نقاط البيع</h1>
            <h2>${document.getElementById('tableTitle').textContent}</h2>
            <p>الفترة: من ${document.getElementById('dateFrom').value} إلى ${document.getElementById('dateTo').value}</p>
            <p>تاريخ الطباعة: ${db.formatDate(new Date().toISOString().split('T')[0])}</p>
        </div>

        <div class="print-stats">
            <div class="stat-row">
                <span>إجمالي المبيعات:</span>
                <span>${document.getElementById('totalSalesAmount').textContent}</span>
            </div>
            <div class="stat-row">
                <span>إجمالي المشتريات:</span>
                <span>${document.getElementById('totalPurchasesAmount').textContent}</span>
            </div>
            <div class="stat-row">
                <span>إجمالي الربح:</span>
                <span>${document.getElementById('totalProfit').textContent}</span>
            </div>
            <div class="stat-row">
                <span>هامش الربح:</span>
                <span>${document.getElementById('profitMargin').textContent}</span>
            </div>
        </div>

        ${document.querySelector('.table-section .card-body').innerHTML}
    `;

    Utils.printContent(printContent);
}

/**
 * تحديث العملة في واجهة التقارير
 */
function updateReportsCurrency(currency) {
    try {
        // تحديث العملة في العنصر الجذر
        document.documentElement.setAttribute('data-currency', currency);

        // تحديث جميع عناصر العملة في واجهة التقارير
        const currencyElements = document.querySelectorAll('.currency, [data-currency], .amount, .total, .revenue');
        currencyElements.forEach(element => {
            element.setAttribute('data-currency', currency);
            element.classList.add('currency');
        });

        // إعادة تحميل التقارير المعروضة حالياً
        const activeTab = document.querySelector('.tab-button.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('data-tab');
            if (tabId === 'sales-report' && typeof loadSalesReport === 'function') {
                loadSalesReport();
            } else if (tabId === 'purchases-report' && typeof loadPurchasesReport === 'function') {
                loadPurchasesReport();
            } else if (tabId === 'inventory-report' && typeof loadInventoryReport === 'function') {
                loadInventoryReport();
            }
        }

        console.log('تم تحديث العملة في التقارير:', currency);
    } catch (error) {
        console.error('خطأ في تحديث العملة في التقارير:', error);
    }
}

/**
 * تحديث نوع الأرقام في واجهة التقارير
 */
function updateReportsNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // تحديث جميع عناصر الأرقام
        const numberElements = document.querySelectorAll('.number, .amount, .total, .revenue, .quantity');
        numberElements.forEach(element => {
            element.classList.add('number');
        });

        // إعادة تحميل التقارير المعروضة حالياً
        const activeTab = document.querySelector('.tab-button.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('data-tab');
            if (tabId === 'sales-report' && typeof loadSalesReport === 'function') {
                loadSalesReport();
            } else if (tabId === 'purchases-report' && typeof loadPurchasesReport === 'function') {
                loadPurchasesReport();
            } else if (tabId === 'inventory-report' && typeof loadInventoryReport === 'function') {
                loadInventoryReport();
            }
        }

        console.log('تم تحديث نوع الأرقام في التقارير:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام في التقارير:', error);
    }
}
